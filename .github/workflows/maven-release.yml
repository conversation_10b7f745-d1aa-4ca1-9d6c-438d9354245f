name: Build, Test, and Release

on:
  push:
    branches:
      - master

jobs:
  build-release:
    name: Build, Test and Release
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          distribution: 'corretto'
          java-version: '17'

      - name: Set up custom Maven settings
        run: |
          mkdir -p ~/.m2
          cp ext/settings.xml ~/.m2/settings.xml

      - name: Initialize build
        run: echo "Initializing the build-release"

      - name: Build and Deploy
        run: |
          mvn clean -e
          mvn install deploy -e
