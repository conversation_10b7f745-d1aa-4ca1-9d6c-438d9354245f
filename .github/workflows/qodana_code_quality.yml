#-------------------------------------------------------------------------------#
#            Discover all capabilities of Qoda<PERSON> in our documentation           #
#             https://www.jetbrains.com/help/qodana/about-qodana.html           #
#-------------------------------------------------------------------------------#

name: Qodana
on:
  workflow_dispatch:
  pull_request:
  push:
    branches:
      - master

jobs:
  qodana:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      checks: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0
      - name: '<PERSON><PERSON><PERSON> Scan'
        uses: JetBrains/qodana-action@v2025.2
        env:
          QODANA_TOKEN: ${{ secrets.QODANA_TOKEN }}
        with:
          # In pr-mode: 'true' Qoda<PERSON> checks only changed files
          pr-mode: false
          use-caches: true
          post-pr-comment: true
          use-annotations: true
          # Upload Qodana results (SARIF, other artifacts, logs) as an artifact to the job
          upload-result: false
          # quick-fixes available in Ultimate and Ultimate Plus plans
          push-fixes: 'none'