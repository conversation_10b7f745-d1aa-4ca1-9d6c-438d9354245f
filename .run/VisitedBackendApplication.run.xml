<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="VisitedBackendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <option name="ACTIVE_PROFILES" value="dev" />
    <option name="ALTERNATIVE_JRE_PATH" value="corretto-21" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
    <module name="visited-backend" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.arrivinginhighheels.visited.backend.VisitedBackendApplication" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.arrivinginhighheels.visited.backend.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <extension name="software.aws.toolkits.jetbrains.core.execution.JavaAwsConnectionExtension">
      <option name="credential" />
      <option name="region" />
      <option name="useCurrentConnection" value="true" />
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>