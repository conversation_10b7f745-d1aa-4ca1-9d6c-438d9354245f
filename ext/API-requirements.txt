Visited Backend 2.0
Rest Responses

All services should be prefaced with the context root of v2

Custom Headers
--------------
Headers can be used to identify stats about the app and response accordingly
-H token: CURRENT_SESSION_TOKEN


Sign Up
--------
Request
POST /v2/signup
{
    "email" : "<EMAIL>",
    "platform" : {
        "OS" : iOS | Android,
        "Version": "2.1.1"
    }
}

SUCCESS 
201 {  "token" : "someTokenToBeStored" }

FAILURE
409 { "error" : "User exists" }
400 { "error" : "Invalid Email" }
401 { "error" : "Disabled Client.  Redirect to App Store" }


Sign In
-------
POST /v2/signin
Essentially same payload at signup
{
    "email" : "<EMAIL>",
    "platform" : {
        "OS" : iOS | Android,
        "Version": "2.1.1"
    }
}

SUCCESS
201 { 
      "token" : "someTokenToBeStored",
      "batch" : {
          "live" : [GeoAreaIds... ],
          "been" : [GeoAreaIds... ],
          "want" : [GeoAreaIds... ]
      },
      "rank" : {
          "total" : TotalNumberOfUsers
          "position" : YouPosition
      }
    }

FAILURE
401 { "error" : "Disabled Client.  Redirect to App Store" }
400 { "error" : "Invalid Email" }
404 { "error" : "User doesn't exist" }

Select
------
POST /v2/select
{
    "GeoAreaId" : "been|live|want|clear"
}

note: save datetime of when this reqest comes In
There is also some business logic on how batch can impact another.
Essentially a parent area should not have lower selection value than its child (though the opposite can be true)

1) Only one parent area and subdivision can be selected as "lived"
    -If a subdivision is selected, then its parent should be be categorized as lived
2) If a subdivision is selected as "BEEN" but its parent is either CLEAR or WANT, then the parent should also be "BEEN"
3) If a subdivision is selected as "WANT" and its parent is CLEAR, then it should also be selected as "WANT"

This is the code that was used in the Android version to calculate this that can be pushed to the Backend, 
but you probably would to adapt it to make it better fit the backend end.  
This version also just tells you that an area's selection has changed, not what it was changed to.

enum SelectionType {
        CLEAR(-1), WANT(0), BEEN(1), LIVED(2);

        private int value;

        SelectionType(int value) {
            this.value = value;
        }

        @Nullable
        public static SelectionType fromInt(int value) {
            switch (value) {
                case -1: return CLEAR;
                case 0:  return WANT;
                case 1:  return BEEN;
                case 2:  return LIVED;
                default: return null;
            }
        }

        public int toInt() {
            return value;
        }
    }

    public void select(GeographicArea area, SelectionType type) {
        // Don't do anything if the selection is not actually changed
        if (getSelection(area) == type) {
            return;
        }

        HashSet<GeographicArea> changeSet = new HashSet<>();
        changeSet.add(area);

        HashSet<GeographicArea> currentSet = setForArea(area);
        if (currentSet != null) {
            currentSet.remove(area);
        }

        GeographicArea parentArea = area.getParent();

        switch (type) {
            case WANT:
                mWantAreas.add(area);

                if (parentArea != null && getSelection(parentArea).toInt() < SelectionType.WANT.toInt()) {
                    mWantAreas.add(parentArea);
                    changeSet.add(parentArea);
                }
                break;

            case BEEN:
                mBeenAreas.add(area);

                if (parentArea != null && getSelection(parentArea).toInt() < SelectionType.LIVED.toInt()) {
                    HashSet parentSet = setForArea(parentArea);
                    if (parentSet != null) {
                        parentSet.remove(parentArea);
                    }
                    mBeenAreas.add(parentArea);
                    changeSet.add(parentArea);
                }
                break;

            case LIVED:
                // Cache what was previously selected as "lived" to
                // notify listeners after the selection has been updated
                for (GeographicArea oldArea : mLivedAreas) {
                    changeSet.add(oldArea);
                }

                mLivedAreas.clear();
                mLivedAreas.add(area);

                // If this a subdivision, change its parent to lived as well
                if (parentArea != null) {
                    HashSet parentSet = setForArea(parentArea);
                    if (parentSet != null) {
                        parentSet.remove(parentArea);
                    }

                    mLivedAreas.add(parentArea);

                    // If the parent area was already selected,
                    // there is no need to report that it has changed
                    if (changeSet.contains(parentArea)) {
                        changeSet.remove(parentArea);
                    } else {
                        changeSet.add(parentArea);
                    }
                }

                break;

            case CLEAR:
                if(area.getSubdivisions() != null) {
                    for (GeographicArea subdivision : area.getSubdivisions()) {
                        HashSet set = setForArea(subdivision);
                        if (set != null) {
                            set.remove(subdivision);
                        }
                        changeSet.add(subdivision);
                    }
                }
                else {
                    HashSet set = setForArea(area);
                    if (set != null) {
                        set.remove(area);
                    }
                    changeSet.add(area);
                }

                break;
        }

        /**
         Do one final pass to ensure that any subdivision
         doesn't have a higher selection value than its parent
         */
        if (area.hasSubdivisions()) {
            for (GeographicArea subdivision : area.getSubdivisions()) {
                SelectionType subdivisionSelection = getSelection(subdivision);
                if (subdivisionSelection.toInt() > type.toInt()) {
                    HashSet previousSet = setForArea(subdivision);
                    if (previousSet != null) {
                        previousSet.remove(subdivision);
                    }

                    HashSet newSelelectionSet = setForSelectionType(type);
                    if (newSelelectionSet != null) {
                        newSelelectionSet.add(subdivision);
                    }

                    changeSet.add(subdivision);
                }
            }
        }
    }


SUCCESS
201 {
    "rank" : {
          "total" : TotalNumberOfUsers
          "position" : YouPosition
      },
    "Diffs" : [
        {"ImpactedArea": "been|live|want|clear"},
        {"ImpactedArea": "been|live|want|clear"},
        ...
    ]  
}

FAILURE
400 { "error" : "invalid area" }