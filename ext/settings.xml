<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
  <servers>
    <server>
      <id>s3.artifacts.release</id>
      <username>********************</username>
      <password>gvwDDZfTu7SB7kg47oE6gy/84Hin2WEJcRiP0nAE</password>
      <filePermissions>AuthenticatedRead</filePermissions>
    </server>
    <server>
      <id>s3.artifacts.snapshot</id>
      <username>********************</username>
      <password>gvwDDZfTu7SB7kg47oE6gy/84Hin2WEJcRiP0nAE</password>
      <filePermissions>AuthenticatedRead</filePermissions>
    </server>
  </servers>
</settings>
