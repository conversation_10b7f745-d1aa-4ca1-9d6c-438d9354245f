# Visited - Java Backend

A new version of the "Visited" app, written in Java with a PostgreSQL database.

## Running the app

### Dependencies

* Java (JDK8)
* Maven

### Mac OS additional setup

Run the following commands:
* Download the latest version of Java 8.  Do not use Java 9.
* echo export "JAVA_HOME=\$(/usr/libexec/java_home)" >> ~/.bash_profile
* brew install maven
* brew install postgres

### Environment variables ###

Those are the necessary environment variables to run the app in `dev` mode:

* **VISITED_DATABASE_URL**=`****************************************` 
* **VISITED_DATABASE_USERNAME**=`visited` 
* **VISITED_DATABASE_PASSWORD**=`visitedpass`

And these are the `production` ones - they're just different to match <PERSON><PERSON>'s model:

* **JDBC_DATABASE_URL**=`jdbc:postgresql://<HOST>:5432/<DATABASE>` 
* **JDBC_DATABASE_USERNAME**=`<USERNAME>` 
* **JDBC_DATABASE_PASSWORD**=`<PASSWORD>`

### Commands to run the application in DEV mode (in-memory DB, with pre-created data)

First, run maven clean compile to fetch the Backend dependencies (optional)

``` $ mvn clean compile ```

And, finally, run the spring-boot's plugin run command with maven

``` $ mvn spring-boot:run ```

Alternatively, to test the production environment, the app can be run with Maven with the following command

``` $ mvn spring-boot:run -Drun.jvmArguments="-Dspring.profiles.active=production"```

The application will be running at the following URL: [http:localhost:8080/](http:localhost:8080/)

**IMPORTANT:** There will be no data present in the database the first time the system is run. There's a few SQL 
commands that can be run to create the minimum necessary data in the file `/src/test/resources/import.sql`.

## Packaging the app for production

### Dependencies
* Java (JDK8)
* Maven
* Installed database

### Necessary configurations

#### Papertrail ####

To change **Papertrail** settings, edit the `/src/main/resources/logback.xml` file.

#### Database ####

There will be no data present in the database the first time the system is run. There's a few SQL 
commands that can be run to create the minimum necessary data in the file `/src/test/resources/import.sql`.

### Packaging command with Maven

First, run Maven to package the app:

``` $ mvn clean package spring-boot:repackage ```

After this, a jar file will be created at `target/visited-backend-0.0.1.jar`

This jar can be executed with Java (JDK8) using:

``` $ java -jar -Dspring.profiles.active=production visited-backend-0.0.1.jar ```

## Creating a Docker image

### Dependencies
* Java (JDK8)
* Maven
* Docker

### Commands to create a Docker image

#### Configurations ####

To configure the group name in which the docker image will be created, alter the property `docker.image.prefix` in the `/pom.xml` file. 

#### Packaging/Running ####

First, run the Maven's package command together with the docker:build plugin command:

```mvn package docker:build```

To run the app using docker (using 'eltonsantos' as the `docker.image.prefix` property in this example):

```docker run -p 8080:8080 -t eltonsantos/visited-backend```
