CREATE TABLE public.top_places
(
  id bigint PRIMARY KEY,
  lived_geo_area_id bigint NOT NULL,
  been_geo_area_id bigint NOT NULL,
  number_of_users_been int NOT NULL,
  CONSTRAINT top_places_geoareas_lived_id_fk FOREIGN KEY (lived_geo_area_id) REFERENCES public.geoareas (id),
  CONSTRAINT top_places_geoareas_been_id_fk FOREIGN KEY (been_geo_area_id) REFERENCES public.geoareas (id)
);
CREATE UNIQUE INDEX top_places_lived_been_geo_area_ids_uindex ON public.top_places (lived_geo_area_id, been_geo_area_id);
CREATE INDEX top_places_lived_geo_area_id_index ON public.top_places (lived_geo_area_id);

CREATE SEQUENCE top_places_id_seq
  START WITH 1
  INCREMENT BY 1
  NO MINVALUE
  NO MAXVALUE
  CACHE 1;

/*

B<PERSON>IN;

truncate top_places;

insert into top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been)
select nextval('top_places_id_seq') as id,
       sLived.geo_area_id as lived_geo_area_id,
       sBeen.geo_area_id as been_geo_area_id,
       count(sBeen.user_id) as number_of_users_been
  from selections sLived
       inner join selections sBeen
               on sBeen.user_id = sLived.user_id
              and sBeen.selection_type = 'BEEN'
 where sLived.selection_type = 'LIVED'
 group by sLived.geo_area_id, sBeen.geo_area_id;

COMMIT;


-- Testing with Brazil
SELECT g.name, t.number_of_users_been
  FROM top_places t
       inner join geoareas g
               on t.been_geo_area_id = g.id
              and g.parent_id is null --get only countries
 WHERE lived_geo_area_id = 169
 ORDER BY number_of_users_been DESC;

-- Testing with USA
SELECT g.name, t.number_of_users_been
FROM top_places t
       inner join geoareas g
         on t.been_geo_area_id = g.id
        and g.parent_id is null --get only countries
WHERE lived_geo_area_id = 9
ORDER BY number_of_users_been DESC;

--Reverse testing: Which countries has been more to Brazil
SELECT g.name, t.number_of_users_been
FROM top_places t
       inner join geoareas g
         on t.lived_geo_area_id = g.id
              and g.parent_id is null --get only countries
WHERE been_geo_area_id = 169
ORDER BY number_of_users_been DESC;
*/