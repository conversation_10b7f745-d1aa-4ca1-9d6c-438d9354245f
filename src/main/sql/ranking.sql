begin;

truncate ranking;

insert into ranking
  select rank() over (order by a.num_areas_visited desc) as position,
    a.num_areas_visited,
         a.count as num_users
  from (
         SELECT DISTINCT num_areas_visited,
           count(*) as count
         FROM users
         WHERE num_areas_visited is not null
         group BY num_areas_visited
       ) as a;

commit;


select * from users where username = 'rachl<PERSON><EMAIL>';

select coalesce(sum(num_users), 0) + 1 as rank
from ranking
where num_areas_visited > 244;

explain
insert into statistics
select 'USERS_ACTIVE_LAST_30_DAYS', count(DISTINCT s.user_id) as int_value
  from sessions s
 where s.start_date_time >= current_date - INTERVAL '30' DAY ;

insert into statistics
select 'USERS_WITHOUT_WANT_DATA', count(u.id) as int_value
  from users u
 where not exists(
   select *
     from selections
    where user_id = u.id
      and selection_type = 'WANT'
 );


delete
  from casl_agreements
 where user_id in (
   SELECT id
   FROM users
   WHERE lower(username) LIKE '%@anonymous.com'
 );

select count(*)
FROM users
WHERE lower(username) LIKE '%@anonymous.com';

select id, username
FROM users
WHERE lower(username) LIKE '%@anonymous.com'
  and id < 4000;

delete
FROM users
WHERE lower(username) LIKE '%@anonymous.com'
      and id >= 10000
      and id < 11000;

delete from users
where id in (
  1807,
  1673,
  1683,
  1694,
  1700,
  1703,
  1727,
  1823,
  1728,
  1746,
  1766,
  1768,
  1785,
  1811,
  1814,
  1817,
  1822,
  1826,
  1831,
  1767,
  1836,
  1852,
  1857,
  1903,
  1887,
  1927,
  1959,
  1967,
  2000,
  2006,
  1873,
  1910,
  1919,
  1921,
  1942,
  1953,
  1965,
  1979,
  1980,
  1983,
  1997,
  2002,
  1839,
  1842,
  1862,
  1876,
  1883,
  1893,
  1907,
  1912,
  1931,
  1905,
  1989,
  1956,
  1962,
  1975,
  2007,
  1849,
  1896,
  1925,
  1928,
  2003,
  1993,
  1995,
  2009,
  2012,
  2016,
  2034,
  2041,
  2048,
  2051,
  2054,
  2059,
  2062,
  2065,
  2052,
  2068,
  2076,
  2080,
  2081,
  2089,
  2091,
  2037,
  2047,
  2072,
  2075,
  2094,
  2116,
  2120,
  2143,
  2146,
  2149,
  2189,
  2098,
  2103,
  2107,
  2112,
  2125,
  2187,
  2121,
  2203,
  2134,
  2135,
  2136,
  2172,
  2174,
  2181,
  2184,
  2190,
  2202,
  2204,
  2212,
  2216,
  2221,
  2222,
  2238,
  2248,
  2361,
  2384,
  2242,
  2243,
  2335,
  2251,
  2257,
  2271,
  2370,
  2285,
  2294,
  2316,
  2331,
  2352,
  2291,
  2303,
  2363,
  2368,
  2315,
  2348,
  2351,
  2276,
  2279,
  2282,
  2288,
  2296,
  2298,
  2301,
  2338,
  2344,
  2365,
  2371,
  2377,
  2390,
  2392,
  2405,
  2407,
  2422,
  2437,
  2475,
  2492,
  2515,
  2522,
  2553,
  2413,
  2417,
  2423,
  2431,
  2503,
  2518,
  2524,
  2527,
  2537,
  2549,
  2557,
  2561,
  2420,
  2454,
  2455,
  2468,
  2472,
  2478,
  2496,
  2542,
  2545,
  2556,
  2563,
  2569,
  2427,
  2438,
  2442,
  2448,
  2483,
  2491,
  2500,
  2507,
  2509,
  2534,
  2577,
  2578,
  2581,
  2740,
  2745,
  2760,
  2587,
  2588,
  2597,
  2598,
  2621,
  2623,
  2617,
  2619,
  2649,
  2667,
  2692,
  2727,
  2728,
  2642,
  2665,
  2705,
  2718,
  2734,
  2591,
  2592,
  2593,
  2596,
  2644,
  2687,
  2691,
  2693,
  2701,
  2710,
  2713,
  2714,
  2715,
  2731,
  2738,
  2761,
  2624,
  2647,
  2661,
  2662,
  2688,
  2695,
  2768,
  2769,
  2771,
  2773,
  2774,
  2782,
  2788,
  2822,
  2824,
  2868,
  2858,
  2879,
  2881,
  2894,
  2902,
  2911,
  2945,
  2947,
  2951,
  2955,
  2960,
  2990,
  2786,
  2871,
  2880,
  2882,
  2889,
  2906,
  2915,
  2918,
  2922,
  2924,
  2929,
  2935,
  2938,
  2977,
  2980,
  3011,
  2793,
  2795,
  2799,
  2801,
  2803,
  2804,
  2812,
  2932,
  2835,
  2837,
  2841,
  2849,
  2850,
  2859,
  2862,
  2867,
  2876,
  2890,
  2909,
  2921,
  2937

);

delete
from users_authorities
where user_id in (
  2807,
  24752,
  47615,
  165677,
  106094
);

delete
from selection_logs
where user_id in (
  2807,
  24752,
  47615,
  165677,
  106094
);

delete
from sessions
where user_id in (
  2807,
  24752,
  47615,
  165677,
  106094
);

delete
from selections
where user_id in (
  2807,
  24752,
  47615,
  165677,
  106094
);

delete
from users
where id in (
  2807,
  24752,
  47615,
  165677,
  106094
);

update users
  set enabled = false
 where username in (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
 );

ALTER TABLE public.users ADD unsubscribed BOOLEAN DEFAULT FALSE NULL;

update users set unsubscribed = false;