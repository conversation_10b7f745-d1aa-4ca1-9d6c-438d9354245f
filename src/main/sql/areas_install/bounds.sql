update geoareas set bounding_min_long = '-69.16361999511719', bounding_min_lat = '17.52055549621582', bounding_max_long = '-62.93861389160156', bounding_max_lat = '12.02055549621582' where iso_key = 'AN';
update geoareas set bounding_min_long = '-78.339502', bounding_min_lat = '18.522229', bounding_max_long = '-76.210795', bounding_max_lat = '17.71494' where iso_key = 'JM';
update geoareas set bounding_min_long = '-59.64669', bounding_min_lat = '13.317689', bounding_max_long = '-59.427633', bounding_max_lat = '13.0622' where iso_key = 'BB';
update geoareas set bounding_min_long = '-69.158874', bounding_min_lat = '12.380278', bounding_max_long = '-68.751095', bounding_max_lat = '12.045467' where iso_key = 'CW';
update geoareas set bounding_min_long = '-87.670173', bounding_min_lat = '15.008051', bounding_max_long = '-83.157528', bounding_max_lat = '10.735366' where iso_key = 'NI';
update geoareas set bounding_min_long = '-89.237493', bounding_min_lat = '18.482309', bounding_max_long = '-87.788615', bounding_max_lat = '15.888695' where iso_key = 'BZ';
update geoareas set bounding_min_long = '145.152102', bounding_min_lat = '18.806785', bounding_max_long = '145.835471', bounding_max_lat = '14.111335' where iso_key = 'MP';
update geoareas set bounding_min_long = '-61.073114', bounding_min_lat = '14.093352', bounding_max_long = '-60.886769', bounding_max_lat = '13.717561' where iso_key = 'LC';
update geoareas set bounding_min_long = '-178.194518', bounding_min_lat = '71.407683', bounding_max_long = '179.779962', bounding_max_lat = '18.963907' where iso_key = 'US';
update geoareas set bounding_min_long = '-109.058934', bounding_min_lat = '41.003906', bounding_max_long = '-102.042974', bounding_max_lat = '36.994786' where iso_key = 'US-CO';
update geoareas set bounding_min_long = '-82.621743', bounding_min_lat = '40.636951', bounding_max_long = '-77.719881', bounding_max_lat = '37.20291' where iso_key = 'US-WV';
update geoareas set bounding_min_long = '-106.643603', bounding_min_lat = '36.501861', bounding_max_long = '-93.526331', bounding_max_lat = '25.887551' where iso_key = 'US-TX';
update geoareas set bounding_min_long = '-80.518598', bounding_min_lat = '42.269079', bounding_max_long = '-74.69661', bounding_max_lat = '39.722302' where iso_key = 'US-PA';
update geoareas set bounding_min_long = '-88.471115', bounding_min_lat = '35.00118', bounding_max_long = '-84.889196', bounding_max_lat = '30.247195' where iso_key = 'US-AL';
update geoareas set bounding_min_long = '-124.410798', bounding_min_lat = '42.011663', bounding_max_long = '-114.136058', bounding_max_lat = '32.536556' where iso_key = 'US-CA';
update geoareas set bounding_min_long = '-83.673316', bounding_min_lat = '39.464886', bounding_max_long = '-75.244304', bounding_max_lat = '36.5402' where iso_key = 'US-VA';
update geoareas set bounding_min_long = '-71.859555', bounding_min_lat = '42.01714', bounding_max_long = '-71.120168', bounding_max_lat = '41.321569' where iso_key = 'US-RI';
update geoareas set bounding_min_long = '-90.415429', bounding_min_lat = '48.173221', bounding_max_long = '-82.413619', bounding_max_lat = '41.694001' where iso_key = 'US-MI';
update geoareas set bounding_min_long = '-97.228743', bounding_min_lat = '49.383625', bounding_max_long = '-89.615796', bounding_max_lat = '43.501391' where iso_key = 'US-MN';
update geoareas set bounding_min_long = '-77.117418', bounding_min_lat = '38.993869', bounding_max_long = '-76.909294', bounding_max_lat = '38.791222' where iso_key = 'US-DC';
update geoareas set bounding_min_long = '-102.053927', bounding_min_lat = '40.001626', bounding_max_long = '-94.610765', bounding_max_lat = '36.994786' where iso_key = 'US-KS';
update geoareas set bounding_min_long = '-90.311367', bounding_min_lat = '36.677123', bounding_max_long = '-81.679709', bounding_max_lat = '34.984749' where iso_key = 'US-TN';
update geoareas set bounding_min_long = '-104.053011', bounding_min_lat = '43.002989', bounding_max_long = '-95.306337', bounding_max_lat = '40.001626' where iso_key = 'US-NE';
update geoareas set bounding_min_long = '-103.001438', bounding_min_lat = '37.000263', bounding_max_long = '-94.430026', bounding_max_lat = '33.637421' where iso_key = 'US-OK';
update geoareas set bounding_min_long = '-84.817996', bounding_min_lat = '41.978802', bounding_max_long = '-80.518598', bounding_max_lat = '38.424267' where iso_key = 'US-OH';
update geoareas set bounding_min_long = '-91.636787', bounding_min_lat = '34.995703', bounding_max_long = '-88.098683', bounding_max_lat = '30.181472' where iso_key = 'US-MS';
update geoareas set bounding_min_long = '-178.123152', bounding_min_lat = '71.351633', bounding_max_long = '173.304726', bounding_max_lat = '51.61274' where iso_key = 'US-AK';
update geoareas set bounding_min_long = '-104.058488', bounding_min_lat = '45.944106', bounding_max_long = '-96.434587', bounding_max_lat = '42.488157' where iso_key = 'US-SD';
update geoareas set bounding_min_long = '-73.508114', bounding_min_lat = '42.887974', bounding_max_long = '-69.937149', bounding_max_lat = '41.496831' where iso_key = 'US-MA';
update geoareas set bounding_min_long = '-75.786521', bounding_min_lat = '39.831841', bounding_max_long = '-75.047134', bounding_max_lat = '38.451652' where iso_key = 'US-DE';
update geoareas set bounding_min_long = '-159.764448', bounding_min_lat = '22.228955', bounding_max_long = '-154.807817', bounding_max_lat = '18.948267' where iso_key = 'US-HI';
update geoareas set bounding_min_long = '-96.631756', bounding_min_lat = '43.501391', bounding_max_long = '-90.141582', bounding_max_lat = '40.379535' where iso_key = 'US-IA';
update geoareas set bounding_min_long = '-91.50534', bounding_min_lat = '42.510065', bounding_max_long = '-87.49622', bounding_max_lat = '36.983832' where iso_key = 'US-IL';
update geoareas set bounding_min_long = '-114.815198', bounding_min_lat = '37.00574', bounding_max_long = '-109.042503', bounding_max_lat = '31.331629' where iso_key = 'US-AZ';
update geoareas set bounding_min_long = '-75.561967', bounding_min_lat = '41.359907', bounding_max_long = '-73.902454', bounding_max_lat = '38.993869' where iso_key = 'US-NJ';
update geoareas set bounding_min_long = '-85.606675', bounding_min_lat = '35.00118', bounding_max_long = '-80.885553', bounding_max_lat = '30.356734' where iso_key = 'US-GA';
update geoareas set bounding_min_long = '-111.05254', bounding_min_lat = '45.002073', bounding_max_long = '-104.053011', bounding_max_lat = '40.998429' where iso_key = 'US-WY';
update geoareas set bounding_min_long = '-95.7664', bounding_min_lat = '40.615043', bounding_max_long = '-89.133825', bounding_max_lat = '35.997983' where iso_key = 'US-MO';
update geoareas set bounding_min_long = '-71.08183', bounding_min_lat = '47.461219', bounding_max_long = '-66.979601', bounding_max_lat = '43.057759' where iso_key = 'US-ME';
update geoareas set bounding_min_long = '-104.047534', bounding_min_lat = '49.000239', bounding_max_long = '-96.560556', bounding_max_lat = '45.933153' where iso_key = 'US-ND';
update geoareas set bounding_min_long = '-116.04751', bounding_min_lat = '49.000239', bounding_max_long = '-104.042057', bounding_max_lat = '44.394132' where iso_key = 'US-MT';
update geoareas set bounding_min_long = '-120.001861', bounding_min_lat = '42.000709', bounding_max_long = '-114.04295', bounding_max_lat = '35.00118' where iso_key = 'US-NV';
update geoareas set bounding_min_long = '-124.706553', bounding_min_lat = '49.000239', bounding_max_long = '-116.918344', bounding_max_lat = '45.549767' where iso_key = 'US-WA';
update geoareas set bounding_min_long = '-109.04798', bounding_min_lat = '37.000263', bounding_max_long = '-103.001438', bounding_max_lat = '31.331629' where iso_key = 'US-NM';
update geoareas set bounding_min_long = '-92.885529', bounding_min_lat = '46.95734', bounding_max_long = '-87.03068', bounding_max_lat = '42.493634' where iso_key = 'US-WI';
update geoareas set bounding_min_long = '-84.319594', bounding_min_lat = '36.589492', bounding_max_long = '-75.715321', bounding_max_lat = '33.845545' where iso_key = 'US-NC';
update geoareas set bounding_min_long = '-114.048427', bounding_min_lat = '42.000709', bounding_max_long = '-109.042503', bounding_max_lat = '37.000263' where iso_key = 'US-UT';
update geoareas set bounding_min_long = '-72.544173', bounding_min_lat = '45.303304', bounding_max_long = '-70.703921', bounding_max_lat = '42.696281' where iso_key = 'US-NH';
update geoareas set bounding_min_long = '-124.553198', bounding_min_lat = '46.261769', bounding_max_long = '-116.463758', bounding_max_lat = '41.989755' where iso_key = 'US-OR';
update geoareas set bounding_min_long = '-94.616242', bounding_min_lat = '36.501861', bounding_max_long = '-89.730812', bounding_max_lat = '33.002096' where iso_key = 'US-AR';
update geoareas set bounding_min_long = '-87.633143', bounding_min_lat = '31.003013', bounding_max_long = '-80.03115', bounding_max_lat = '25.120779' where iso_key = 'US-FL';
update geoareas set bounding_min_long = '-117.241483', bounding_min_lat = '49.000239', bounding_max_long = '-111.047063', bounding_max_lat = '41.995232' where iso_key = 'US-ID';
update geoareas set bounding_min_long = '-89.418626', bounding_min_lat = '39.103408', bounding_max_long = '-81.969987', bounding_max_lat = '36.496384' where iso_key = 'US-KY';
update geoareas set bounding_min_long = '-73.436914', bounding_min_lat = '45.013027', bounding_max_long = '-71.4926', bounding_max_lat = '42.729142' where iso_key = 'US-VT';
update geoareas set bounding_min_long = '-79.488933', bounding_min_lat = '39.722302', bounding_max_long = '-75.047134', bounding_max_lat = '37.909435' where iso_key = 'US-MD';
update geoareas set bounding_min_long = '-83.339222', bounding_min_lat = '35.198349', bounding_max_long = '-78.541422', bounding_max_lat = '32.032678' where iso_key = 'US-SC';
update geoareas set bounding_min_long = '-73.727192', bounding_min_lat = '42.050002', bounding_max_long = '-71.799309', bounding_max_lat = '40.987475' where iso_key = 'US-CT';
update geoareas set bounding_min_long = '-88.060345', bounding_min_lat = '41.759724', bounding_max_long = '-84.801565', bounding_max_lat = '37.788942' where iso_key = 'US-IN';
update geoareas set bounding_min_long = '-79.76278', bounding_min_lat = '45.018503', bounding_max_long = '-72.100541', bounding_max_lat = '40.543843' where iso_key = 'US-NY';
update geoareas set bounding_min_long = '-94.041164', bounding_min_lat = '33.018527', bounding_max_long = '-89.002379', bounding_max_lat = '29.009407' where iso_key = 'US-LA';
update geoareas set bounding_min_long = '-63.160015', bounding_min_lat = '18.269738', bounding_max_long = '-62.979561', bounding_max_lat = '18.171398' where iso_key = 'AI';
update geoareas set bounding_min_long = '-67.937062', bounding_min_lat = '18.522151', bounding_max_long = '-65.294872', bounding_max_lat = '17.947251' where iso_key = 'PR';
update geoareas set bounding_min_long = '-70.066131', bounding_min_lat = '12.614114', bounding_max_long = '-69.895676', bounding_max_lat = '12.423015' where iso_key = 'AW';
update geoareas set bounding_min_long = '-81.419082', bounding_min_lat = '19.765745', bounding_max_long = '-79.742311', bounding_max_lat = '19.271899' where iso_key = 'KY';
update geoareas set bounding_min_long = '-63.12472', bounding_min_lat = '18.068942', bounding_max_long = '-63.011168', bounding_max_lat = '18.019185' where iso_key = 'SX';
update geoareas set bounding_min_long = '-61.906112', bounding_min_lat = '11.325407', bounding_max_long = '-60.525499', bounding_max_lat = '10.064657' where iso_key = 'TT';
update geoareas set bounding_min_long = '-64.695089', bounding_min_lat = '18.752706', bounding_max_long = '-64.273591', bounding_max_lat = '18.399136' where iso_key = 'VG';
update geoareas set bounding_min_long = '144.64929', bounding_min_lat = '13.622373', bounding_max_long = '144.940848', bounding_max_lat = '13.257537' where iso_key = 'GU';
update geoareas set bounding_min_long = '-56.386889', bounding_min_lat = '47.098986', bounding_max_long = '-56.137343', bounding_max_lat = '46.752857' where iso_key = 'PM';
update geoareas set bounding_min_long = '-61.21972', bounding_min_lat = '14.875268', bounding_max_long = '-60.826282', bounding_max_lat = '14.426252' where iso_key = 'MQ';
update geoareas set bounding_min_long = '-78.985664', bounding_min_lat = '26.940087', bounding_max_long = '-72.747285', bounding_max_lat = '20.937379' where iso_key = 'BS';
update geoareas set bounding_min_long = '-118.401346', bounding_min_lat = '32.715342', bounding_max_long = '-86.696305', bounding_max_lat = '14.545392' where iso_key = 'MX';
update geoareas set bounding_min_long = '-61.481151', bounding_min_lat = '15.633103', bounding_max_long = '-61.251062', bounding_max_lat = '15.227288' where iso_key = 'DM';
update geoareas set bounding_min_long = '-85.908032', bounding_min_lat = '11.189447', bounding_max_long = '-82.563585', bounding_max_lat = '8.07067' where iso_key = 'CR';
update geoareas set bounding_min_long = '-62.840474', bounding_min_lat = '17.402607', bounding_max_long = '-62.532224', bounding_max_lat = '17.10061' where iso_key = 'KN';
update geoareas set bounding_min_long = '-63.123019', bounding_min_lat = '18.115355', bounding_max_long = '-63.00943', bounding_max_lat = '18.068942' where iso_key = 'MF';
update geoareas set bounding_min_long = '-62.22307', bounding_min_lat = '16.809569', bounding_max_long = '-62.148449', bounding_max_lat = '16.681204' where iso_key = 'MS';
update geoareas set bounding_min_long = '-90.10589', bounding_min_lat = '14.431083', bounding_max_long = '-87.715312', bounding_max_lat = '13.164003' where iso_key = 'SV';
update geoareas set bounding_min_long = '-74.47811', bounding_min_lat = '20.093632', bounding_max_long = '-71.645311', bounding_max_lat = '18.039158' where iso_key = 'HT';
update geoareas set bounding_min_long = '-61.887121', bounding_min_lat = '17.714061', bounding_max_long = '-61.686048', bounding_max_lat = '16.997154' where iso_key = 'AG';
update geoareas set bounding_min_long = '-64.862857', bounding_min_lat = '32.386912', bounding_max_long = '-64.668321', bounding_max_lat = '32.259633' where iso_key = 'BM';
update geoareas set bounding_min_long = '-141.002137', bounding_min_lat = '83.116116', bounding_max_long = '-52.653654', bounding_max_lat = '41.674873' where iso_key = 'CA';
update geoareas set bounding_min_long = '-102.0016486358272', bounding_min_lat = '60.00176841733511', bounding_max_long = '-88.94849506171704', bounding_max_lat = '48.99176433522747' where iso_key = 'CA-MB';
update geoareas set bounding_min_long = '-120.6818513747876', bounding_min_lat = '83.11611378149931', bounding_max_long = '-61.20721348589291', bounding_max_lat = '51.94488944788018' where iso_key = 'CA-NU';
update geoareas set bounding_min_long = '-110.0000254057848', bounding_min_lat = '60.0010872649654', bounding_max_long = '-101.3672705598698', bounding_max_lat = '48.99308269465275' where iso_key = 'CA-SK';
update geoareas set bounding_min_long = '-69.05366031841427', bounding_min_lat = '48.0669681573711', bounding_max_long = '-63.83191329304856', bounding_max_lat = '44.62890650278197' where iso_key = 'CA-NB';
update geoareas set bounding_min_long = '-67.7614703277149', bounding_min_lat = '60.30597985472115', bounding_max_long = '-52.65365358928807', bounding_max_lat = '46.62828646186474' where iso_key = 'CA-NL';
update geoareas set bounding_min_long = '-141.0021371841246', bounding_min_lat = '69.65078611940038', bounding_max_long = '-123.8191567923179', bounding_max_lat = '60.0010872649654' where iso_key = 'CA-YT';
update geoareas set bounding_min_long = '-120.0010176085814', bounding_min_lat = '60.0010872649654', bounding_max_long = '-109.9994321440434', bounding_max_lat = '48.99308269465275' where iso_key = 'CA-AB';
update geoareas set bounding_min_long = '-139.0565188237724', bounding_min_lat = '60.00158164974989', bounding_max_long = '-114.0506023425286', bounding_max_lat = '48.32278481752041' where iso_key = 'CA-BC';
update geoareas set bounding_min_long = '-95.16036297446225', bounding_min_lat = '56.85130687137327', bounding_max_long = '-74.34044640876104', bounding_max_lat = '41.73583984374999' where iso_key = 'CA-ON';
update geoareas set bounding_min_long = '-79.7139366039778', bounding_min_lat = '62.5725143650107', bounding_max_long = '-57.10012836828267', bounding_max_lat = '45.00386989599195' where iso_key = 'CA-QC';
update geoareas set bounding_min_long = '-136.4454365708471', bounding_min_lat = '78.75782626172816', bounding_max_long = '-101.9866358178717', bounding_max_lat = '60.0010872649654' where iso_key = 'CA-NT';
update geoareas set bounding_min_long = '-66.3241069916339', bounding_min_lat = '47.00970981625864', bounding_max_long = '-59.72712431810393', bounding_max_lat = '43.51806783735999' where iso_key = 'CA-NS';
update geoareas set bounding_min_long = '-64.40314744569886', bounding_min_lat = '47.06157627331535', bounding_max_long = '-62.02372840961863', bounding_max_lat = '45.9668875108532' where iso_key = 'CA-PE';
update geoareas set bounding_min_long = '-61.782192', bounding_min_lat = '12.237031', bounding_max_long = '-61.607035', bounding_max_lat = '12.00844' where iso_key = 'GD';
update geoareas set bounding_min_long = '-72.00038', bounding_min_lat = '19.913979', bounding_max_long = '-68.339156', bounding_max_lat = '17.635616' where iso_key = 'DO';
update geoareas set bounding_min_long = '-89.362602', bounding_min_lat = '16.513954', bounding_max_long = '-83.157528', bounding_max_lat = '12.97926' where iso_key = 'HN';
update geoareas set bounding_min_long = '-72.342375', bounding_min_lat = '21.95189', bounding_max_long = '-71.636914', bounding_max_lat = '21.751696' where iso_key = 'TC';
update geoareas set bounding_min_long = '-61.353536', bounding_min_lat = '13.35872', bounding_max_long = '-61.124015', bounding_max_lat = '12.694703' where iso_key = 'VC';
update geoareas set bounding_min_long = '-84.88719', bounding_min_lat = '23.190445', bounding_max_long = '-74.136813', bounding_max_lat = '19.855481' where iso_key = 'CU';
update geoareas set bounding_min_long = '-65.023622', bounding_min_lat = '18.385209', bounding_max_long = '-64.580445', bounding_max_lat = '17.701711' where iso_key = 'VI';
update geoareas set bounding_min_long = '-92.23514', bounding_min_lat = '17.816432', bounding_max_long = '-88.22833', bounding_max_lat = '13.736526' where iso_key = 'GT';
update geoareas set bounding_min_long = '-62.875433', bounding_min_lat = '17.922266', bounding_max_long = '-62.799727', bounding_max_lat = '17.875188' where iso_key = 'BL';
update geoareas set bounding_min_long = '-83.02733', bounding_min_lat = '9.597864', bounding_max_long = '-77.196006', bounding_max_lat = '7.220076' where iso_key = 'PA';
update geoareas set bounding_min_long = '-63.16651694148267', bounding_min_lat = '18.13444035064876', bounding_max_long = '-61.00787456922184', bounding_max_lat = '15.84479213150664' where iso_key = 'GP';
update geoareas set bounding_min_long = '35.764463', bounding_min_lat = '37.297259', bounding_max_long = '42.359048', bounding_max_lat = '32.317304' where iso_key = 'SY';
update geoareas set bounding_min_long = '123.679821', bounding_min_lat = '45.509522', bounding_max_long = '145.83299', bounding_max_lat = '24.266064' where iso_key = 'JP';
update geoareas set bounding_min_long = '126.007534', bounding_min_lat = '38.623457', bounding_max_long = '130.934262', bounding_max_lat = '33.201514' where iso_key = 'KR';
update geoareas set bounding_min_long = '73.607321', bounding_min_lat = '53.555594', bounding_max_long = '134.752323', bounding_max_lat = '18.218242' where iso_key = 'CN';
update geoareas set bounding_min_long = '88.023392', bounding_min_lat = '26.571531', bounding_max_long = '92.63169', bounding_max_lat = '20.790437' where iso_key = 'BD';
update geoareas set bounding_min_long = '102.319729', bounding_min_lat = '14.705098', bounding_max_long = '107.605453', bounding_max_lat = '10.411251' where iso_key = 'KH';
update geoareas set bounding_min_long = '46.609228', bounding_min_lat = '55.389617', bounding_max_long = '87.322815', bounding_max_lat = '40.608633' where iso_key = 'KZ';
update geoareas set bounding_min_long = '95.206623', bounding_min_lat = '5.90703', bounding_max_long = '140.976129', bounding_max_lat = '-10.90967' where iso_key = 'ID';
update geoareas set bounding_min_long = '38.77353', bounding_min_lat = '37.371879', bounding_max_long = '48.546526', bounding_max_lat = '29.063653' where iso_key = 'IQ';
update geoareas set bounding_min_long = '87.743203', bounding_min_lat = '52.117284', bounding_max_long = '119.897829', bounding_max_lat = '41.595523' where iso_key = 'MN';
update geoareas set bounding_min_long = '97.373933', bounding_min_lat = '20.424413', bounding_max_long = '105.641025', bounding_max_lat = '5.636762' where iso_key = 'TH';
update geoareas set bounding_min_long = '46.531455', bounding_min_lat = '30.09731', bounding_max_long = '48.442449', bounding_max_lat = '28.533168' where iso_key = 'KW';
update geoareas set bounding_min_long = '131.134921', bounding_min_lat = '7.712088', bounding_max_long = '134.659616', bounding_max_lat = '3.021883' where iso_key = 'PW';
update geoareas set bounding_min_long = '51.568357', bounding_min_lat = '26.068177', bounding_max_long = '56.387959', bounding_max_lat = '22.621462' where iso_key = 'AE';
update geoareas set bounding_min_long = '55.975684', bounding_min_lat = '45.555359', bounding_max_long = '73.136963', bounding_max_lat = '37.172227' where iso_key = 'UZ';
update geoareas set bounding_min_long = '118.287282', bounding_min_lat = '25.276907', bounding_max_long = '121.929023', bounding_max_lat = '21.925019' where iso_key = 'TW';
update geoareas set bounding_min_long = '42.549062', bounding_min_lat = '18.996153', bounding_max_long = '54.511123', bounding_max_lat = '12.31899' where iso_key = 'YE';
update geoareas set bounding_min_long = '67.349616', bounding_min_lat = '41.035092', bounding_max_long = '75.118805', bounding_max_lat = '36.684014' where iso_key = 'TJ';
update geoareas set bounding_min_long = '88.738801', bounding_min_lat = '28.311192', bounding_max_long = '92.083455', bounding_max_lat = '26.701549' where iso_key = 'BT';
update geoareas set bounding_min_long = '124.036388', bounding_min_lat = '-8.139916', bounding_max_long = '127.296137', bounding_max_lat = '-9.511926' where iso_key = 'TL';
update geoareas set bounding_min_long = '51.977634', bounding_min_lat = '26.35635', bounding_max_long = '59.837516', bounding_max_lat = '16.64839' where iso_key = 'OM';
update geoareas set bounding_min_long = '102.127441', bounding_min_lat = '23.34519', bounding_max_long = '109.444927', bounding_max_lat = '8.583249' where iso_key = 'VN';
update geoareas set bounding_min_long = '116.969533', bounding_min_lat = '20.84126', bounding_max_long = '126.593338', bounding_max_lat = '5.060208' where iso_key = 'PH';
update geoareas set bounding_min_long = '124.348617', bounding_min_lat = '42.998151', bounding_max_long = '130.687352', bounding_max_lat = '37.719042' where iso_key = 'KP';
update geoareas set bounding_min_long = '44.023184', bounding_min_lat = '39.768529', bounding_max_long = '63.305211', bounding_max_lat = '25.102112' where iso_key = 'IR';
update geoareas set bounding_min_long = '25.668956', bounding_min_lat = '42.093245', bounding_max_long = '44.817141', bounding_max_lat = '35.831457' where iso_key = 'TR';
update geoareas set bounding_min_long = '60.485797', bounding_min_lat = '38.456413', bounding_max_long = '74.891326', bounding_max_lat = '29.391953' where iso_key = 'AF';
update geoareas set bounding_min_long = '34.24528', bounding_min_lat = '33.431758', bounding_max_long = '35.913498', bounding_max_lat = '29.477322' where iso_key = 'IL';
update geoareas set bounding_min_long = '114.063818', bounding_min_lat = '5.022381', bounding_max_long = '115.326739', bounding_max_lat = '4.023992' where iso_key = 'BN';
update geoareas set bounding_min_long = '79.707828', bounding_min_lat = '9.812683', bounding_max_long = '81.876894', bounding_max_lat = '5.949353' where iso_key = 'LK';
update geoareas set bounding_min_long = '34.616213', bounding_min_lat = '32.124499', bounding_max_long = '55.641028', bounding_max_lat = '16.371792' where iso_key = 'SA';
update geoareas set bounding_min_long = '35.108586', bounding_min_lat = '34.678685', bounding_max_long = '36.584982', bounding_max_lat = '33.075708' where iso_key = 'LB';
update geoareas set bounding_min_long = '99.646249', bounding_min_lat = '7.351645', bounding_max_long = '119.266343', bounding_max_lat = '0.861963' where iso_key = 'MY';
update geoareas set bounding_min_long = '92.179573', bounding_min_lat = '28.517045', bounding_max_long = '101.147294', bounding_max_lat = '9.875367' where iso_key = 'MM';
update geoareas set bounding_min_long = '34.21666', bounding_min_lat = '32.534', bounding_max_long = '35.572', bounding_max_lat = '31.216541' where iso_key = 'PS';
update geoareas set bounding_min_long = '80.051631', bounding_min_lat = '30.387525', bounding_max_long = '88.161575', bounding_max_lat = '26.360303' where iso_key = 'NP';
update geoareas set bounding_min_long = '50.452456', bounding_min_lat = '26.246434', bounding_max_long = '50.61751', bounding_max_lat = '25.806797' where iso_key = 'BH';
update geoareas set bounding_min_long = '52.493881', bounding_min_lat = '42.778474', bounding_max_long = '66.629349', bounding_max_lat = '35.170773' where iso_key = 'TM';
update geoareas set bounding_min_long = '69.229087', bounding_min_lat = '43.240358', bounding_max_long = '80.246193', bounding_max_lat = '39.20753' where iso_key = 'KG';
update geoareas set bounding_min_long = '73.382064', bounding_min_lat = '4.247648', bounding_max_long = '73.52836', bounding_max_lat = '3.229416' where iso_key = 'MV';
update geoareas set bounding_min_long = '100.114954', bounding_min_lat = '22.495268', bounding_max_long = '107.65315', bounding_max_lat = '13.921166' where iso_key = 'LA';
update geoareas set bounding_min_long = '50.754556', bounding_min_lat = '26.153288', bounding_max_long = '51.608871', bounding_max_lat = '24.564651' where iso_key = 'QA';
update geoareas set bounding_min_long = '113.838871', bounding_min_lat = '22.565005', bounding_max_long = '114.335275', bounding_max_lat = '22.195183' where iso_key = 'HK';
update geoareas set bounding_min_long = '138.061888', bounding_min_lat = '9.593291', bounding_max_long = '162.993493', bounding_max_lat = '5.277249' where iso_key = 'FM';
update geoareas set bounding_min_long = '103.650139', bounding_min_lat = '1.447095', bounding_max_long = '103.996371', bounding_max_lat = '1.265401' where iso_key = 'SG';
update geoareas set bounding_min_long = '34.950818', bounding_min_lat = '33.372201', bounding_max_long = '39.292775', bounding_max_lat = '29.190467' where iso_key = 'JO';
update geoareas set bounding_min_long = '113.478893', bounding_min_lat = '22.245929', bounding_max_long = '113.548139', bounding_max_lat = '22.195545' where iso_key = 'MO';
update geoareas set bounding_min_long = '68.16507', bounding_min_lat = '35.495922', bounding_max_long = '97.343547', bounding_max_lat = '6.748684' where iso_key = 'IN';
update geoareas set bounding_min_long = '60.843398', bounding_min_lat = '37.036654', bounding_max_long = '77.04861', bounding_max_lat = '23.753382' where iso_key = 'PK';
update geoareas set bounding_min_long = '-54.616273', bounding_min_lat = '5.782231', bounding_max_long = '-51.652552', bounding_max_lat = '2.121059' where iso_key = 'GF';
update geoareas set bounding_min_long = '-81.336632', bounding_min_lat = '-0.041754', bounding_max_long = '-68.685234', bounding_max_lat = '-18.345598' where iso_key = 'PE';
update geoareas set bounding_min_long = '-62.651002', bounding_min_lat = '-19.286213', bounding_max_long = '-54.241826', bounding_max_lat = '-27.553822' where iso_key = 'PY';
update geoareas set bounding_min_long = '-73.576253', bounding_min_lat = '-21.802545', bounding_max_long = '-53.668527', bounding_max_lat = '-55.032144' where iso_key = 'AR';
update geoareas set bounding_min_long = '-69.645692', bounding_min_lat = '-9.710415', bounding_max_long = '-57.495658', bounding_max_lat = '-22.891677' where iso_key = 'BO';
update geoareas set bounding_min_long = '-58.438133', bounding_min_lat = '-30.101056', bounding_max_long = '-53.125563', bounding_max_lat = '-34.932803' where iso_key = 'UY';
update geoareas set bounding_min_long = '-79.025429', bounding_min_lat = '12.434358', bounding_max_long = '-66.876042', bounding_max_lat = '-4.235969' where iso_key = 'CO';
update geoareas set bounding_min_long = '-73.366188', bounding_min_lat = '12.177862', bounding_max_long = '-59.828927', bounding_max_lat = '0.687969' where iso_key = 'VE';
update geoareas set bounding_min_long = '-61.145048', bounding_min_lat = '-51.269945', bounding_max_long = '-57.791816', bounding_max_lat = '-52.308021' where iso_key = 'FK';
update geoareas set bounding_min_long = '-109.434142', bounding_min_lat = '-17.506011', bounding_max_long = '-66.435784', bounding_max_lat = '-55.89173' where iso_key = 'CL';
update geoareas set bounding_min_long = '-58.054513', bounding_min_lat = '5.993459', bounding_max_long = '-53.990472', bounding_max_lat = '1.842239' where iso_key = 'SR';
update geoareas set bounding_min_long = '-91.654142', bounding_min_lat = '1.455364', bounding_max_long = '-75.249587', bounding_max_lat = '-4.990651' where iso_key = 'EC';
update geoareas set bounding_min_long = '-61.390795', bounding_min_lat = '8.549298', bounding_max_long = '-56.4828', bounding_max_lat = '1.201219' where iso_key = 'GY';
update geoareas set bounding_min_long = '-74.002067', bounding_min_lat = '5.257974', bounding_max_long = '-34.805467', bounding_max_lat = '-33.742178' where iso_key = 'BR';
update geoareas set bounding_min_long = '19.519047', bounding_min_lat = '60.405822', bounding_max_long = '20.61128', bounding_max_lat = '60.011686' where iso_key = 'AX';
update geoareas set bounding_min_long = '20.899789', bounding_min_lat = '56.411182', bounding_max_long = '26.77571', bounding_max_lat = '53.89299' where iso_key = 'LT';
update geoareas set bounding_min_long = '7.377721', bounding_min_lat = '43.77092', bounding_max_long = '7.438699', bounding_max_lat = '43.73175' where iso_key = 'MC';
update geoareas set bounding_min_long = '-2.235827', bounding_min_lat = '49.266373', bounding_max_long = '-2.009898', bounding_max_lat = '49.169816' where iso_key = 'JE';
update geoareas set bounding_min_long = '2.524942', bounding_min_lat = '51.491095', bounding_max_long = '6.364501', bounding_max_lat = '49.51088' where iso_key = 'BE';
update geoareas set bounding_min_long = '14.128626', bounding_min_lat = '54.838178', bounding_max_long = '24.105743', bounding_max_lat = '49.020729' where iso_key = 'PL';
update geoareas set bounding_min_long = '43.439447', bounding_min_lat = '41.290968', bounding_max_long = '46.584733', bounding_max_lat = '38.869049' where iso_key = 'AM';
update geoareas set bounding_min_long = '-8.144794', bounding_min_lat = '60.831894', bounding_max_long = '1.746591', bounding_max_lat = '50.021392' where iso_key = 'GB';
update geoareas set bounding_min_long = '-5.262304687499977', bounding_min_lat = '53.41928710937501', bounding_max_long = '-2.662304687499926', bounding_max_lat = '51.39042968750005' where iso_key = 'GB-WLS';
update geoareas set bounding_min_long = '-7.54296875', bounding_min_lat = '60.83188476562498', bounding_max_long = '-0.774267578124949', bounding_max_lat = '54.68945312500006' where iso_key = 'GB-SCT';
update geoareas set bounding_min_long = '-8.144824218749989', bounding_min_lat = '55.241796875', bounding_max_long = '-5.470410156249983', bounding_max_lat = '54.05126953125' where iso_key = 'GB-NIR';
update geoareas set bounding_min_long = '-5.65625', bounding_min_lat = '55.80795898437501', bounding_max_long = '1.74658203125', bounding_max_lat = '50.02138671875002' where iso_key = 'GB-ENG';
update geoareas set bounding_min_long = '20.622183', bounding_min_lat = '70.064819', bounding_max_long = '31.536504', bounding_max_lat = '59.816039' where iso_key = 'FI';
update geoareas set bounding_min_long = '9.523998', bounding_min_lat = '49.001118', bounding_max_long = '17.147357', bounding_max_lat = '46.3997' where iso_key = 'AT';
update geoareas set bounding_min_long = '14.180354', bounding_min_lat = '36.075809', bounding_max_long = '14.566171', bounding_max_lat = '35.820191' where iso_key = 'MT';
update geoareas set bounding_min_long = '13.517139', bounding_min_lat = '46.534628', bounding_max_long = '19.401018', bounding_max_lat = '42.432914' where iso_key = 'HR';
update geoareas set bounding_min_long = '-68.371067', bounding_min_lat = '53.625513', bounding_max_long = '7.197318', bounding_max_lat = '12.032082' where iso_key = 'NL';
update geoareas set bounding_min_long = '-4.762494', bounding_min_lat = '51.09714', bounding_max_long = '9.556451', bounding_max_lat = '41.384916' where iso_key = 'FR';
update geoareas set bounding_min_long = '22.131859', bounding_min_lat = '52.353549', bounding_max_long = '40.12828', bounding_max_lat = '44.387575' where iso_key = 'UA';
update geoareas set bounding_min_long = '5.857503', bounding_min_lat = '55.058758', bounding_max_long = '15.016634', bounding_max_lat = '47.27882' where iso_key = 'DE';
update geoareas set bounding_min_long = '20.241845', bounding_min_lat = '48.263463', bounding_max_long = '29.705918', bounding_max_lat = '43.670797' where iso_key = 'RO';
update geoareas set bounding_min_long = '-18.160566', bounding_min_lat = '43.764564', bounding_max_long = '4.322041', bounding_max_lat = '27.646401' where iso_key = 'ES';
update geoareas set bounding_min_long = '1.414829', bounding_min_lat = '42.64272', bounding_max_long = '1.740184', bounding_max_lat = '42.434464' where iso_key = 'AD';
update geoareas set bounding_min_long = '-9.098871', bounding_min_lat = '80.477853', bounding_max_long = '33.629297', bounding_max_lat = '58.020955' where iso_key = 'NO';
update geoareas set bounding_min_long = '-31.282943', bounding_min_lat = '42.137377', bounding_max_long = '-6.212509', bounding_max_lat = '32.648292' where iso_key = 'PT';
update geoareas set bounding_min_long = '9.479453', bounding_min_lat = '47.270758', bounding_max_long = '9.610504', bounding_max_lat = '47.057386' where iso_key = 'LI';
update geoareas set bounding_min_long = '-72.818082', bounding_min_lat = '83.5996', bounding_max_long = '-11.42555', bounding_max_lat = '59.815471' where iso_key = 'GL';
update geoareas set bounding_min_long = '20.448602', bounding_min_lat = '42.358138', bounding_max_long = '23.005656', bounding_max_lat = '40.84991' where iso_key = 'MK';
update geoareas set bounding_min_long = '5.970003', bounding_min_lat = '47.775637', bounding_max_long = '10.454587', bounding_max_lat = '45.830045' where iso_key = 'CH';
update geoareas set bounding_min_long = '19.646481', bounding_min_lat = '41.743783', bounding_max_long = '28.231797', bounding_max_lat = '34.934457' where iso_key = 'GR';
update geoareas set bounding_min_long = '22.344042', bounding_min_lat = '44.237791', bounding_max_long = '28.585315', bounding_max_lat = '41.243529' where iso_key = 'BG';
update geoareas set bounding_min_long = '6.627689', bounding_min_lat = '47.082139', bounding_max_long = '18.485828', bounding_max_lat = '36.687838' where iso_key = 'IT';
update geoareas set bounding_min_long = '12.42749', bounding_min_lat = '41.906202', bounding_max_long = '12.439169', bounding_max_lat = '41.897572' where iso_key = 'VA';
update geoareas set bounding_min_long = '12.396897', bounding_min_lat = '43.98977', bounding_max_long = '12.514668', bounding_max_lat = '43.894091' where iso_key = 'SM';
update geoareas set bounding_min_long = '18.839088', bounding_min_lat = '46.169172', bounding_max_long = '22.97682', bounding_max_lat = '42.242125' where iso_key = 'RS';
update geoareas set bounding_min_long = '39.978315', bounding_min_lat = '43.569796', bounding_max_long = '46.672583', bounding_max_lat = '41.070207' where iso_key = 'GE';
update geoareas set bounding_min_long = '15.736642', bounding_min_lat = '45.276565', bounding_max_long = '19.583746', bounding_max_lat = '42.559728' where iso_key = 'BA';
update geoareas set bounding_min_long = '19.280715', bounding_min_lat = '42.647966', bounding_max_long = '21.031099', bounding_max_lat = '39.653497' where iso_key = 'AL';
update geoareas set bounding_min_long = '20.029506', bounding_min_lat = '43.261081', bounding_max_long = '21.752915', bounding_max_lat = '41.853802' where iso_key = 'XK';
update geoareas set bounding_min_long = '21.854511', bounding_min_lat = '59.638996', bounding_max_long = '28.151078', bounding_max_lat = '57.525481' where iso_key = 'EE';
update geoareas set bounding_min_long = '-24.475658', bounding_min_lat = '66.526069', bounding_max_long = '-13.556118', bounding_max_lat = '63.406699' where iso_key = 'IS';
update geoareas set bounding_min_long = '11.147155', bounding_min_lat = '69.036872', bounding_max_long = '24.155508', bounding_max_lat = '55.346389' where iso_key = 'SE';
update geoareas set bounding_min_long = '32.301005', bounding_min_lat = '35.182685', bounding_max_long = '34.050149', bounding_max_lat = '34.56957' where iso_key = 'CY';
update geoareas set bounding_min_long = '13.378181', bounding_min_lat = '46.86329', bounding_max_long = '16.516181', bounding_max_lat = '45.42839' where iso_key = 'SI';
update geoareas set bounding_min_long = '18.436374', bounding_min_lat = '43.542355', bounding_max_long = '20.347627', bounding_max_lat = '41.869073' where iso_key = 'ME';
update geoareas set bounding_min_long = '21.014976', bounding_min_lat = '58.063433', bounding_max_long = '28.202031', bounding_max_lat = '55.667507' where iso_key = 'LV';
update geoareas set bounding_min_long = '23.175103', bounding_min_lat = '56.145824', bounding_max_long = '32.710283', bounding_max_lat = '51.265062' where iso_key = 'BY';
update geoareas set bounding_min_long = '-5.355799', bounding_min_lat = '36.1633070000001', bounding_max_long = '-5.33451', bounding_max_lat = '36.112175' where iso_key = 'GI';
update geoareas set bounding_min_long = '16.093054', bounding_min_lat = '48.553471', bounding_max_long = '22.87662', bounding_max_lat = '45.753022' where iso_key = 'HU';
update geoareas set bounding_min_long = '-10.390239', bounding_min_lat = '55.36582', bounding_max_long = '-6.027379', bounding_max_lat = '51.473706' where iso_key = 'IE';
update geoareas set bounding_min_long = '5.724953', bounding_min_lat = '50.167171', bounding_max_long = '6.493795', bounding_max_lat = '49.445458' where iso_key = 'LU';
update geoareas set bounding_min_long = '-179.999989', bounding_min_lat = '81.854177', bounding_max_long = '180', bounding_max_lat = '41.199269' where iso_key = 'RU';
update geoareas set bounding_min_long = '44.768255', bounding_min_lat = '41.890958', bounding_max_long = '50.365949', bounding_max_lat = '38.398742' where iso_key = 'AZ';
update geoareas set bounding_min_long = '12.089733', bounding_min_lat = '51.037815', bounding_max_long = '18.832215', bounding_max_lat = '48.576208' where iso_key = 'CZ';
update geoareas set bounding_min_long = '8.121499', bounding_min_lat = '57.73689', bounding_max_long = '15.137092', bounding_max_lat = '54.628862' where iso_key = 'DK';
update geoareas set bounding_min_long = '-4.785335', bounding_min_lat = '54.40717', bounding_max_long = '-4.337998', bounding_max_lat = '54.058716' where iso_key = 'IM';
update geoareas set bounding_min_long = '-2.646138', bounding_min_lat = '49.506591', bounding_max_long = '-2.512322', bounding_max_lat = '49.428715' where iso_key = 'GG';
update geoareas set bounding_min_long = '16.862671', bounding_min_lat = '49.597696', bounding_max_long = '22.538656', bounding_max_lat = '47.763416' where iso_key = 'SK';
update geoareas set bounding_min_long = '26.618975', bounding_min_lat = '48.477713', bounding_max_long = '30.131009', bounding_max_lat = '45.450456' where iso_key = 'MD';
update geoareas set bounding_min_long = '-7.422616', bounding_min_lat = '62.355677', bounding_max_long = '-6.406063', bounding_max_lat = '61.414288' where iso_key = 'FO';
update geoareas set bounding_min_long = '51.659307', bounding_min_lat = '-46.326836', bounding_max_long = '70.555415', bounding_max_lat = '-49.709834' where iso_key = 'TF';
update geoareas set bounding_min_long = '-180', bounding_min_lat = '-60.520905', bounding_max_long = '180', bounding_max_lat = '-89.998899' where iso_key = 'AQ';
update geoareas set bounding_min_long = '-159.842486', bounding_min_lat = '-21.186458', bounding_max_long = '-159.736885', bounding_max_lat = '-21.249504' where iso_key = 'CK';
update geoareas set bounding_min_long = '-169.948329', bounding_min_lat = '-18.966025', bounding_max_long = '-169.793429', bounding_max_lat = '-19.137901' where iso_key = 'NU';
update geoareas set bounding_min_long = '-180', bounding_min_lat = '-12.476913', bounding_max_long = '180', bounding_max_lat = '-21.705807' where iso_key = 'FJ';
update geoareas set bounding_min_long = '-151.5124', bounding_min_lat = '-8.781531', bounding_max_long = '-136.293897', bounding_max_lat = '-20.875883' where iso_key = 'PF';
update geoareas set bounding_min_long = '-178.194389', bounding_min_lat = '-13.221673', bounding_max_long = '-176.128055', bounding_max_lat = '-14.324913' where iso_key = 'WF';
update geoareas set bounding_min_long = '-128.350196', bounding_min_lat = '-24.323218', bounding_max_long = '-128.290097', bounding_max_lat = '-24.412566' where iso_key = 'PN';
update geoareas set bounding_min_long = '167.906165', bounding_min_lat = '-29.013939', bounding_max_long = '167.990398', bounding_max_lat = '-29.096312' where iso_key = 'NF';
update geoareas set bounding_min_long = '-172.496979', bounding_min_lat = '-8.553888', bounding_max_long = '-171.214722', bounding_max_lat = '-9.37889' where iso_key = 'TK';
update geoareas set bounding_min_long = '105.628998', bounding_min_lat = '-10.38408', bounding_max_long = '105.736603', bounding_max_lat = '-10.51097' where iso_key = 'CX';
update geoareas set bounding_min_long = '159.928254', bounding_min_lat = '-19.114647', bounding_max_long = '168.139122', bounding_max_lat = '-22.661097' where iso_key = 'NC';
update geoareas set bounding_min_long = '140.862338', bounding_min_lat = '-1.35325', bounding_max_long = '155.957644', bounding_max_lat = '-11.630556' where iso_key = 'PG';
update geoareas set bounding_min_long = '-174.540839', bounding_min_lat = '3.923533', bounding_max_long = '174.778924', bounding_max_lat = '-11.45682' where iso_key = 'KI';
update geoareas set bounding_min_long = '176.066376', bounding_min_lat = '-5.65778', bounding_max_long = '179.231094', bounding_max_lat = '-8.55415' where iso_key = 'TV';
update geoareas set bounding_min_long = '-176.847675', bounding_min_lat = '-8.546506', bounding_max_long = '178.536214', bounding_max_lat = '-52.570279' where iso_key = 'NZ';
update geoareas set bounding_min_long = '-172.778492', bounding_min_lat = '-13.465276', bounding_max_long = '-171.449581', bounding_max_lat = '-14.047256' where iso_key = 'WS';
update geoareas set bounding_min_long = '166.907053', bounding_min_lat = '-0.489376', bounding_max_long = '166.958419', bounding_max_lat = '-0.550819' where iso_key = 'NR';
update geoareas set bounding_min_long = '155.677557', bounding_min_lat = '-6.608847', bounding_max_long = '166.92917', bounding_max_lat = '-11.832197' where iso_key = 'SB';
update geoareas set bounding_min_long = '96.819443', bounding_min_lat = '-12.12833', bounding_max_long = '96.91470300000012', bounding_max_lat = '-12.2' where iso_key = 'CC';
update geoareas set bounding_min_long = '166.844731', bounding_min_lat = '11.168647', bounding_max_long = '171.756783', bounding_max_lat = '5.799801' where iso_key = 'MH';
update geoareas set bounding_min_long = '112.908178', bounding_min_lat = '-10.051738', bounding_max_long = '158.958908', bounding_max_lat = '-54.749268' where iso_key = 'AU';
update geoareas set bounding_min_long = '150.6118705220982', bounding_min_lat = '-35.11962193000792', bounding_max_long = '150.7221073427097', bounding_max_lat = '-35.19004429597557' where iso_key = 'AU-JB';
update geoareas set bounding_min_long = '148.7700125416807', bounding_min_lat = '-35.14640659899841', bounding_max_long = '149.4038358080466', bounding_max_lat = '-35.91410926500274' where iso_key = 'AU-ACT';
update geoareas set bounding_min_long = '129.001960029777', bounding_min_lat = '-25.99901367192805', bounding_max_long = '141.025727578291', bounding_max_lat = '-38.07140671057834' where iso_key = 'AU-SA';
update geoareas set bounding_min_long = '140.9663354861816', bounding_min_lat = '-34.005190735152', bounding_max_long = '149.9424954965635', bounding_max_lat = '-39.14554005233448' where iso_key = 'AU-VIC';
update geoareas set bounding_min_long = '141.0021069719212', bounding_min_lat = '-28.14440193736187', bounding_max_long = '153.6169609687646', bounding_max_lat = '-37.50057905208423' where iso_key = 'AU-NSW';
update geoareas set bounding_min_long = '112.908153263503', bounding_min_lat = '-13.74418166147464', bounding_max_long = '129.001960029777', bounding_max_lat = '-35.09780308151935' where iso_key = 'AU-WA';
update geoareas set bounding_min_long = '138.0017406465288', bounding_min_lat = '-10.05169861046129', bounding_max_long = '153.5386723915595', bounding_max_lat = '-29.13934582297228' where iso_key = 'AU-QLD';
update geoareas set bounding_min_long = '129.001960029777', bounding_min_lat = '-10.96879337200827', bounding_max_long = '138.0024217988984', bounding_max_lat = '-25.99901367192805' where iso_key = 'AU-NT';
update geoareas set bounding_min_long = '143.8386891113783', bounding_min_lat = '-39.5802471001673', bounding_max_long = '148.474128741331', bounding_max_lat = '-43.61945868003225' where iso_key = 'AU-TAS';
update geoareas set bounding_min_long = '-170.820524', bounding_min_lat = '-14.257476', bounding_max_long = '-170.56811', bounding_max_lat = '-14.359795' where iso_key = 'AS';
update geoareas set bounding_min_long = '166.526094', bounding_min_lat = '-13.709499', bounding_max_long = '169.896328', bounding_max_lat = '-20.241813' where iso_key = 'VU';
update geoareas set bounding_min_long = '42.656446', bounding_min_lat = '11.499815', bounding_max_long = '48.938543', bounding_max_lat = '7.997083' where iso_key = 'SOL';
update geoareas set bounding_min_long = '21.825262', bounding_min_lat = '22.202418', bounding_max_long = '38.609458', bounding_max_lat = '8.665621' where iso_key = 'SD';
update geoareas set bounding_min_long = '7.495595', bounding_min_lat = '37.340383', bounding_max_long = '11.535968', bounding_max_lat = '30.229421' where iso_key = 'TN';
update geoareas set bounding_min_long = '-17.09877', bounding_min_lat = '27.656452', bounding_max_long = '-8.6821', bounding_max_lat = '20.806146' where iso_key = 'EH';
update geoareas set bounding_min_long = '11.130153', bounding_min_lat = '3.687295', bounding_max_long = '18.622151', bounding_max_lat = '-5.004294' where iso_key = 'CG';
update geoareas set bounding_min_long = '24.703226', bounding_min_lat = '31.654967', bounding_max_long = '36.871373', bounding_max_lat = '21.994885' where iso_key = 'EG';
update geoareas set bounding_min_long = '-16.711791', bounding_min_lat = '12.67995', bounding_max_long = '-13.673553', bounding_max_lat = '10.940159' where iso_key = 'GW';
update geoareas set bounding_min_long = '-13.292697', bounding_min_lat = '9.996522', bounding_max_long = '-10.283217', bounding_max_lat = '6.90653' where iso_key = 'SL';
update geoareas set bounding_min_long = '21.978897', bounding_min_lat = '-8.193608', bounding_max_long = '33.661543', bounding_max_lat = '-18.041534' where iso_key = 'ZM';
update geoareas set bounding_min_long = '25.22397', bounding_min_lat = '-15.643076', bounding_max_long = '33.006751', bounding_max_lat = '-22.402043' where iso_key = 'ZW';
update geoareas set bounding_min_long = '8.434245', bounding_min_lat = '3.758324', bounding_max_long = '11.33536', bounding_max_lat = '0.960097' where iso_key = 'GQ';
update geoareas set bounding_min_long = '-15.05122', bounding_min_lat = '12.673904', bounding_max_long = '-7.681205', bounding_max_lat = '7.215891' where iso_key = 'GN';
update geoareas set bounding_min_long = '29.323409', bounding_min_lat = '-0.994926', bounding_max_long = '40.463556', bounding_max_lat = '-11.716235' where iso_key = 'TZ';
update geoareas set bounding_min_long = '13.448203', bounding_min_lat = '23.445236', bounding_max_long = '23.983425', bounding_max_lat = '7.475306' where iso_key = 'TD';
update geoareas set bounding_min_long = '-16.824807', bounding_min_lat = '13.812129', bounding_max_long = '-13.826722', bounding_max_lat = '13.064138' where iso_key = 'GM';
update geoareas set bounding_min_long = '55.232836', bounding_min_lat = '-20.865134', bounding_max_long = '55.839104', bounding_max_lat = '-21.369083' where iso_key = 'RE';
update geoareas set bounding_min_long = '-0.090196', bounding_min_lat = '11.115601', bounding_max_long = '1.777907', bounding_max_lat = '6.089422' where iso_key = 'TG';
update geoareas set bounding_min_long = '2.686017', bounding_min_lat = '13.872849', bounding_max_long = '14.627149', bounding_max_lat = '4.277414' where iso_key = 'NG';
update geoareas set bounding_min_long = '19.977365', bounding_min_lat = '-17.787596', bounding_max_long = '29.364802', bounding_max_lat = '-26.854174' where iso_key = 'BW';
update geoareas set bounding_min_long = '8.703169', bounding_min_lat = '2.302185', bounding_max_long = '14.480595', bounding_max_lat = '-3.916298' where iso_key = 'GA';
update geoareas set bounding_min_long = '41.764666', bounding_min_lat = '12.708604', bounding_max_long = '43.409785', bounding_max_lat = '10.941038' where iso_key = 'DJ';
update geoareas set bounding_min_long = '43.226644', bounding_min_lat = '-11.368453', bounding_max_long = '44.526719', bounding_max_lat = '-12.368289' where iso_key = 'KM';
update geoareas set bounding_min_long = '6.468164', bounding_min_lat = '1.699121', bounding_max_long = '7.45229', bounding_max_lat = '0.047388' where iso_key = 'ST';
update geoareas set bounding_min_long = '11.721693', bounding_min_lat = '-16.967698', bounding_max_long = '25.2588', bounding_max_lat = '-28.93875' where iso_key = 'NA';
update geoareas set bounding_min_long = '-8.603578', bounding_min_lat = '10.724074', bounding_max_long = '-2.505862', bounding_max_lat = '4.351311' where iso_key = 'CI';
update geoareas set bounding_min_long = '12.213704', bounding_min_lat = '5.312131', bounding_max_long = '31.273988', bounding_max_lat = '-13.453804' where iso_key = 'CD';
update geoareas set bounding_min_long = '40.964404', bounding_min_lat = '11.983713', bounding_max_long = '51.390176', bounding_max_lat = '-1.695348' where iso_key = 'SO';
update geoareas set bounding_min_long = '57.31767', bounding_min_lat = '-19.989942', bounding_max_long = '57.791956', bounding_max_lat = '-20.513218' where iso_key = 'MU';
update geoareas set bounding_min_long = '29.014126', bounding_min_lat = '-2.312985', bounding_max_long = '30.811432', bounding_max_lat = '-4.455852' where iso_key = 'BI';
update geoareas set bounding_min_long = '32.67044', bounding_min_lat = '-9.394982', bounding_max_long = '35.892724', bounding_max_lat = '-17.131099' where iso_key = 'MW';
update geoareas set bounding_min_long = '0.16382', bounding_min_lat = '23.517867', bounding_max_long = '15.963191', bounding_max_lat = '11.696289' where iso_key = 'NE';
update geoareas set bounding_min_long = '-11.507535', bounding_min_lat = '8.53767', bounding_max_long = '-7.39993', bounding_max_lat = '4.351311' where iso_key = 'LR';
update geoareas set bounding_min_long = '9.310264', bounding_min_lat = '33.181954', bounding_max_long = '25.150486', bounding_max_lat = '19.49664' where iso_key = 'LY';
update geoareas set bounding_min_long = '72.349724', bounding_min_lat = '-7.220386', bounding_max_long = '72.498552', bounding_max_lat = '-7.43536' where iso_key = 'IO';
update geoareas set bounding_min_long = '28.85765', bounding_min_lat = '-1.063036', bounding_max_long = '30.876596', bounding_max_lat = '-2.808562' where iso_key = 'RW';
update geoareas set bounding_min_long = '29.561948', bounding_min_lat = '4.220208', bounding_max_long = '34.978206', bounding_max_lat = '-1.469936' where iso_key = 'UG';
update geoareas set bounding_min_long = '-14.414954', bounding_min_lat = '-7.882568', bounding_max_long = '-5.659701', bounding_max_lat = '-16.004036' where iso_key = 'SH';
update geoareas set bounding_min_long = '30.787506', bounding_min_lat = '-25.742976', bounding_max_long = '32.112903', bounding_max_lat = '-27.309961' where iso_key = 'SZ';
update geoareas set bounding_min_long = '19.510555', bounding_min_lat = '60.350273', bounding_max_long = '45.223111', bounding_max_lat = '-12.984995' where iso_key = 'YT';
update geoareas set bounding_min_long = '33.899978', bounding_min_lat = '5.492301', bounding_max_long = '41.884038', bounding_max_lat = '-4.692375' where iso_key = 'KE';
update geoareas set bounding_min_long = '30.221753', bounding_min_lat = '-10.464322', bounding_max_long = '40.844567', bounding_max_lat = '-26.861616' where iso_key = 'MZ';
update geoareas set bounding_min_long = '-17.535617', bounding_min_lat = '16.678905', bounding_max_long = '-11.382426', bounding_max_lat = '12.328033' where iso_key = 'SN';
update geoareas set bounding_min_long = '-5.523559', bounding_min_lat = '15.077866', bounding_max_long = '2.389188', bounding_max_lat = '9.424722' where iso_key = 'BF';
update geoareas set bounding_min_long = '-17.003065', bounding_min_lat = '35.9299', bounding_max_long = '-1.065537', bounding_max_lat = '21.420708' where iso_key = 'MA';
update geoareas set bounding_min_long = '14.43114', bounding_min_lat = '10.996228', bounding_max_long = '27.40332', bounding_max_lat = '2.270068' where iso_key = 'CF';
update geoareas set bounding_min_long = '16.447555', bounding_min_lat = '-22.146296', bounding_max_long = '37.887745', bounding_max_lat = '-46.96287' where iso_key = 'ZA';
update geoareas set bounding_min_long = '27.051713', bounding_min_lat = '-28.581718', bounding_max_long = '29.390692', bounding_max_lat = '-30.642315' where iso_key = 'LS';
update geoareas set bounding_min_long = '24.147395', bounding_min_lat = '12.223079', bounding_max_long = '35.268318', bounding_max_lat = '3.490717' where iso_key = 'SS';
update geoareas set bounding_min_long = '36.426748', bounding_min_lat = '18.005077', bounding_max_long = '43.116728', bounding_max_lat = '12.376583' where iso_key = 'ER';
update geoareas set bounding_min_long = '32.998896', bounding_min_lat = '14.852298', bounding_max_long = '47.978188', bounding_max_lat = '3.456094' where iso_key = 'ET';
update geoareas set bounding_min_long = '-3.243905', bounding_min_lat = '11.16689', bounding_max_long = '1.187246', bounding_max_lat = '4.762449' where iso_key = 'GH';
update geoareas set bounding_min_long = '8.532844', bounding_min_lat = '13.078504', bounding_max_long = '16.183436', bounding_max_lat = '1.676229' where iso_key = 'CM';
update geoareas set bounding_min_long = '-8.683366', bounding_min_lat = '37.092362', bounding_max_long = '11.96788', bounding_max_lat = '18.986645' where iso_key = 'DZ';
update geoareas set bounding_min_long = '-12.280614', bounding_min_lat = '24.995581', bounding_max_long = '4.234657', bounding_max_lat = '10.143257' where iso_key = 'ML';
update geoareas set bounding_min_long = '0.763344', bounding_min_lat = '12.383844', bounding_max_long = '3.834474', bounding_max_lat = '6.216778' where iso_key = 'BJ';
update geoareas set bounding_min_long = '11.743036', bounding_min_lat = '-4.428928', bounding_max_long = '24.046677', bounding_max_lat = '-18.019778' where iso_key = 'AO';
update geoareas set bounding_min_long = '55.383421', bounding_min_lat = '-4.55874', bounding_max_long = '55.542998', bounding_max_lat = '-4.785496' where iso_key = 'SC';
update geoareas set bounding_min_long = '43.257133', bounding_min_lat = '-12.079624', bounding_max_long = '50.482738', bounding_max_lat = '-25.570532' where iso_key = 'MG';
update geoareas set bounding_min_long = '-175.36234', bounding_min_lat = '-18.565326', bounding_max_long = '-173.921859', bounding_max_lat = '-21.450628' where iso_key = 'TO';
update geoareas set bounding_min_long = '-17.063992', bounding_min_lat = '27.285932', bounding_max_long = '-4.82262', bounding_max_lat = '14.745354' where iso_key = 'MR';
update geoareas set bounding_min_long = '-25.341548', bounding_min_lat = '17.19368', bounding_max_long = '-22.681892', bounding_max_lat = '14.818217' where iso_key = 'CV';
