create table supported_languages
(
  id bigint not null
    constraint supported_languages_pkey
    primary key,
  code varchar(10) not null
    constraint uk_7v91ica560hywsf7vr9b6pbb1
    unique,
  name varchar(150) not null
);

create table popularity
(
  geo_area_id bigint not null
    constraint popularity_pkey
    primary key
    constraint fk3tl1aambjnny30sn8djn4dx0e
    references geoareas,
  geo_area_iso_key varchar(255),
  geo_area_name varchar(255),
  rank integer,
  total bigint
);

create table airports
(
  id bigint not null
    constraint airports_pkey
    primary key,
  iata_code varchar(15) not null
    constraint uk_rck10qn096aw10ds8rjqf35ah
    unique,
  last_modification_time timestamp,
  lat numeric(19,10),
  long numeric(19,10),
  name varchar(250) not null
);

create table user_airports
(
  id bigint not null
    constraint user_airports_pkey
    primary key,
  last_modification_time timestamp,
  airport_id bigint not null
    constraint fk65etq0uen2r6jt6q3bnhp7p0v
    references airports,
  user_id bigint not null
    constraint uk_8873k802j1e3nc9x215sdrcka
    unique
    constraint fkpsyw7aghub9w5cdft8ieyxd6a
    references users
);

create table airport_geo_areas
(
  id bigint not null
    constraint airport_geo_areas_pkey
    primary key,
  last_modification_time timestamp,
  airport_id bigint not null
    constraint fkdln46k67gtua2dr9ug70oot0r
    references airports,
  geo_area_id bigint not null
    constraint fkcp4j40gb45n2r0xrgnghy35rn
    references geoareas,
  constraint ak_aiportgeoarea
  unique (geo_area_id, airport_id)
);

create table geoarea_details
(
  geo_area_id bigint not null
    constraint geoarea_details_pkey
    primary key
    constraint fk718i97xpiy5v8bm2ac0ehdvi
    references geoareas,
  population bigint,
  size bigint,
  last_modification_time timestamp
);

create table geoarea_labels
(
  id bigint not null
    constraint geoarea_labels_pkey
    primary key,
  latitude numeric(19,14) not null,
  longitude numeric(19,14) not null,
  resolution integer not null,
  geo_area_id bigint not null
    constraint fklovlgsfs0yke7h30sbt6q2mb7
    references geoareas
);

create table geoarea_must_see
(
  id bigint not null
    constraint geoarea_must_see_pkey
    primary key,
  description varchar(250) not null,
  geo_area_id bigint not null
    constraint fkt0dcjbltfhmyslmqyalb0b2ms
    references geoarea_details
);

create table geoarea_user_notes
(
  id bigint not null
    constraint geoarea_user_notes_pkey
    primary key,
  notes text not null,
  geo_area_id bigint not null
    constraint fk7uluqsx242x571ys558gv6cj6
    references geoareas,
  user_id bigint not null
    constraint fkqdqpmk18s0c7taokn5c3e7k28
    references users,
  constraint ak_geoareausernotes
  unique (geo_area_id, user_id)
);

-- translations

create table region_translations
(
  id bigint not null
    constraint region_translations_pkey
    primary key,
  name varchar(255) not null,
  region_id bigint not null
    constraint fk9ytr2unk00rq5llejqaxqym5k
    references regions,
  supported_language_id bigint not null
    constraint fkqokvcnykxi92c863odahdb5iw
    references supported_languages,
  constraint ak_regiontranslations
  unique (region_id, supported_language_id)
);

create table geoarea_translations
(
  id bigint not null
    constraint geoarea_translations_pkey
    primary key,
  name varchar(255) not null,
  geo_area_id bigint not null
    constraint fk2cwfq6t3artk8wqhrwy8rkn2j
    references geoareas,
  supported_language_id bigint not null
    constraint fkc6n9lbjkcybocddy06rfhm9og
    references supported_languages,
  constraint ak_geoareatranslations
  unique (geo_area_id, supported_language_id)
);

create table geoarea_must_see_translations
(
  id bigint not null
    constraint geoarea_must_see_translations_pkey
    primary key,
  description varchar(255) not null,
  geo_area_must_see_id bigint not null
    constraint fk3px9milyocm5r95jly8m2st57
    references geoarea_must_see,
  supported_language_id bigint not null
    constraint fkr10aw7ir7vgqmdmna3tppwmu7
    references supported_languages,
  constraint ak_geoareamustseetranslations
  unique (geo_area_must_see_id, supported_language_id)
);

create table airport_translations
(
  id bigint not null
    constraint airport_translations_pkey
    primary key,
  name varchar(255) not null,
  airport_id bigint not null
    constraint fki1wxkerr0kj0wih3n9brp62hn
    references airports,
  supported_language_id bigint not null
    constraint fkt6wuikys0evcpjchyjry38pfi
    references supported_languages,
  constraint ak_airporttranslations
  unique (airport_id, supported_language_id)
);

-- sequences

create sequence supported_languages_id_seq;
create sequence airports_id_seq;
create sequence airport_geo_areas_id_seq;
create sequence geoarea_labels_id_seq;
create sequence geoarea_must_see_id_seq;
create sequence geoarea_user_notes_id_seq;
--create sequence top_places_id_seq;
create sequence user_airports_id_seq;

create sequence region_translations_id_seq;
create sequence geoarea_translations_id_seq;
create sequence geoarea_must_see_translations_id_seq;
create sequence airport_translations_id_seq;
