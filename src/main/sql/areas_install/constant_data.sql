insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total)
select row_number() over (order by total DESC) as rank,
       id,
       iso_key,
       name,
       total
from (
     select g.id,
            g.iso_key,
            g.name,
            count(s.id) as total
     from selections s
            inner join geoareas g
              on s.geo_area_id = g.id
     where g.geo_area_type = 'COUNTRY'
       and selection_type = 'BEEN'
     group by g.id, g.iso_key, g.name
     order by 4 DESC
     ) as a;