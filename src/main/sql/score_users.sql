--DROP TABLE user_scores;
SELECT u.id as user_id, AVG(s.score) as score, sum(s.score)
INTO   user_scores
FROM   selections, geoarea_score s, users u, geoareas
WHERE  selections.selection_type ILIKE '%BEEN%' 
AND    selections.user_id = u.id
AND    selections.geo_area_id = geoareas.id
AND    selections.geo_area_id = s.geoarea_id
AND    (geoareas.parent_id IS NULL)
--AND    selections.user_id = 
--       ( 
--              SELECT user_id 
--              FROM   selections 
--              WHERE  geo_area_id = 9 
--              AND    selection_type ilike '%LIVED%')
GROUP BY u.id;