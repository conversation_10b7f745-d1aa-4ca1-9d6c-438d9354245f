DO
$$
DECLARE
    user_cursor CURSOR FOR
                    SELECT *
                      FROM users;
BEGIN
  FOR user_rec IN user_cursor LOOP
    UPDATE users
       SET num_areas_visited = (
          select count(*)
            from selections s
                 INNER JOIN geoareas as g
                         ON g.id = s.geo_area_id
                        AND g.parent_id is null
           where s.user_id = user_rec.id
             AND (   s.selection_type = 'BEEN'
                  OR s.selection_type = 'LIVED')
    )
    WHERE id = user_rec.id;
  END LOOP;
END
$$;