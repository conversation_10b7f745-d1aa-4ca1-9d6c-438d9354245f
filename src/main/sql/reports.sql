
-- GET ALL E-MAILS
select username
from users
order by username;

-- GET ALL E-MAILS, EXCLUDING THE ***@anonymous.com ONES
select username
from users
where username not like '%@anonymous.com'
order by username;

-- GET THE MOST VISITED COUNTRIES
SELECT g.iso_key,
  g.name,
  count(s.*) AS number_of_times_visited
FROM selections s
  INNER JOIN geoareas g
    ON g.id = s.geo_area_id
WHERE /*s.geo_area_id in (SELECT id
                           FROM geoareas
                          WHERE geo_area_type = 'COUNTRY'
                            AND iso_key in ('US', 'BR', 'CA', 'GB')
                        )
   AND */s.selection_type = 'BEEN'
GROUP BY g.iso_key,
         g.name
ORDER BY number_of_times_visited DESC