CREATE TABLE user_deletion_queue
(
  id bigint PRIMARY KEY,
  user_id bigint NOT NULL
);

ALTER TABLE public.user_deletion_queue OWNER TO visited;


CREATE SEQUENCE user_deletion_queue_id_seq
  START WITH 1
  INCREMENT BY 1
  NO MINVALUE
  NO MAXVALUE
  CACHE 1;


ALTER TABLE public.user_deletion_queue_id_seq OWNER TO visited;


--
-- Name: authorities; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE authorities (
  id bigint NOT NULL,
  name character varying(50) NOT NULL
);


ALTER TABLE public.authorities OWNER TO visited;

--
-- Name: authorities_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE authorities_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.authorities_id_seq OWNER TO visited;

--
-- Name: geoareas; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE geoareas (
  id bigint NOT NULL,
  iso_key character varying(15) NOT NULL,
  name character varying(250) NOT NULL,
  geo_area_type character varying(25) NOT NULL,
  parent_id bigint,
  region_id bigint
);


ALTER TABLE public.geoareas OWNER TO visited;

--
-- Name: geoareas_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE geoareas_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.geoareas_id_seq OWNER TO visited;

--
-- Name: regions; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE regions (
  id bigint NOT NULL,
  name character varying(100) NOT NULL
);


ALTER TABLE public.regions OWNER TO visited;

--
-- Name: regions_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE regions_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.regions_id_seq OWNER TO visited;

--
-- Name: selection_logs; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE selection_logs (
  id bigint NOT NULL,
  date character varying(10) NOT NULL,
  "timestamp" timestamp without time zone NOT NULL,
  selection_type character varying(10) NOT NULL,
  geo_area_id bigint NOT NULL,
  user_id bigint NOT NULL
);


ALTER TABLE public.selection_logs OWNER TO visited;

--
-- Name: selection_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE selection_logs_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.selection_logs_id_seq OWNER TO visited;

--
-- Name: selections; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE selections (
  id bigint NOT NULL,
  date character varying(10) NOT NULL,
  "timestamp" timestamp without time zone NOT NULL,
  selection_type character varying(10) NOT NULL,
  geo_area_id bigint NOT NULL,
  user_id bigint NOT NULL
);


ALTER TABLE public.selections OWNER TO visited;

--
-- Name: selections_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE selections_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.selections_id_seq OWNER TO visited;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE sessions (
  id bigint NOT NULL,
  date character varying(10) NOT NULL,
  os character varying(50) NOT NULL,
  version character varying(50) NOT NULL,
  start_date_time timestamp without time zone NOT NULL,
  token character varying(550) NOT NULL,
  user_id bigint NOT NULL
);


ALTER TABLE public.sessions OWNER TO visited;

--
-- Name: sessions_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE sessions_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.sessions_id_seq OWNER TO visited;

--
-- Name: users; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE users (
  id bigint NOT NULL,
  enabled boolean NOT NULL,
  last_password_reset_date timestamp without time zone NOT NULL,
  password character varying(100) NOT NULL,
  username character varying(500) NOT NULL
);


ALTER TABLE public.users OWNER TO visited;

--
-- Name: users_authorities; Type: TABLE; Schema: public; Owner: visited; Tablespace:
--

CREATE TABLE users_authorities (
  user_id bigint NOT NULL,
  authority_id bigint NOT NULL
);


ALTER TABLE public.users_authorities OWNER TO visited;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: visited
--

CREATE SEQUENCE users_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO visited;


--
-- Name: gdpr_ageement; Type: SEQUENCE; Schema: public; Owner: visited
--
CREATE TABLE "public"."gdpr_agreements" (
  "id" integer,
"agreed" character varying(5) NOT NULL,
"required" boolean NOT NULL,
"user_id" bigint NOT NULL,
"timestamp" timestamp without time zone NOT NULL,
PRIMARY KEY ("id"),
UNIQUE ("user_id"),
FOREIGN KEY ("user_id") REFERENCES "public"."users"("id")
) TABLESPACE "pg_default";


--
-- Data for Name: authorities; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO authorities (id, name) VALUES (1, 'ROLE_USER');
INSERT INTO authorities (id, name) VALUES (2, 'ROLE_ADMIN');


--
-- Name: authorities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('authorities_id_seq', 2, true);


--
-- Data for Name: geoareas; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (1, 'AN', 'Netherlands Antilles', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (2, 'JM', 'Jamaica', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (3, 'BB', 'Barbados', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (4, 'CW', 'Curaçao', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (5, 'NI', 'Nicaragua', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (6, 'BZ', 'Belize', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (7, 'MP', 'Northern Mariana Islands', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (8, 'LC', 'Saint Lucia', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (9, 'US', 'United States of America', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (10, 'US-CO', 'Colorado', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (11, 'US-WV', 'West Virginia', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (12, 'US-TX', 'Texas', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (13, 'US-PA', 'Pennsylvania', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (14, 'US-AL', 'Alabama', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (15, 'US-CA', 'California', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (16, 'US-VA', 'Virginia', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (17, 'US-RI', 'Rhode Island', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (18, 'US-MI', 'Michigan', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (19, 'US-MN', 'Minnesota', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (20, 'US-DC', 'District of Columbia', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (21, 'US-KS', 'Kansas', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (22, 'US-TN', 'Tennessee', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (23, 'US-NE', 'Nebraska', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (24, 'US-OK', 'Oklahoma', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (25, 'US-OH', 'Ohio', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (26, 'US-MS', 'Mississippi', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (27, 'US-AK', 'Alaska', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (28, 'US-SD', 'South Dakota', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (29, 'US-MA', 'Massachusetts', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (30, 'US-DE', 'Delaware', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (31, 'US-HI', 'Hawaii', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (32, 'US-IA', 'Iowa', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (33, 'US-IL', 'Illinois', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (34, 'US-AZ', 'Arizona', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (35, 'US-NJ', 'New Jersey', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (36, 'US-GA', 'Georgia', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (37, 'US-WY', 'Wyoming', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (38, 'US-MO', 'Missouri', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (39, 'US-ME', 'Maine', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (40, 'US-ND', 'North Dakota', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (41, 'US-MT', 'Montana', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (42, 'US-NV', 'Nevada', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (43, 'US-WA', 'Washington', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (44, 'US-NM', 'New Mexico', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (45, 'US-WI', 'Wisconsin', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (46, 'US-NC', 'North Carolina', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (47, 'US-UT', 'Utah', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (48, 'US-NH', 'New Hampshire', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (49, 'US-OR', 'Oregon', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (50, 'US-AR', 'Arkansas', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (51, 'US-FL', 'Florida', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (52, 'US-ID', 'Idaho', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (53, 'US-KY', 'Kentucky', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (54, 'US-VT', 'Vermont', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (55, 'US-MD', 'Maryland', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (56, 'US-SC', 'South Carolina', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (57, 'US-CT', 'Connecticut', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (58, 'US-IN', 'Indiana', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (59, 'US-NY', 'New York', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (60, 'US-LA', 'Louisiana', 'STATE', 9, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (61, 'AI', 'Anguilla', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (62, 'PR', 'Puerto Rico', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (63, 'AW', 'Aruba', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (64, 'KY', 'Cayman Islands', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (65, 'SX', 'Sint Maarten', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (66, 'TT', 'Trinidad and Tobago', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (67, 'VG', 'British Virgin Islands', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (68, 'GU', 'Guam', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (69, 'PM', 'Saint Pierre and Miquelon', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (70, 'MQ', 'Martinique', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (71, 'BS', 'The Bahamas', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (72, 'MX', 'Mexico', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (73, 'DM', 'Dominica', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (74, 'CR', 'Costa Rica', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (75, 'KN', 'Saint Kitts and Nevis', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (76, 'MF', 'Saint Martin', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (77, 'MS', 'Montserrat', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (78, 'SV', 'El Salvador', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (79, 'HT', 'Haiti', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (80, 'AG', 'Antigua and Barbuda', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (81, 'BM', 'Bermuda', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (82, 'CA', 'Canada', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (83, 'CA-MB', 'Manitoba', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (84, 'CA-NU', 'Nunavut', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (85, 'CA-SK', 'Saskatchewan', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (86, 'CA-NB', 'New Brunswick', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (87, 'CA-NL', 'Newfoundland and Labrador', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (88, 'CA-YT', 'Yukon', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (89, 'CA-AB', 'Alberta', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (90, 'CA-BC', 'British Columbia', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (91, 'CA-ON', 'Ontario', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (92, 'CA-QC', 'Quebec', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (93, 'CA-NT', 'Northwest Territories', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (94, 'CA-NS', 'Nova Scotia', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (95, 'CA-PE', 'Prince Edward Island', 'STATE', 82, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (96, 'GD', 'Grenada', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (97, 'DO', 'Dominican Republic', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (98, 'HN', 'Honduras', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (99, 'TC', 'Turks and Caicos Islands', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (100, 'VC', 'Saint Vincent and the Grenadines', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (101, 'CU', 'Cuba', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (102, 'VI', 'United States Virgin Islands', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (103, 'GT', 'Guatemala', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (104, 'BL', 'Saint Barthelemy', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (105, 'PA', 'Panama', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (106, 'GP', 'Guadeloupe', 'COUNTRY', NULL, 1);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (107, 'SY', 'Syria', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (108, 'JP', 'Japan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (109, 'KR', 'South Korea', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (110, 'CN', 'China', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (111, 'BD', 'Bangladesh', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (112, 'KH', 'Cambodia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (113, 'KZ', 'Kazakhstan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (114, 'ID', 'Indonesia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (115, 'IQ', 'Iraq', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (116, 'MN', 'Mongolia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (117, 'TH', 'Thailand', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (118, 'KW', 'Kuwait', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (119, 'PW', 'Palau', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (120, 'AE', 'United Arab Emirates', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (121, 'UZ', 'Uzbekistan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (122, 'TW', 'Taiwan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (123, 'YE', 'Yemen', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (124, 'TJ', 'Tajikistan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (125, 'BT', 'Bhutan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (126, 'TL', 'Timor', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (127, 'OM', 'Oman', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (128, 'VN', 'Vietnam', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (129, 'PH', 'Philippines', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (130, 'KP', 'North Korea', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (131, 'IR', 'Iran', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (132, 'TR', 'Turkey', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (133, 'AF', 'Afghanistan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (134, 'IL', 'Israel', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (135, 'BN', 'Brunei', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (136, 'LK', 'Sri Lanka', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (137, 'SA', 'Saudi Arabia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (138, 'LB', 'Lebanon', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (139, 'MY', 'Malaysia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (140, 'MM', 'Myanmar', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (141, 'PS', 'Palestine', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (142, 'NP', 'Nepal', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (143, 'BH', 'Bahrain', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (144, 'TM', 'Turkmenistan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (145, 'KG', 'Kyrgyzstan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (146, 'MV', 'Maldives', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (147, 'LA', 'Laos', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (148, 'QA', 'Qatar', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (149, 'HK', 'Hong Kong S.A.R.', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (150, 'FM', 'Micronesia', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (151, 'SG', 'Singapore', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (152, 'JO', 'Jordan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (153, 'MO', 'Macau S.A.R', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (154, 'IN', 'India', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (155, 'PK', 'Pakistan', 'COUNTRY', NULL, 2);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (156, 'GF', 'French Guiana', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (157, 'PE', 'Peru', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (158, 'PY', 'Paraguay', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (159, 'AR', 'Argentina', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (160, 'BO', 'Bolivia', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (161, 'UY', 'Uruguay', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (162, 'CO', 'Colombia', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (163, 'VE', 'Venezuela', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (164, 'FK', 'Falkland Islands', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (165, 'CL', 'Chile', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (166, 'SR', 'Suriname', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (167, 'EC', 'Ecuador', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (168, 'GY', 'Guyana', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (169, 'BR', 'Brazil', 'COUNTRY', NULL, 3);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (170, 'AX', 'Aland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (171, 'LT', 'Lithuania', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (172, 'MC', 'Monaco', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (173, 'JE', 'Jersey', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (174, 'BE', 'Belgium', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (175, 'SM', 'San Marino', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (176, 'PL', 'Poland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (177, 'AM', 'Armenia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (178, 'GB', 'United Kingdom', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (179, 'FI', 'Finland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (180, 'AT', 'Austria', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (181, 'MT', 'Malta', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (182, 'HR', 'Croatia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (183, 'NL', 'Netherlands', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (184, 'FR', 'France', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (185, 'UA', 'Ukraine', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (186, 'DE', 'Germany', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (187, 'RO', 'Romania', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (188, 'ES', 'Spain', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (189, 'AD', 'Andorra', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (190, 'NO', 'Norway', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (191, 'PT', 'Portugal', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (192, 'LI', 'Liechtenstein', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (193, 'GL', 'Greenland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (194, 'VA', 'Vatican', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (195, 'MK', 'Macedonia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (196, 'CH', 'Switzerland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (197, 'GR', 'Greece', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (198, 'BG', 'Bulgaria', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (199, 'IT', 'Italy', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (200, 'RS', 'Serbia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (201, 'GE', 'Georgia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (202, 'BA', 'Bosnia and Herzegovina', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (203, 'AL', 'Albania', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (204, 'XK', 'Kosovo', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (205, 'EE', 'Estonia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (206, 'IS', 'Iceland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (207, 'SE', 'Sweden', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (208, 'CY', 'Cyprus', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (209, 'SI', 'Slovenia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (210, 'ME', 'Montenegro', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (211, 'LV', 'Latvia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (212, 'BY', 'Belarus', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (213, 'GI', 'Gibraltar', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (214, 'HU', 'Hungary', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (215, 'IE', 'Ireland', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (216, 'LU', 'Luxembourg', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (217, 'RU', 'Russia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (218, 'AZ', 'Azerbaijan', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (219, 'CZ', 'Czech Republic', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (220, 'DK', 'Denmark', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (221, 'IM', 'Isle of Man', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (222, 'GG', 'Guernsey', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (223, 'SK', 'Slovakia', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (224, 'MD', 'Moldova', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (225, 'FO', 'Faroe Islands', 'COUNTRY', NULL, 4);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (226, 'TF', 'French Southern and Antarctic Lands', 'COUNTRY', NULL, 5);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (227, 'AQ', 'Antarctica', 'COUNTRY', NULL, 5);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (228, 'CK', 'Cook Islands', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (229, 'NU', 'Niue', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (230, 'FJ', 'Fiji', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (231, 'PF', 'French Polynesia', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (232, 'WF', 'Wallis and Futuna', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (233, 'PN', 'Pitcairn Islands', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (234, 'NF', 'Norfolk Island', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (235, 'TK', 'Tokelau', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (236, 'CX', 'Christmas Island', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (237, 'NC', 'New Caledonia', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (238, 'PG', 'Papua New Guinea', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (239, 'KI', 'Kiribati', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (240, 'TV', 'Tuvalu', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (241, 'NZ', 'New Zealand', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (242, 'WS', 'Samoa', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (243, 'NR', 'Nauru', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (244, 'SB', 'Solomon Islands', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (245, 'CC', 'Cocos (Keeling) Islands', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (246, 'MH', 'Marshall Islands', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (247, 'AU', 'Australia', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (248, 'AU-JB', 'Jervis Bay Territory', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (249, 'AU-ACT', 'Australian Capital Territory', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (250, 'AU-SA', 'South Australia', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (251, 'AU-VIC', 'Victoria', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (252, 'AU-NSW', 'New South Wales', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (253, 'AU-WA', 'Western Australia', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (254, 'AU-QLD', 'Queensland', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (255, 'AU-NT', 'Northern Territory', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (256, 'AU-TAS', 'Tasmania', 'STATE', 247, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (257, 'AS', 'American Samoa', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (258, 'VU', 'Vanuatu', 'COUNTRY', NULL, 6);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (259, 'SOL', 'Somaliland', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (260, 'SD', 'Sudan', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (261, 'TN', 'Tunisia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (262, 'EH', 'Western Sahara', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (263, 'CG', 'Congo', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (264, 'EG', 'Egypt', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (265, 'GW', 'Guinea Bissau', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (266, 'SL', 'Sierra Leone', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (267, 'ZM', 'Zambia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (268, 'ZW', 'Zimbabwe', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (269, 'GQ', 'Equatorial Guinea', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (270, 'GN', 'Guinea', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (271, 'TZ', 'Tanzania', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (272, 'TD', 'Chad', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (273, 'GM', 'Gambia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (274, 'RE', 'Reunion', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (275, 'TG', 'Togo', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (276, 'NG', 'Nigeria', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (277, 'BW', 'Botswana', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (278, 'GA', 'Gabon', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (279, 'DJ', 'Djibouti', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (280, 'KM', 'Comoros', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (281, 'ST', 'Sao Tome and Principe', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (282, 'NA', 'Namibia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (283, 'CI', 'Cote d''Ivoire', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (284, 'LS', 'Lesotho', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (285, 'CD', 'Democratic Republic of the Congo', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (286, 'SO', 'Somalia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (287, 'MU', 'Mauritius', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (288, 'BI', 'Burundi', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (289, 'MW', 'Malawi', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (290, 'NE', 'Niger', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (291, 'LR', 'Liberia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (292, 'LY', 'Libya', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (293, 'IO', 'British Indian Ocean Territory', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (294, 'RW', 'Rwanda', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (295, 'UG', 'Uganda', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (296, 'SH', 'Saint Helena', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (297, 'SZ', 'Swaziland', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (298, 'YT', 'Mayotte', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (299, 'KE', 'Kenya', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (300, 'MZ', 'Mozambique', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (301, 'SN', 'Senegal', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (302, 'BF', 'Burkina Faso', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (303, 'MA', 'Morocco', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (304, 'CF', 'Central African Republic', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (305, 'ZA', 'South Africa', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (306, 'SS', 'South Sudan', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (307, 'ER', 'Eritrea', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (308, 'ET', 'Ethiopia', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (309, 'GH', 'Ghana', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (310, 'CM', 'Cameroon', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (311, 'DZ', 'Algeria', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (312, 'ML', 'Mali', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (313, 'BJ', 'Benin', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (314, 'AO', 'Angola', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (315, 'SC', 'Seychelles', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (316, 'MG', 'Madagascar', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (317, 'TO', 'Tonga', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (318, 'MR', 'Mauritania', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (319, 'CV', 'Cape Verde', 'COUNTRY', NULL, 7);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (320, 'GB-ENG', 'England', 'COUNTRY', 178, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (321, 'GB-NIR', 'Northern Ireland', 'PROVINCE', 178, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (322, 'GB-SCT', 'Scotland', 'COUNTRY', 178, NULL);
INSERT INTO geoareas (id, iso_key, name, geo_area_type, parent_id, region_id) VALUES (323, 'GB-WLS', 'Wales', 'COUNTRY', 178, NULL);

--
-- Name: geoareas_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('geoareas_id_seq', 323, true);


--
-- Data for Name: regions; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO regions (id, name) VALUES (1, 'North America');
INSERT INTO regions (id, name) VALUES (2, 'Asia');
INSERT INTO regions (id, name) VALUES (3, 'South America');
INSERT INTO regions (id, name) VALUES (4, 'Europe');
INSERT INTO regions (id, name) VALUES (5, 'Antarctica');
INSERT INTO regions (id, name) VALUES (6, 'Oceania');
INSERT INTO regions (id, name) VALUES (7, 'Africa');


--
-- Name: regions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('regions_id_seq', 7, true);


--
-- Data for Name: selection_logs; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO selection_logs (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (10, '2017-01-26', '2017-01-26 10:15:05.846', 'LIVED', 169, 2);
INSERT INTO selection_logs (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (11, '2017-01-26', '2017-01-26 10:15:19.16', 'WANT', 92, 2);
INSERT INTO selection_logs (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (12, '2017-01-26', '2017-01-26 10:15:19.175', 'WANT', 82, 2);


--
-- Name: selection_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('selection_logs_id_seq', 12, true);


--
-- Data for Name: selections; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO selections (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (8, '2017-01-26', '2017-01-26 10:15:05.846', 'LIVED', 169, 2);
INSERT INTO selections (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (9, '2017-01-26', '2017-01-26 10:15:19.16', 'WANT', 92, 2);
INSERT INTO selections (id, date, "timestamp", selection_type, geo_area_id, user_id) VALUES (10, '2017-01-26', '2017-01-26 10:15:19.175', 'WANT', 82, 2);


--
-- Name: selections_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('selections_id_seq', 10, true);


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (5, '2017-01-26', 'iOS', '123', '2017-01-26 10:14:22.173', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.zlfpqMUcWHCqVBb_E0sihXgE4ACp54Q1QcBr9kT9odlcUyZnyIjM_LiMCBhBvMwbl2s-vZOsaGvqAC6fw2iJ-g', 2);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (6, '2017-01-26', 'iOS', '123', '2017-01-26 10:46:42.113', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.kGc5NEj4QNEp9MU1pYeKwHNAKxfA1FOc7RMwsdx4q-aREw68HFTn8b32-IooDXLfNbWiXVS46aK3jHaviy24FA', 2);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (7, '2017-01-26', 'iOS', '123', '2017-01-26 10:49:24.448', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.g-ArtpA3hL6Xq3lWelAfzCvHd4O_ox0-LPZBWcw1-gpfL3zHqfFcdAZjOLEmNT9vp31DvVpsHLjX7Kn0xT_8cw', 2);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (8, '2017-01-26', 'iOS', '123', '2017-01-26 10:51:16.693', 'eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************.SPmsf929bwpIGBYZhFR8WpNmNRpML8kmrU921lDemp2XgNrrcMCwDqZ4OeWNDh9wNvort4JZvDDfVSwckI3auA', 1);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (9, '2017-01-27', 'iOS', '123', '2017-01-27 09:17:49.714', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.SVDC8YBCV116pLNswH9nnjAPPAQVToXHhEwmZzDQ3U3ZxBOpDxgtFKRNEAxHo_TlstsHu699kHwh9vWMTjCFXQ', 2);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (10, '2017-01-27', 'iOS', '123', '2017-01-27 09:24:38.332', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.qCECiK6mKt5vCG3IBf2y8opmuuHnfMUoFGoBezw3ko_GHLP4Iv4JBXz8Me3No-zyTqpzoxuSKp3s5yAQb_RQ-w', 2);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (11, '2017-01-27', 'iOS', '123', '2017-01-27 09:24:53.882', 'eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************.MyBVyiUa14_vOTPIaPdDp9qN9etNtQkSMmAdUlV_D6rCt-LiZ_hsnUi8zT4zCP0w8SteZ3fLFPFlEAmg-j1XNQ', 1);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (12, '2017-01-27', 'iOS', '123', '2017-01-27 10:38:47.044', 'eyJhbGciOiJIUzUxMiJ9.******************************************************************************************************************.GBeoV9SBof70GaEMA1RjabxeNxeBo3KGDEDG--j940ENP7_yDapPZyBmB3jcSKCh5CDOeWsaolnBtV5ysuAh5w', 1);
INSERT INTO sessions (id, date, os, version, start_date_time, token, user_id) VALUES (13, '2017-01-27', 'iOS', 'string', '2017-01-27 10:47:12.301', 'eyJhbGciOiJIUzUxMiJ9.****************************************************************************************************************.nbKLSPtchD9DDV7z0qIJcWVEKkfHTDQmwVbUUfVh-DmtkmzSxulEcjuagbkKXmwhUd8pioHwfn9kgHKFFj4DXw', 2);


--
-- Name: sessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('sessions_id_seq', 13, true);


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO users (id, enabled, last_password_reset_date, password, username) VALUES (1, true, '2017-01-01 00:00:00', '$2a$06$kThj8/U0Z5YP3vSvlxawPOPnA3Pn4ab0nrF0.xG8UfjoX5YbxTOV6', '<EMAIL>');
INSERT INTO users (id, enabled, last_password_reset_date, password, username) VALUES (2, true, '2017-01-01 00:00:00', '$2a$06$ZoaL5o.tT8dr5vrzNkM9kO8Ga7LxCb1xQUBybNh3LEipzTReslONC', '<EMAIL>');


--
-- Data for Name: users_authorities; Type: TABLE DATA; Schema: public; Owner: visited
--

INSERT INTO users_authorities (user_id, authority_id) VALUES (1, 1);
INSERT INTO users_authorities (user_id, authority_id) VALUES (1, 2);
INSERT INTO users_authorities (user_id, authority_id) VALUES (2, 1);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: visited
--

SELECT pg_catalog.setval('users_id_seq', 2, true);


--
-- Name: authorities_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY authorities
  ADD CONSTRAINT authorities_pkey PRIMARY KEY (id);


--
-- Name: geoareas_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY geoareas
  ADD CONSTRAINT geoareas_pkey PRIMARY KEY (id);


--
-- Name: regions_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY regions
  ADD CONSTRAINT regions_pkey PRIMARY KEY (id);


--
-- Name: selection_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY selection_logs
  ADD CONSTRAINT selection_logs_pkey PRIMARY KEY (id);


--
-- Name: selections_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY selections
  ADD CONSTRAINT selections_pkey PRIMARY KEY (id);


--
-- Name: sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY sessions
  ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: uk_lsbxksqjfhn20tnw8a2mys1r; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY geoareas
  ADD CONSTRAINT uk_lsbxksqjfhn20tnw8a2mys1r UNIQUE (iso_key);


--
-- Name: uk_r43af9ap4edm43mmtq01oddj6; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY users
  ADD CONSTRAINT uk_r43af9ap4edm43mmtq01oddj6 UNIQUE (username);


--
-- Name: users_pkey; Type: CONSTRAINT; Schema: public; Owner: visited; Tablespace:
--

ALTER TABLE ONLY users
  ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: fk39ndyp2hgxugqhviokhdhc9jv; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY geoareas
  ADD CONSTRAINT fk39ndyp2hgxugqhviokhdhc9jv FOREIGN KEY (parent_id) REFERENCES geoareas(id);


--
-- Name: fkbqws01fqf2g4iskg9q2wlm8fu; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY selections
  ADD CONSTRAINT fkbqws01fqf2g4iskg9q2wlm8fu FOREIGN KEY (geo_area_id) REFERENCES geoareas(id);


--
-- Name: fkdsfxx5g8x8mnxne1fe0yxhjhq; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY users_authorities
  ADD CONSTRAINT fkdsfxx5g8x8mnxne1fe0yxhjhq FOREIGN KEY (authority_id) REFERENCES authorities(id);


--
-- Name: fki7mkwwlprsorp9wtld4814qwp; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY selection_logs
  ADD CONSTRAINT fki7mkwwlprsorp9wtld4814qwp FOREIGN KEY (geo_area_id) REFERENCES geoareas(id);


--
-- Name: fkmpa8tfbevekmyh5dkig0rxvih; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY geoareas
  ADD CONSTRAINT fkmpa8tfbevekmyh5dkig0rxvih FOREIGN KEY (region_id) REFERENCES regions(id);


--
-- Name: fkn6mvkl9x4canghlwin7agts2k; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY selection_logs
  ADD CONSTRAINT fkn6mvkl9x4canghlwin7agts2k FOREIGN KEY (user_id) REFERENCES users(id);


--
-- Name: fkq3lq694rr66e6kpo2h84ad92q; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY users_authorities
  ADD CONSTRAINT fkq3lq694rr66e6kpo2h84ad92q FOREIGN KEY (user_id) REFERENCES users(id);


--
-- Name: fkqw682uktl1rqv58ujf1llo25x; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY selections
  ADD CONSTRAINT fkqw682uktl1rqv58ujf1llo25x FOREIGN KEY (user_id) REFERENCES users(id);


--
-- Name: fkruie73rneumyyd1bgo6qw8vjt; Type: FK CONSTRAINT; Schema: public; Owner: visited
--

ALTER TABLE ONLY sessions
  ADD CONSTRAINT fkruie73rneumyyd1bgo6qw8vjt FOREIGN KEY (user_id) REFERENCES users(id);
