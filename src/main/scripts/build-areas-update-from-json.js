const areas = require('../../../ext/areas.json');
const fs = require('fs');

let finalLabelSQLFileContents = '';
let finalBoundsSQLFileContents = '';
const insertQueryTemplate = `insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), ( select id from geoareas where iso_key = '$geoAreaIso' ), '$latitude', '$longitude', $resolution);`;
const updateQueryTemplate = `update geoareas set bounding_min_long = '$bounds_initial_x', bounding_min_lat = '$bounds_initial_y', bounding_max_long = '$bounds_final_x', bounding_max_lat = '$bounds_final_y' where iso_key = '$geoAreaIso';`;

for (const area of areas) {
    createQueriesForArea(area);
}

fs.writeFile("../sql/labels.sql", finalLabelSQLFileContents, function(err) {
    if(err) {
        return console.log(err);
    }

    console.log("File 'labels.sql' saved!");
});

fs.writeFile("../sql/bounds.sql", finalBoundsSQLFileContents, function(err) {
    if(err) {
        return console.log(err);
    }

    console.log("File 'bounds.sql' saved!");
});

function createQueriesForArea(area) {
    //generate labels for each area
    for (const label of area.labels) {
        const query =
            insertQueryTemplate.replace('$geoAreaIso', area.iso)
                .replace('$latitude', label.coordinate.lat)
                .replace('$longitude', label.coordinate.long)
                .replace('$resolution', label.resolution);
        finalLabelSQLFileContents += query + '\n';
    }

    //generate bounds values for each area
    const query =
        updateQueryTemplate.replace('$geoAreaIso', area.iso)
            .replace('$bounds_initial_x', area.bounds[0])
            .replace('$bounds_initial_y', area.bounds[1])
            .replace('$bounds_final_x', area.bounds[2])
            .replace('$bounds_final_y', area.bounds[3]);
    finalBoundsSQLFileContents += query + '\n';

    if (area.subdivisions) {
        for (const subdivision of area.subdivisions) {
            createQueriesForArea(subdivision);
        }
    }
}