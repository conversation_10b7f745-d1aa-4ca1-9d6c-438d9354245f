#!/bin/bash

# Create the AWS CLI configuration with the credentials to pull the app WAR file from s3
mkdir ~/.aws
touch ~/.aws/credentials
echo "[default]
aws_access_key_id = ********************
aws_secret_access_key = gvwDDZfTu7SB7kg47oE6gy/84Hin2WEJcRiP0nAE" > ~/.aws/credentials
touch ~/.aws/config
echo "[default]
region = us-east-1" > ~/.aws/config

# Installing base libs
sudo yum update -y
sudo yum install git -y

# Downloading Oracle's JAVA 8 from S3
aws s3 cp s3://installation.visited-backend/libs/jdk8-linux-x64.rpm .

# Installing Java SDK 8
sudo rpm -i jdk8-linux-x64.rpm
export JAVA_HOME=/usr/java/default
echo "export JAVA_HOME=/usr/java/default" >> .bash_profile

# Removing old java version
sudo yum remove java-1.7.0-openjdk -y

#Configuring redirection in iptables for the port 8080
sudo iptables -t nat -A PREROUTING -p tcp --dport 80 -j REDIRECT --to-port 8080
sudo iptables -t nat -I OUTPUT -p tcp -d 127.0.0.1 --dport 80 -j REDIRECT --to-ports 8080

# Fetching app resources from the s3 bucket
aws s3 cp s3://artifacts.visited-backend/release/com/arrivinginhighheels/visited-backend/1.0.0/visited-backend-1.0.0.jar .

# Create a visited user to run the app as a service
sudo useradd visited

# visited's login shell disabled
sudo chsh -s /sbin/nologin visited

# Changing permissions to the visited user
sudo chown visited:visited visited-backend-1.0.0.jar
sudo chmod 500 visited-backend-1.0.0.jar

#configuring the service
sudo mkdir /var/visited-backend
sudo chown visited:visited /var/visited-backend/
sudo mv visited-backend-1.0.0.jar /var/visited-backend/
sudo mkdir /var/run/visited-backend/
sudo chown visited:visited /var/run/visited-backend/
sudo touch /var/log/visited-backend.log
sudo chown visited:visited /var/log/visited-backend.log

# Installing the configuration and service files
echo "RUN_ARGS=--spring.profiles.active=aws" > /var/visited-backend/visited-backend-1.0.0.conf
sudo chown visited:visited /var/visited-backend/visited-backend-1.0.0.conf
sudo ln -s /var/visited-backend/visited-backend-1.0.0.jar /etc/init.d/visited-backend
sudo chown -h visited:visited /etc/init.d/visited-backend

# Run the service
sudo service visited-backend start

#Configuring the dynamic DNS service
#sudo yum-config-manager --enable epel
#sudo yum install -y noip
#sudo noip2 -C
##TODO prompt... just creates a config file... maybe I don't really need this -C option above
#sudo chkconfig noip on
#sudo service noip start

# Configures the services to run on server boot
sudo chkconfig visited-backend on
