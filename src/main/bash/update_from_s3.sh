#!/usr/bin/env bash

# copying file from s3 - expects a configured environment
#wget https://corretto.aws/downloads/latest/amazon-corretto-17-x64-linux-jdk.tar.gz
#curl -LO https://corretto.aws/downloads/latest/amazon-corretto-17-x64-linux-jdk.tar.gz
aws s3 cp s3://artifacts.visited-backend/release/com/arrivinginhighheels/visited-backend/1.1.0/visited-backend-1.1.0.jar .

# stopping the service
sudo service visited-backend stop

# file permissions and ownsership
sudo chown visited:visited visited-backend-1.1.0.jar
sudo chmod 500 visited-backend-1.1.0.jar

# move the file to the correct path
sudo mv visited-backend-1.1.0.jar /var/visited-backend/

# restart service
sudo service visited-backend start