#!/bin/bash

keyName=$1

if [[ -n "$keyName" ]]; then
    echo "Using '$keyName' as key name"
else
    echo "You must supply the key file as an argument. Ex.: './create_instance_aws.sh myKeyName'"
    exit 1
fi

echo using AWS_PROFILE=aihh

echo ----- Creating AWS instance
InstanceID=`AWS_PROFILE=aihh aws ec2 run-instances \
                                    --region us-east-1 \
                                    --key-name $keyName \
                                    --instance-type t2.micro \
                                    --image-id ami-0b33d91d \
                                    --subnet-id subnet-ac95da81 \
                                    --associate-public-ip-address \
                                    --security-group-ids sg-ab139ed7 \
                                    --user-data file://install_aws.sh | jq -r ".Instances[0].InstanceId"`

echo ----- Created AWS instance with id=$InstanceID

echo ----- Adding tag AIHH-Visited-Backend-VM to the instance
AWS_PROFILE=aihh aws ec2 create-tags     --resources $InstanceID \
                                         --tags "Key=Name,Value=AIHH-Visited-Backend-VM"

echo ----- Instance provisioned.

echo "----- Waiting for the IP"
sleep 30s

echo "----- Capturing instance's IP"
IP=`AWS_PROFILE=aihh aws ec2 describe-instances --instance-ids $InstanceID | jq -r ".Reservations[0].Instances[0].PublicIpAddress"`

echo IP: $IP
echo To connect to the vm instance: ssh $IP -l ec2-user -i $keyName.pem
