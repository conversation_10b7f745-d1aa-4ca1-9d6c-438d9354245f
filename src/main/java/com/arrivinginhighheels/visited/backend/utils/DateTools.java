package com.arrivinginhighheels.visited.backend.utils;

import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class DateTools {
    private static final String DEFAULT_TIMESTAMP = "01/01/2017 00:00:00";

    public Date nullSafeLastModifiedTime(Date date) {
        if (date != null) {
            return date;
        }

        return defaultLastModificationTime();
    }

    public Date defaultLastModificationTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
        try {
            return formatter.parse(DEFAULT_TIMESTAMP);
        } catch (ParseException e) {
            return new Date(); //failsafe... should never occur.
        }
    }
}
