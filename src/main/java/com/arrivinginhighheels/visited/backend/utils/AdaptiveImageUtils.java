package com.arrivinginhighheels.visited.backend.utils;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import org.springframework.stereotype.Service;

@Service
public class AdaptiveImageUtils {
    public String getResolution(AdaptiveImage image, double resolution) {
        if (image == null) {
            return null;
        }

        try {
            if (resolution >= 4) {
                return image.getFourUrl();
            } else if (resolution >= 3) {
                return image.getThreeUrl();
            } else if (resolution >= 2) {
                return image.getTwoUrl();
            } else if (resolution >= 1.5) {
                return image.getOnePointFiveUrl();
            } else {
                return image.getOneUrl();
            }
        } catch (Exception e) {
            return image.getOneUrl();
        }
    }
}
