package com.arrivinginhighheels.visited.backend.utils;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;

public class UrlBuilder {
    private final StringBuilder query;
    private String host = "";
    private String schema = "https";
    private String path = "";

    public UrlBuilder() {
        query = new StringBuilder();
    }

    public UrlBuilder schema(String schema) {
        this.schema = schema;
        return this;
    }

    public UrlBuilder host(String host) {
        this.host = host;
        return this;
    }

    public UrlBuilder path(String path) {
        this.path = path;
        return this;
    }

    public UrlBuilder addQueryParameter(String key, String value) {
        if (query.length() > 0) {
            query.append("&");
        }

        query.append(key);
        query.append("=");
        query.append(value);
        return this;
    }

    public  URL build() {
        try {
            return new URI(schema, host, path, query.toString(), null).toURL();
        } catch (MalformedURLException e) {
            e.printStackTrace();
            return null;
        } catch (URISyntaxException e) {
            e.printStackTrace();
            return null;
        }
    }
}
