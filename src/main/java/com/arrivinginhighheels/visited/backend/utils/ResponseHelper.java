package com.arrivinginhighheels.visited.backend.utils;

import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Enumeration;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static org.springframework.http.HttpHeaders.IF_NONE_MATCH;

@Component
public class ResponseHelper {
    public static final CacheControl standardCacheLength = CacheControl.maxAge(1, TimeUnit.DAYS);

    public <T> ResponseEntity<T> standardCacheableResponse(T value) {
        return ResponseEntity
                .ok()
                .cacheControl(standardCacheLength)
                .body(value);
    }

    public <T> ResponseEntity<T> doNotCacheResponse(T value) {
        return ResponseEntity
                .ok()
                .cacheControl(CacheControl.noCache())
                .body(value);
    }

    public <T> ResponseEntity<T> wrap(final HttpServletRequest request,
                                      final Date lastModificationTime,
                                      final Function<HttpServletRequest, T> fnBuildResponse) {

        return wrap(request, lastModificationTime, Optional.empty(), fnBuildResponse);
    }

    public <T> ResponseEntity<T> wrap(final HttpServletRequest request,
                                      final Date lastModificationTime,
                                      final CacheControl cacheControl,
                                      final Function<HttpServletRequest, T> fnBuildResponse) {
        return wrap(request, lastModificationTime, Optional.ofNullable(cacheControl), fnBuildResponse);
    }

    private <T> ResponseEntity<T> wrap(final HttpServletRequest request,
                                      final Date lastModificationTime,
                                      final Optional<CacheControl> cacheControl,
                                      final Function<HttpServletRequest, T> fnBuildResponse) {

        final String hashedValueOfTheETag = ETagHashUtils.buildHashedValueForTheETagFromADate(lastModificationTime);

        if (thereIsAnETagMatchInTheRequest(request, hashedValueOfTheETag)) {
            return ResponseEntity.status(HttpStatus.NOT_MODIFIED).build();
        }

        var entity = ResponseEntity.ok()
                .eTag(hashedValueOfTheETag)
                .header("X-Last-Modified-Etag", hashedValueOfTheETag); // Using custom header since Spring-Boot seems to override the default eTag header

        if (cacheControl.isPresent()) {
            entity = entity.cacheControl(cacheControl.get());
        }

        return entity.body(fnBuildResponse.apply(request));
    }

    private boolean thereIsAnETagMatchInTheRequest(final HttpServletRequest request, final String hashedValueOfTheETag) {
        boolean thereIsATagMatching = false;
        final Enumeration<String> ifNoneMatchHeaders = request.getHeaders(IF_NONE_MATCH);
        if (ifNoneMatchHeaders != null && ifNoneMatchHeaders.hasMoreElements()) {
            while (ifNoneMatchHeaders.hasMoreElements()) {
                final String ifNoneMatchHeader = ifNoneMatchHeaders.nextElement();
                if (ifNoneMatchHeader.equalsIgnoreCase(hashedValueOfTheETag)) {
                    thereIsATagMatching = true;
                    break;
                }
            }
        }
        return thereIsATagMatching;
    }
}
