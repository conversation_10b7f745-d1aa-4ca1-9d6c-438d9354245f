package com.arrivinginhighheels.visited.backend.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.format.datetime.DateFormatter;

import java.util.Date;
import java.util.Locale;

public class ETagHashUtils {

    public static String buildHashedValueForTheETagFromADate(Date theDate) {
        if (theDate == null) {
            theDate = new Date();
        }
        String dateAsString = new DateFormatter().print(theDate, Locale.US); //uses a default locale to simplify things
        String languageName = LocaleContextHolder.getLocale().getDisplayName();
        return DigestUtils.md5Hex(languageName + " - " + dateAsString);
    }

}
