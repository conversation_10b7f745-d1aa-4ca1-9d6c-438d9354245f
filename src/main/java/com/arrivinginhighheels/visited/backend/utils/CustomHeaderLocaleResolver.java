package com.arrivinginhighheels.visited.backend.utils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.lang.NonNull;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.LocaleResolver;

import java.util.Locale;

public class CustomHeaderLocaleResolver implements LocaleResolver {

    @Override
    @NonNull
    public Locale resolveLocale(HttpServletRequest request) {
        String acceptLanguage = request.getHeader("Accept-Language");

        if (!StringUtils.hasText(acceptLanguage)) {
            return Locale.getDefault();
        }

        // Normalize to dashes and take only the first language code
        String normalized = acceptLanguage.replace('_', '-').split(",")[0].trim();

        // Check for zh-Hant or zh-Hans even if there's a region
        String lower = normalized.toLowerCase();
        if (lower.startsWith("zh-hant")) {
            return Locale.forLanguageTag("zh-Hant");
        } else if (lower.startsWith("zh-hans")) {
            return Locale.forLanguageTag("zh-Hans");
        }

        // Otherwise just strip off region/script parts
        String baseLang = normalized.split("-")[0];
        return Locale.forLanguageTag(baseLang);
    }

    @Override
    public void setLocale( HttpServletRequest request, HttpServletResponse response, Locale locale) {
        // Optional, usually no-op
    }
}
