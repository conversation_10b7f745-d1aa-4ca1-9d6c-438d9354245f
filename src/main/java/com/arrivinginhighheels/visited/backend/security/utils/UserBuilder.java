package com.arrivinginhighheels.visited.backend.security.utils;

import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.model.AuthorityName;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.AuthorityRepository;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

@Component
public class UserBuilder {

    private final AuthorityRepository authorityRepository;
    private final PasswordEncoder passwordEncoder;

    public UserBuilder(
            AuthorityRepository authorityRepository,
            PasswordEncoder passwordEncoder
    ) {
        this.authorityRepository = authorityRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public UserDTO createDTOFromUser(User user) {
        UserDTO dto = new UserDTO();
        dto.setEmail(user.getUsername());
        dto.setCreationDate( user.getCreationDate());
        dto.setUnsubscribed(user.getUnsubscribed());
        dto.setEnabled(user.isEnabled());
        dto.setAuthorities(
                user.getAuthorities()
                    .stream()
                    .map(granted -> AuthorityName.valueOf(granted.getAuthority()))
                    .collect(toList())
        );

        return dto;
    }

    public User createUserFromDTO(AuthRequest userDTO) {
        return createUserFromDTOWithAuthorities(userDTO,
                                                Stream.of(AuthorityName.ROLE_USER).collect(toList()));
    }

    public User createUserFromDTOWithAuthorities(AuthRequest userDTO, List<AuthorityName> authorities) {
        var user = new User();
        replaceInfoFromDTO(user, userDTO, authorities);

        // Use the email as the password if none is provided
        var password = userDTO.getPassword();
        if (password == null) {
            password = userDTO.getEmail();
        }

        var encoded = passwordEncoder.encode(password);
        user.setPassword(encoded);

        user.setLastPasswordResetDate(new Date());
        user.setEnabled(true);

        return user;
    }

    public void replaceInfoFromDTO(User user, AuthRequest newUserDTO, List<AuthorityName> authorities) {
        user.setUsername(newUserDTO.getEmail());
        setAuthoritiesToTheUser(user, authorities);
    }

    private void setAuthoritiesToTheUser(User user, List<AuthorityName> authorities) {
        user.setUserAuthorities(
                authorities
                        .stream()
                        .map(authorityRepository::findByName)
                        .collect(toList()));
    }
}
