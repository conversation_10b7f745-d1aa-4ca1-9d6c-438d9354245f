package com.arrivinginhighheels.visited.backend.security.utils;

import com.arrivinginhighheels.visited.backend.model.OSType;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

@Component
public class LoggedInUserUtil {

    @Value("${jwt.header}")
    private String tokenHeader;

    private final JwtTokenUtil jwtTokenUtil;

    private final UserDetailsService userDetailsService;

    public LoggedInUserUtil(JwtTokenUtil jwtTokenUtil, UserDetailsService userDetailsService) {
        this.jwtTokenUtil = jwtTokenUtil;
        this.userDetailsService = userDetailsService;
    }

    public OSType getCurrentOperatingSystem(HttpServletRequest request) {
        final String platform = request.getHeader("x-app-platform");
        return OSType.valueOf(platform);
    }

    public User getLoggedInUser(HttpServletRequest request) {
        String token = request.getHeader(tokenHeader);
        if (token == null || token.isEmpty()) {
            return null;
        }

        String username = jwtTokenUtil.getUsernameFromToken(token);
        if (username == null) {
            return null;
        }

        return (User) userDetailsService.loadUserByUsername(username);
    }
}
