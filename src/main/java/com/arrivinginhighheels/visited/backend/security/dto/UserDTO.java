package com.arrivinginhighheels.visited.backend.security.dto;

import com.arrivinginhighheels.visited.backend.model.AuthorityName;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * DTO for the User, because the password is needed, and the Entity
 * has @JsonIgnore's.
 * Makes the API responses simpler to read.
 */
@Setter
@Getter
@NoArgsConstructor
public class UserDTO {
    @NotNull
    private String email;

    private boolean enabled = true;

    private List<AuthorityName> authorities = new ArrayList<>();

    private Date creationDate;

    private boolean unsubscribed;

    public UserDTO(String email, Date creationDate) {
        this.email = email;
        this.creationDate = creationDate;
    }

    @Override
    public String toString() {
        return "UserDTO{" +
                "email='" + email + '\'' +
                ", creationDate=" + creationDate +
                ", enabled=" + enabled +
                ", authorities=" + authorities +
                '}';
    }

}
