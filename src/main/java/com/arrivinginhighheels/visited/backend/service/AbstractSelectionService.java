package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import org.springframework.beans.factory.annotation.Autowired;


abstract public class AbstractSelectionService {

    @Autowired
    public GeoAreaRepository geoAreaRepository;

    /**
     * Get the Geo Area by its ISO key, if it exists. If it doesn't, throw an exception.
     *
     * @param geoAreaIsoKey the GeoArea's ISO key.
     * @return GeoArea, if it exists
     * @throws GeoAreaNotFoundException in case there's no GeoArea with the $code{geoAreaIsoKey} registered.
     */
    public GeoArea getGeoAreaVerifyingIfItExists(final String geoAreaIsoKey) {

        return geoAreaRepository.findByIsoKey(geoAreaIsoKey)
                .orElseThrow(() -> new GeoAreaNotFoundException(geoAreaIsoKey));
    }
}
