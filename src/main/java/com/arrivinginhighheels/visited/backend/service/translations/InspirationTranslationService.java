package com.arrivinginhighheels.visited.backend.service.translations;

import com.arrivinginhighheels.visited.backend.model.Inspiration;
import com.arrivinginhighheels.visited.backend.model.InspirationTranslation;
import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationTranslationRepository;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

@Service
public class InspirationTranslationService extends AbstractTranslationService {

    private final InspirationTranslationRepository repository;

    public InspirationTranslationService(InspirationTranslationRepository repository) {
        this.repository = repository;
    }

    public String getTranslation(Inspiration inspiration) {
        final SupportedLanguage language = getCurrentLanguage();
        if (language == null || language.isEnglish()) {
            return inspiration.getName();
        }

        final InspirationTranslation translation = repository.findByInspirationAndSupportedLanguage(inspiration, language);
        return translation == null ? inspiration.getName() : translation.getName();
    }

    public Map<Inspiration, String> getAllTranslationsForInspirations(List<Inspiration> inspirations) {
        final SupportedLanguage language = getCurrentLanguage();
        final List<Long> ids = inspirations.stream().map(Inspiration::getId).collect(toList());
        final List<InspirationTranslation> translations = repository.findBySupportedLanguageAndInspirationIdIn(language, ids);
        final Map<Inspiration, String> translationsLookup = new HashMap<>();

        for (final Inspiration inspiration : inspirations) {
            InspirationTranslation translation = null;
            for (final InspirationTranslation queried : translations) {
                if (queried.getInspiration().equals(inspiration)) {
                    translation = queried;
                    break;
                }
            }

            if (translation == null) {
                continue;
            }

            translations.remove(translation);
            translationsLookup.put(inspiration, translation.getName());
        }

        return translationsLookup;
    }
}
