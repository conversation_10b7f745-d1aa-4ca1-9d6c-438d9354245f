package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.features.authentication.BrandAmbassadorRequest;
import com.arrivinginhighheels.visited.backend.features.emails.EmailService;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class ContactUsService {
    private final EmailService emailService;

    public ContactUsService(EmailService emailService) {
        this.emailService = emailService;
    }

    public void becomeBrandAmbassador(BrandAmbassadorRequest request) {
        final var builder = new StringBuilder();
        builder.append("<html><body>");
        builder.append("<div>Name: ").append(request.name()).append("</div><br>");
        builder.append("<div>Email: ").append(request.email()).append("</div><br>");
        builder.append("<div>Country: ").append(request.country()).append("</div><br>");
        builder.append("<br>");

        if (request.socialMedia() != null) {
            for (final var socialMedia : request.socialMedia()) {
                builder.append(socialMedia.platform()).append("<br>");
                builder.append(socialMedia.handle()).append("<br>");
                builder.append(socialMedia.followers()).append("<br>");
                builder.append("<br>");
            }
        }

        final var other = request.otherSocialMedia();
        if (other != null && !other.isEmpty()) {
            builder.append(other).append("<br>");
        }

        final var anythingElse = request.anythingElse();
        if (anythingElse != null && !anythingElse.isEmpty()) {
            builder.append(anythingElse).append("<br>");
        }

        builder.append("</body></html>");

        emailService.sendSimpleMessage(
                Optional.of("Visited App"),
                Optional.of("<EMAIL>"),
                "<EMAIL>",
                "New Brand Ambassador Request",
                builder.toString(),
                false
        );
    }

}
