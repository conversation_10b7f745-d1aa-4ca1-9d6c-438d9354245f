package com.arrivinginhighheels.visited.backend.service.translations;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.ExperienceTranslation;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceTranslationRepository;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.stereotype.Service;

@Service
public class ExperiencesTranslationService extends AbstractTranslationService {

    private final ExperienceTranslationRepository experienceTranslationRepository;

    public ExperiencesTranslationService(ExperienceTranslationRepository experienceTranslationRepository) {
        this.experienceTranslationRepository = experienceTranslationRepository;
    }

    public String getExperienceName(final Experience e) {
        final SupportedLanguage lang = getCurrentLanguage();
        if (lang == null || lang.isEnglish()) {
            return e.getName();
        }

        final ExperienceTranslation experienceTranslation = experienceTranslationRepository.findByExperienceAndSupportedLanguage(e, lang);
        if (experienceTranslation == null) {
            return e.getName();
        }

        return experienceTranslation.getName();
    }
}
