package com.arrivinginhighheels.visited.backend.service.translations;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaMustSee;
import com.arrivinginhighheels.visited.backend.model.GeoAreaMustSeeTranslation;
import com.arrivinginhighheels.visited.backend.model.GeoAreaTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.repository.translations.GeoAreaMustSeeTranslationRepository;
import com.arrivinginhighheels.visited.backend.repository.translations.GeoAreaTranslationRepository;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.stream.Collectors.toList;

@Service
public class AreaTranslationService extends AbstractTranslationService {

    private final GeoAreaTranslationRepository geoAreaTranslationRepository;

    private final GeoAreaMustSeeTranslationRepository geoAreaMustSeeTranslationRepository;

    public AreaTranslationService(GeoAreaTranslationRepository geoAreaTranslationRepository, GeoAreaMustSeeTranslationRepository geoAreaMustSeeTranslationRepository) {
        this.geoAreaTranslationRepository = geoAreaTranslationRepository;
        this.geoAreaMustSeeTranslationRepository = geoAreaMustSeeTranslationRepository;
    }

    public String getAreaName(final GeoArea a) {
        final SupportedLanguage lang = getCurrentLanguage();
        return getAreaName(a, lang);
    }

    public String getAreaName(final GeoArea a, final SupportedLanguage lang) {
        if (lang == null || lang.isEnglish()) {
            return a.getName(); //default
        }

        final GeoAreaTranslation geoAreaTranslation = geoAreaTranslationRepository.findByGeoAreaAndSupportedLanguage(a, lang);
        if (geoAreaTranslation == null) {
            return a.getName(); //again, default
        }

        return geoAreaTranslation.getName();
    }

    public String getMustSeeDescription(final GeoAreaMustSee geoAreaMustSee) {
        final SupportedLanguage lang = getCurrentLanguage();
        if (lang == null || lang.isEnglish()) {
            return geoAreaMustSee.getDescription(); //default
        }

        final GeoAreaMustSeeTranslation geoAreaMustSeeTranslation =
                geoAreaMustSeeTranslationRepository.findByGeoAreaMustSeeAndSupportedLanguage(geoAreaMustSee, lang);
        if (geoAreaMustSeeTranslation == null) {
            return geoAreaMustSee.getDescription();
        }

        return geoAreaMustSeeTranslation.getDescription();
    }

    public List<GeoAreaTranslation> getAllTranslationsForArea(final GeoArea area) {
        return geoAreaTranslationRepository.findAllGeoAreaId(area.getId());
    }

    public Map<GeoArea, String> getAllTranslationsForAreas(final Set<GeoArea> areas) {
        final SupportedLanguage language = getCurrentLanguage();
        final List<Long> ids = areas.stream().map(GeoArea::getId).collect(toList());
        final List<GeoAreaTranslation> translations = geoAreaTranslationRepository.findBySupportedLanguageAndGeoAreaIdIn(language, ids);
        final Map<GeoArea, String> translationsLookup = new HashMap<>();

        for (final GeoArea area : areas) {
            GeoAreaTranslation translation = null;
            for (final GeoAreaTranslation queried : translations) {
                if (queried.getGeoArea().equals(area)) {
                    translation = queried;
                    break;
                }
            }

            if (translation == null) {
                continue;
            }

            translations.remove(translation);
            translationsLookup.put(area, translation.getName());
        }

        return translationsLookup;
    }
}

