package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.dto.RankDTO;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.stereotype.Service;

/**
 * Service for calculating the user Rank
 */
@Service
public class RankService {

    private final UserService userService;

    public RankService(UserService userService) {
        this.userService = userService;
    }

    public RankDTO getUserRank(User user) {

        if (user.getNumberOfAreasVisited() == null) {
            userService.recalculateUsersTotalNumberOfAreasVisited(user);
        }

        Long position =
                user.getRanking() != null
                    ? user.getRanking()
                    : userService.calculateRankForTheUser(user);

        Long numberOfUsers = userService.countUsers();

        return new RankDTO(position, numberOfUsers);
    }

}
