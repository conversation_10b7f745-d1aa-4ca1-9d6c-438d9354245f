package com.arrivinginhighheels.visited.backend.service.translations;

import com.arrivinginhighheels.visited.backend.model.Region;
import com.arrivinginhighheels.visited.backend.model.RegionTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.repository.translations.RegionTranslationRepository;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RegionTranslationService extends AbstractTranslationService {

    private final RegionTranslationRepository regionTranslationRepository;

    public RegionTranslationService(RegionTranslationRepository regionTranslationRepository) {
        this.regionTranslationRepository = regionTranslationRepository;
    }

    public String getRegionName(Region r) {
        SupportedLanguage lang = getCurrentLanguage();

        if (lang == null || lang.isEnglish()) {
            return r.getName(); //default
        }

        RegionTranslation regionTranslation = regionTranslationRepository.findByRegionAndSupportedLanguage(r, lang);
        if (regionTranslation == null) {
            return r.getName();
        }

        return regionTranslation.getName();
    }

    public Map<String, String> getAll(Region region) {
        final List<RegionTranslation> results = regionTranslationRepository.findAllRegionById(region.getId());
        final Map<String, String> translations = new HashMap<>();

        for (final RegionTranslation translation : results) {
            translations.put(translation.getSupportedLanguage().getCode(), translation.getName());
        }

        return translations;
    }
}
