package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.config.Constants.Counters;
import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.UserChangePasswordRequest;
import com.arrivinginhighheels.visited.backend.exception.EmailAlreadyExistsException;
import com.arrivinginhighheels.visited.backend.exception.InsufficientParametersException;
import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.features.cities.UserCitiesRepository;
import com.arrivinginhighheels.visited.backend.features.emails.EmailService;
import com.arrivinginhighheels.visited.backend.features.experiences.UserExperienceAreaRepository;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.features.inspirations.UserInspirationsRepository;
import com.arrivinginhighheels.visited.backend.features.itineraries.UserItineraryRepository;
import com.arrivinginhighheels.visited.backend.features.poster.PrintingRepository;
import com.arrivinginhighheels.visited.backend.features.privacy.PrivacyService;
import com.arrivinginhighheels.visited.backend.features.todoLists.UserTodoListItemRepository;
import com.arrivinginhighheels.visited.backend.model.Counter;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.UserDeletionQueue;
import com.arrivinginhighheels.visited.backend.repository.*;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

/**
 * Business logic class for the users
 */
@Service
public class UserService {

    private static final Logger log = Logger.getLogger("UserService");

    private final UserRepository userRepository;

    private final UserBuilder userBuilder;

    private final CounterRepository counterRepository;

    private final PrivacyService privacyService;

    private final SelectionRepository selectionRepository;

    private final UserBeenCountRepository userBeenCountRepository;

    private final UserDeletionQueueRepository userDeletionQueueRepository;

    private final SessionRepository sessionRepository;

    private final GeoAreaUserNotesRepository geoAreaUserNotesRepository;

    private final UserExperienceAreaRepository userExperienceAreaRepository;

    private final UserScoresRepository userScoresRepository;

    private final UserPurchasesRepository userPurchasesRepository;

    private final UserCitiesRepository userCitiesRepository;

    private final UserInspirationsRepository userInspirationsRepository;

    private final UserTodoListItemRepository userTodoListItemRepository;

    private final PrintingRepository printingRepository;

    private final PasswordEncoder passwordEncoder;

    private final UserItineraryRepository userItineraryRepository;

    private final EmailService emailService;

    private final YamlConfig config;

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration}")
    private Long jwtExpiration;

    public UserService(
            UserDeletionQueueRepository userDeletionQueueRepository,
            UserRepository userRepository,
            UserBuilder userBuilder,
            UserScoresRepository userScoresRepository,
            UserInspirationsRepository userInspirationsRepository,
            CounterRepository counterRepository,
            PrivacyService privacyService,
            SelectionRepository selectionRepository,
            UserBeenCountRepository userBeenCountRepository,
            SessionRepository sessionRepository,
            PasswordEncoder passwordEncoder,
            GeoAreaUserNotesRepository geoAreaUserNotesRepository,
            UserTodoListItemRepository userTodoListItemRepository,
            PrintingRepository printingRepository,
            UserPurchasesRepository userPurchasesRepository,
            UserCitiesRepository userCitiesRepository,
            UserExperienceAreaRepository userExperienceAreaRepository,
            UserItineraryRepository userItineraryRepository,
            EmailService emailService, YamlConfig config) {
        this.userDeletionQueueRepository = userDeletionQueueRepository;
        this.userRepository = userRepository;
        this.userBuilder = userBuilder;
        this.userScoresRepository = userScoresRepository;
        this.userInspirationsRepository = userInspirationsRepository;
        this.counterRepository = counterRepository;
        this.privacyService = privacyService;
        this.selectionRepository = selectionRepository;
        this.userBeenCountRepository = userBeenCountRepository;
        this.sessionRepository = sessionRepository;
        this.passwordEncoder = passwordEncoder;
        this.geoAreaUserNotesRepository = geoAreaUserNotesRepository;
        this.userTodoListItemRepository = userTodoListItemRepository;
        this.printingRepository = printingRepository;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userCitiesRepository = userCitiesRepository;
        this.userExperienceAreaRepository = userExperienceAreaRepository;
        this.userItineraryRepository = userItineraryRepository;
        this.emailService = emailService;
        this.config = config;
    }

    @Transactional
    public User createUser(final AuthRequest userDTO) {

        var email = userDTO.getEmail();
        if (email != null) {
            email = email.toLowerCase();
        }

        if (userRepository.findByUsername(email).isPresent()) {
            throw new EmailAlreadyExistsException();
        }

        final Counter totalUsersCounter = getTotalUsersCounter();
        final long nextTotalUsers = totalUsersCounter.getValue() + 1;

        final var user = userBuilder.createUserFromDTO(userDTO);
        user.setRanking(nextTotalUsers);
        user.setNumberOfAreasVisited(0);
        user.setCreationDate(new Date());

        final var userSaved = userRepository.save(user);

        totalUsersCounter.setValue(nextTotalUsers);
        counterRepository.save(totalUsersCounter);

        return userSaved;
    }

    public void updatePassword(User user, UserChangePasswordRequest request) {
        //TODO: figure out how to correctly compare passwords
//        var encodedOldPassword = passwordEncoder.encode(request.getOldPassword().toLowerCase());
//        if (!user.getPassword().equals(encodedOldPassword)) {
//            throw new RuntimeException("Incorrect original password");
//        }

        var encoded = passwordEncoder.encode(request.getNewPassword());
        user.setPassword(encoded);
        userRepository.save(user);
    }

    public User findUser(final String username) {
        if (username == null) {
            throw new InsufficientParametersException("id");
        }


        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UserNotFoundByUsernameException(username));
    }

    @Transactional
    public void recalculateUsersTotalNumberOfAreasVisited(final User user) {
        final Integer totalNumberOfAreasVisited = userRepository.getTotalNumberOfAreasVisitedForTheUser(user.getId());

        user.setNumberOfAreasVisited(totalNumberOfAreasVisited);

        log.info("Setting the total number of areas visited for user " + user.getUsername() + " to " +
                totalNumberOfAreasVisited);

        userRepository.saveAndFlush(user);
    }

    @Transactional
    public Long calculateRankForTheUser(final User user) {
        log.info("Calculating the ranking for the user " + user.getUsername());
        final Long rankForTheUser = userRepository.getRankForTheUser(user.getId());
        user.setRanking(rankForTheUser);
        userRepository.save(user);

        return user.getRanking();
    }

    @Transactional
    public Long countUsers() {
        final Counter totalUsersCounter = getTotalUsersCounter();

        return totalUsersCounter.getValue();
    }

    private Counter getTotalUsersCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        if (totalUsersCounter == null) {
            final Long totalUsers = userRepository.count();
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
            counterRepository.save(totalUsersCounter);
        }

        return totalUsersCounter;
    }

    @Transactional
    public void deleteUser(final User user) {

        //remove all data relative to the user in the database
        userExperienceAreaRepository.deleteByUser(user);
        userBeenCountRepository.deleteByUser(user);
        userScoresRepository.deleteByUserId(user.getId());
        userPurchasesRepository.deleteByUser(user);
        userTodoListItemRepository.deleteByUser(user);
        userCitiesRepository.deleteByUser(user);
        userInspirationsRepository.deleteByUser(user);
        privacyService.deletePrivacyAgreementForUserId(user);
        printingRepository.deleteByUser(user);
        userItineraryRepository.deleteAllByUser(user);

        try {
            selectionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            sessionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            geoAreaUserNotesRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        //save the user deletion in the queue to remove dirty data from the Selection Logs later
        final UserDeletionQueue userDeletionQueue = new UserDeletionQueue(user.getId());
        userDeletionQueueRepository.save(userDeletionQueue);

        //And finally
        userRepository.delete(user);

        //update counters
        updateUserCounter();

        log.info("Deleted user " + user.getUsername());
    }

    private void updateUserCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        final Long totalUsers = userRepository.count();

        // Unlikely for this to happen, but you never know...
        if (totalUsersCounter == null) {
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
        }

        totalUsersCounter.setValue(totalUsers);
        counterRepository.save(totalUsersCounter);
    }

    public void sendPasswordResetRequest(@Valid String email) {
        final var user = userRepository.findByUsername(email)
                .orElseThrow(() -> new UserNotFoundByUsernameException(email));

        final var resetToken = generatePasswordResetToken(user);
        sendPasswordResetEmail(user, resetToken);

        log.info("Password reset email sent to user: " + user.getUsername());
    }

    /**
     * Generates a JWT token containing the user's ID for password reset purposes.
     * The token has a shorter expiration time for security.
     *
     * @param user The user for whom to generate the reset token
     * @return JWT token string
     */
    private String generatePasswordResetToken(User user) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("purpose", "password_reset");
        claims.put("created", new Date());

        // Set expiration to 1 hour for password reset tokens (more secure than regular tokens)
        Date expirationDate = new Date(System.currentTimeMillis() + 3600 * 1000); // 1 hour

        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(expirationDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }

    /**
     * Sends a password reset email to the user with the JWT token.
     *
     * @param user The user to send the email to
     * @param resetToken The JWT token for password reset
     */
    private void sendPasswordResetEmail(User user, String resetToken) {
        final var subject = "Visited App Password Reset";
        final var emailBody = createPasswordResetEmailBody(user, resetToken);

        emailService.sendSimpleMessage(
                Optional.of("Visited App"),
                Optional.empty(), // Use default sender
                user.getUsername(),
                subject,
                emailBody,
                true // Add unsubscribe header
        );
    }

    /**
     * Creates the HTML email body for password reset.
     *
     * @param user The user requesting password reset
     * @param resetToken The JWT token for password reset
     * @return HTML email body
     */
    private String createPasswordResetEmailBody(User user, String resetToken) {
        // You can customize this URL to point to your frontend password reset page
        String resetUrl = config.getHost() +  "/v2/auth/resetPassword?token=" + resetToken;

        return """
            <html>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <h2 style="color: #2c3e50;">Password Reset Request</h2>

                    <p>Hello,</p>

                    <p>We received a request to reset the password for your Visited App account associated with this email address.</p>

                    <p>To reset your password, please click the button below:</p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background-color: #3498db; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
                    </div>

                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #3498db;">%s</p>

                    <p><strong>This link will expire in 1 hour for security reasons.</strong></p>

                    <p>If you didn't request this password reset, please ignore this email. Your password will remain unchanged.</p>

                    <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

                    <p style="font-size: 12px; color: #666;">
                        This email was sent from the Visited App. If you have any questions, please contact our support team.
                    </p>
                </div>
            </body>
            </html>
            """.formatted(resetUrl, resetUrl);
    }

    /**
     * Validates a password reset token and extracts the user ID from it.
     *
     * @param token The JWT token to validate
     * @return The user ID if the token is valid
     * @throws RuntimeException if the token is invalid, expired, or not for password reset
     */
    public Long validatePasswordResetToken(String token) {
        try {
            var claims = Jwts.parser()
                    .setSigningKey(jwtSecret)
                    .parseClaimsJws(token)
                    .getBody();

            // Check if this token is for password reset
            String purpose = (String) claims.get("purpose");
            if (!"password_reset".equals(purpose)) {
                throw new RuntimeException("Invalid token purpose");
            }

            // Check if token is expired
            if (claims.getExpiration().before(new Date())) {
                throw new RuntimeException("Password reset token has expired");
            }

            // Extract and return user ID
            Object userIdObj = claims.get("userId");
            if (userIdObj instanceof Integer) {
                return ((Integer) userIdObj).longValue();
            } else if (userIdObj instanceof Long) {
                return (Long) userIdObj;
            } else {
                throw new RuntimeException("Invalid user ID in token");
            }

        } catch (Exception e) {
            log.warning("Failed to validate password reset token: " + e.getMessage());
            throw new RuntimeException("Invalid or expired password reset token", e);
        }
    }

    /**
     * Resets a user's password using a valid reset token.
     *
     * @param token The password reset token
     * @param newPassword The new password
     * @throws RuntimeException if the token is invalid or user not found
     */
    public void resetPasswordWithToken(String token, String newPassword) {
        Long userId = validatePasswordResetToken(token);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        // Encode and set the new password
        String encodedPassword = passwordEncoder.encode(newPassword);
        user.setPassword(encodedPassword);
        user.setLastPasswordResetDate(new Date());

        userRepository.save(user);

        log.info("Password successfully reset for user: " + user.getUsername());
    }
}
