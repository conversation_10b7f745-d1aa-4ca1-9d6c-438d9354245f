package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.dto.DealsByAreaDTO;
import com.arrivinginhighheels.visited.backend.dto.DealsByExperienceDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceRepository;
import com.arrivinginhighheels.visited.backend.model.Deal;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.RemoteSwitch;
import com.arrivinginhighheels.visited.backend.repository.DealsRepository;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.repository.RemoteSwitchRepository;
import com.arrivinginhighheels.visited.backend.utils.DateTools;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class DealsService {

    private final DealsRepository repository;

    private final GeoAreaRepository areaRepository;

    private final ExperienceRepository experiencesRepository;

    private final DateTools dateTools;

    private final RemoteSwitchRepository remoteSwitchRepository;

    public DealsService(DealsRepository repository, GeoAreaRepository areaRepository, ExperienceRepository experiencesRepository, DateTools dateTools, RemoteSwitchRepository remoteSwitchRepository) {
        this.repository = repository;
        this.areaRepository = areaRepository;
        this.experiencesRepository = experiencesRepository;
        this.dateTools = dateTools;
        this.remoteSwitchRepository = remoteSwitchRepository;
    }

    public DealsByAreaDTO findByArea(String areaIsoCode) {
        if (!areDealsEnabled()) {
            throw new RuntimeException("Deals Disabled");
        }

        final var area = areaRepository.findByIsoKey(areaIsoCode).orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));
        final List<Deal> deals = repository.findAllByAreaId(area.getId());
        String primaryUrl = "";
        Map<Long, String> experiences = new HashMap<>();

        for (Deal deal : deals) {
            final Experience experience = deal.getExperience();

            if (experience == null) {
                primaryUrl = deal.getUrl();
            } else {
                experiences.put(experience.getId(), deal.getUrl());
            }
        }

        return new DealsByAreaDTO(primaryUrl, experiences);
    }

    public DealsByExperienceDTO findByExperience(Long experienceId) {
        if (!areDealsEnabled()) {
            throw new RuntimeException("Deals Disabled");
        }

        final Optional<Experience> experienceBox = experiencesRepository.findById(experienceId);
        if (experienceBox.isEmpty()) {
            return null;
        }

        final Experience experience = experienceBox.get();
        final List<Deal> deals = repository.findAllByExperienceId(experience.getId());

        String primaryUrl = "";
        Map<String, String> areas = new HashMap<>();

        for (Deal deal : deals) {
            final GeoArea area = deal.getGeoArea();

            if (area == null) {
                primaryUrl = deal.getUrl();
            } else {
                areas.put(area.getIsoKey(), deal.getUrl());
            }
        }

        return new DealsByExperienceDTO(primaryUrl, areas);
    }

    public boolean areDealsEnabled() {
        final RemoteSwitch dealsSwitch = remoteSwitchRepository.findByName("enable_deals");
        if (dealsSwitch == null) {
            return false;
        }

        return  dealsSwitch.getValue();
    }


    /**
     *
      * @param id identifier of an experience
     * @return Date can be null
     */
    public Date getMaxModificationTimeOfDealsByExperienceId(Long id) {
        return repository.getMaxModificationTimeOfDealsByExperienceId(id);
    }

    /**
     *
     * @param isoCode isoCode of an GeoArea
     * @return Date can be null
     */
    public Date getMaxModificationTimeOfDealsByAreaIsoCode(String isoCode) {
        return repository.getMaxModificationTimeOfDealsByAreaIsoCode(isoCode);
    }
}
