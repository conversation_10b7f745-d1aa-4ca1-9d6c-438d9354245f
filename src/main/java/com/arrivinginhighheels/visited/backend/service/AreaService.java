package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.dto.*;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.exception.ResourceNotFoundException;
import com.arrivinginhighheels.visited.backend.features.books.BookService;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceDTO;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceRepository;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperiencesService;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.*;
import com.arrivinginhighheels.visited.backend.service.translations.AreaTranslationService;
import com.arrivinginhighheels.visited.backend.service.translations.ExperiencesTranslationService;
import com.arrivinginhighheels.visited.backend.service.translations.RegionTranslationService;
import com.arrivinginhighheels.visited.backend.utils.AdaptiveImageUtils;
import com.arrivinginhighheels.visited.backend.utils.DateTools;
import com.arrivinginhighheels.visited.backend.utils.ETagHashUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.maven.artifact.versioning.DefaultArtifactVersion;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
public class AreaService {

    private static final String FIRST_LEVEL_PATH = "areas";

    private final GeoAreaRepository geoAreaRepository;

    private final RegionRepository regionRepository;

    private final AreaDetailsRepository areaDetailsRepository;

    private final PopularityRepository popularityRepository;

    private final RegionTranslationService regionTranslationService;

    private final AreaTranslationService areaTranslationService;

    private final GeoAreaUserNotesRepository geoAreaUserNotesRepository;

    private final GeometryRepository geometryRepository;

    private final ExperienceRepository experienceRepository;

    private final ExperiencesTranslationService experiencesTranslationService;

    private final CachedResponseRepository responseRepository;

    private final ImageUpdatesRepository imageRepository;

    private final ExperiencesService experiencesService;

    private final DealsService dealsService;

    private final GeoAreaAvailabilityRepository availabilityRepository;

    private final DateTools dateTools;

    private final RemoteSwitchRepository switchRepository;

    private final AdaptiveImageUtils imageUtils;

    private final DisputedTerritoryRepository disputedTerritoryRepository;

    private final BookService bookService;

    public AreaService(
            AreaDetailsRepository areaDetailsRepository,
            GeoAreaRepository geoAreaRepository,
            DateTools dateTools,
            CachedResponseRepository responseRepository,
            GeoAreaAvailabilityRepository availabilityRepository,
            RegionRepository regionRepository,
            PopularityRepository popularityRepository,
            RegionTranslationService regionTranslationService,
            AreaTranslationService areaTranslationService,
            ExperiencesService experiencesService,
            ExperiencesTranslationService experiencesTranslationService,
            DealsService dealsService,
            GeoAreaUserNotesRepository geoAreaUserNotesRepository,
            GeometryRepository geometryRepository,
            AdaptiveImageUtils imageUtils,
            DisputedTerritoryRepository disputedTerritoryRepository,
            ExperienceRepository experienceRepository,
            ImageUpdatesRepository imageRepository,
            RemoteSwitchRepository switchRepository, BookService bookService) {
        this.areaDetailsRepository = areaDetailsRepository;
        this.geoAreaRepository = geoAreaRepository;
        this.dateTools = dateTools;
        this.responseRepository = responseRepository;
        this.availabilityRepository = availabilityRepository;
        this.regionRepository = regionRepository;
        this.popularityRepository = popularityRepository;
        this.regionTranslationService = regionTranslationService;
        this.areaTranslationService = areaTranslationService;
        this.experiencesService = experiencesService;
        this.experiencesTranslationService = experiencesTranslationService;
        this.dealsService = dealsService;
        this.geoAreaUserNotesRepository = geoAreaUserNotesRepository;
        this.geometryRepository = geometryRepository;
        this.imageUtils = imageUtils;
        this.disputedTerritoryRepository = disputedTerritoryRepository;
        this.experienceRepository = experienceRepository;
        this.imageRepository = imageRepository;
        this.switchRepository = switchRepository;
        this.bookService = bookService;
    }

    public List<RegionDTO> getFirstLevelAreasOrganizedByRegion(final Boolean allowRegions, final String platform, final String version) {
        final SupportedLanguage lang = areaTranslationService.getCurrentLanguage();
        final Date lastModifiedDate = getMaxModificationTimeOfAFirstLevelArea();
        final String lastModifiedString = lastModifiedDate.toString();

        final CachedResponse response = responseRepository.findByPathAndSupportedLanguage(FIRST_LEVEL_PATH, lang);

        if (allowRegions && cachedResponseIsUpToDate(response, lastModifiedString)) {
            final List<RegionDTO> cachedRegions = restoreFirstLevelResponse(response);
            if (cachedRegions != null) {
                return cachedRegions;
            }
        }

        final List<Region> regions = regionRepository.findAll();

        final List<RegionDTO> fetchedRegions = regions.stream()
                .map(r ->
                        new RegionDTO().id(r.getId())
                                .name(regionTranslationService.getRegionName(r)) //i18n
                                .areas(r.getCountries()
                                        .stream()
                                        .map(CountryContinent::getCountry)
                                        .map(a -> buildAreaDTOFromGeoArea(a, lang, allowRegions, platform, version))
                                        .collect(toList())
                                )
                )
                .collect(toList());

        if (allowRegions) {
            cacheFirstLevelAreas(response, fetchedRegions, lastModifiedString, lang);
        }

        return fetchedRegions;
    }

    public AreaUpdatesDTO getLatestChanges(final Date date, final String platform, final String version) {
        final List<RegionDTO> regions = regionRepository.findAllByLastModificationTimeGreaterThan(date)
                .stream()
                .map(region -> new RegionDTO()
                        .id(region.getId())
                        .name(region.getName())
                        .setTranslations(regionTranslationService.getAll(region)))
                .collect(toList());

        final List<GeoArea> fetchedAreas = geoAreaRepository.findAllByLastModificationTimeGreaterThan(date);
        final List<AreaDTO> updates;
        if (!fetchedAreas.isEmpty()) {
            final Map<Long, GeoAreaAvailability> availabilities = getAvailabilities();
            updates = fetchedAreas.stream()
                    .filter(area -> isAllowedToShowArea(area, platform, version, availabilities))
                    .map((GeoArea area) -> buildLocalizedAreaDTO(area, platform, version))
                    .collect(toList());
        } else {
            updates = new ArrayList<>();
        }


        final AreaUpdatesDTO updatesDTO = new AreaUpdatesDTO(new Date(), updates, regions);
        final RemoteSwitch updateAssets = switchRepository.findByName("allow_assets_updates");
        final boolean shouldUpdate = updateAssets != null ? updateAssets.getValue() : false;

        if (shouldUpdate) {
            final List<Geometry> geometries = geometryRepository.findAllByLastModificationTimeGreaterThan(date);
            final Map<String, String> geometryDTO = new HashMap<>();

            for (final Geometry geometry : geometries) {
                geometryDTO.put(geometry.getFile(), geometry.getUrl());
            }
            updatesDTO.setGeometry(geometryDTO);

            final List<ImageUpdate> images = imageRepository.findAllByLastModificationTimeGreaterThan(date);
            final Map<String, String> imagesDTO = new HashMap<>();
            for (final ImageUpdate image : images) {
                imagesDTO.put(image.getName(), image.getUrl());
            }
            updatesDTO.setImages(imagesDTO);
        }

        return updatesDTO;
    }

    private Map<Long, GeoAreaAvailability> getAvailabilities() {
        final List<GeoAreaAvailability> availabilities = availabilityRepository.findAll();
        final Map<Long, GeoAreaAvailability> availabilityMap = new HashMap<>();
        for (final GeoAreaAvailability availability : availabilities) {
            availabilityMap.put(availability.getGeoArea().getId(), availability);
        }

        return availabilityMap;
    }

    private boolean isAllowedToShowArea(final GeoArea area, final String platform, final String version, final Map<Long, GeoAreaAvailability> availabilities) {
        final GeoAreaAvailability availability = availabilities.get(area.getId());

        // No Restrictions have been placed on this area
        if (availability == null) {
            return true;
        }

        return isAvailable(availability, platform, version);
    }

    private boolean isAvailable(final GeoAreaAvailability availability, final String platform, final String version) {
        // The client is old and is not sending version information.
        // This is an automatic fail if this area has restrictions
        if (platform == null && version == null) {
            return (availability.getMinIosVersion() == null && availability.getMinAndroidVersion() == null);
        }

        if (platform == null) {
            return false;
        }

        final String minAllowed = platform.equals("iOS") ? availability.getMinIosVersion() : availability.getMinAndroidVersion();

        if (minAllowed == null) {
            return true;
        }

        final DefaultArtifactVersion client = new DefaultArtifactVersion(version);
        final DefaultArtifactVersion allowed = new DefaultArtifactVersion(minAllowed);

        return client.compareTo(allowed) >= 0;
    }

    public List<AreaDTO> getSubdivisionsOfAnAreaByTheAreasIsoCode(final String areaIsoCode, final Boolean allowGeometry) {
        final boolean allowRegions = allowGeometry != null && allowGeometry;
        final SupportedLanguage lang = areaTranslationService.getCurrentLanguage();
        return geoAreaRepository.findAllSubdivisionsByParentIsoKey(areaIsoCode).stream()
                .map(a -> buildAreaDTOFromGeoArea(a, lang, allowRegions, null, null))
                .collect(toList());
    }

    public List<AreaDTO> getAnAreaTopPlacesPeopleThatLivesInGoesTo(final String areaIsoCode) {
        final var livedArea = geoAreaRepository.findByIsoKey(areaIsoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));;

        final var lang = areaTranslationService.getCurrentLanguage();

        return geoAreaRepository.findTop10TopPlacesThatAreCountriesFromLivedGeoArea(livedArea).stream()
                .map(a -> buildAreaDTOFromGeoArea(a, lang, false, null, null))
                .collect(toList());
    }

    public List<AreaDTO> getCitiesOfAnAreaByTheAreasIsoCode(final String areaIsoCode) {
        final var lang = areaTranslationService.getCurrentLanguage();
        return geoAreaRepository.findAllCitiesByParentIsoKey(areaIsoCode).stream()
                .map(a -> buildAreaDTOFromGeoArea(a, lang, false, null, null))
                .collect(toList());
    }

    public AreaDetailsDto getAreasDetailsByItsIsoCode(final String areaIsoCode, final double thumbnailResolution) {
        final var area = geoAreaRepository.findByIsoKey(areaIsoCode).orElseThrow();
        final var areaDetails = areaDetailsRepository.findByGeoArea(area);
        if (areaDetails == null) {
            throw new ResourceNotFoundException("Details for area", areaIsoCode);
        }

        final var popularity = popularityRepository.findByGeoAreaIsoKey(areaIsoCode);
        final var imageUrl = imageUtils.getResolution(areaDetails.getThumbnail(), thumbnailResolution);

        return new AreaDetailsDto(
            areaDetails.getId(),
            areaDetails.getGeoArea().getIsoKey(),
            areaDetails.getGeoArea().getType().toString(),
            areaTranslationService.getAreaName(areaDetails.getGeoArea()),
            popularity != null ? popularity.getRank() : popularityRepository.getLastPositionInRanking(),
            areaDetails.getSize() != null ? areaDetails.getSize().doubleValue() : 0d,
            areaDetails.getPopulation(),
            mustSeeAsAList(areaDetails),
            imageUrl,
            areaDetails.getThumbnail().getBlurHash(),
            bookService.fetchBooksByAreaIsoCode(areaIsoCode)
        );
    }

    public AreaUserNotesDTO getUserNotesForAnAreaByItsIsoCode(final User user, final String areaIsoCode) {
        final var geoArea = geoAreaRepository.findByIsoKey(areaIsoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));

        final GeoAreaUserNotes geoAreaUserNotes = geoAreaUserNotesRepository.findByGeoAreaAndUser(geoArea, user);
        if (geoAreaUserNotes == null) {
            throw new ResourceNotFoundException("User notes for the area", areaIsoCode);
        }

        return new AreaUserNotesDTO().notes(geoAreaUserNotes.getNotes());
    }

    public AreaUserNotesDTO addOrUpdateUserNotesForAGivenArea(final User user,
                                                              final String areaIsoCode,
                                                              final AreaUserNotesDTO userNotesDTO) {
        final var geoArea = geoAreaRepository.findByIsoKey(areaIsoCode).
                orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));;


        GeoAreaUserNotes geoAreaUserNotes = geoAreaUserNotesRepository.findByGeoAreaAndUser(geoArea, user);
        if (geoAreaUserNotes == null) {
            geoAreaUserNotes =
                    new GeoAreaUserNotes().geoArea(geoArea)
                            .user(user);
        }
        geoAreaUserNotes.setNotes(userNotesDTO.getNotes());
        geoAreaUserNotesRepository.save(geoAreaUserNotes);

        return new AreaUserNotesDTO().notes(geoAreaUserNotes.getNotes());
    }

    private List<String> mustSeeAsAList(final AreaDetails areaDetails) {
        return areaDetails.getMustSees() != null
                ? areaDetails.getMustSees().stream()
                .map(areaTranslationService::getMustSeeDescription)//i18n
                .collect(Collectors.toList())
                : new ArrayList<>();
    }

    private AreaDTO buildLocalizedAreaDTO(final GeoArea area, final String platform, final String version) {
        final List<GeoAreaTranslation> areaTranslations = areaTranslationService.getAllTranslationsForArea(area);
        final Map<String, String> allTranslations = new HashMap<>();
        for (final GeoAreaTranslation translation : areaTranslations) {
            final String code = translation.getSupportedLanguage().getCode();
            final String value = translation.getName();

            allTranslations.put(code, value);
        }
        return new AreaDTO()
                .id(area.getId())
                .continentIds(area)
                .continentId(area)
                .parentId(area)
                .name(area.getName())
                .type(area.getType().toString())
                .iso(area.getIsoKey())
                .flagFileName(area.getFlagFileName())
                .eTags(getTheTagsForAnArea(area, true, platform, version))
                .bounds(area.boundsAsDoubleArray())
                .viewBounds(area.viewBoundsAsDoubleArray())
                .setSovereign(area.isSovereign())
                .setSovereignParentId(area.getSovereignParentId())
                .translations(allTranslations)
                .labels(area.getLabels()
                        .stream()
                        .map(GeoAreaLabel::asAreaLabelDTO)
                        .collect(toList())
                );
    }


    public AreaDTO buildAreaDTOFromGeoArea(final GeoArea area, final SupportedLanguage lang, final Boolean allowGeometry, final String platform, final String version) {
        return new AreaDTO()
                .id(area.getId())
                .continentIds(area)
                .continentId(area)
                .parentId(area)
                .name(areaTranslationService.getAreaName(area, lang)) //i18n
                .type(area.getType().toString())
                .iso(area.getIsoKey())
                .flag(area.getFlagUrl())
                .flagFileName(area.getFlagFileName())
                .eTags(getTheTagsForAnArea(area, allowGeometry, platform, version))
                .bounds(area.boundsAsDoubleArray())
                .viewBounds(area.viewBoundsAsDoubleArray())
                .setSovereign(area.isSovereign())
                .setSovereignParentId(area.getSovereignParentId())
                .labels(area.getLabels()
                        .stream()
                        .map(GeoAreaLabel::asAreaLabelDTO)
                        .collect(toList())
                );
    }

    public List<ExperienceDTO> getExperiencesRelateToAnAreaByIsoCode(final String areaIsoCoe) {
        return experienceRepository.getAllExperiencesRelatedToAnAreaByTheAreasIsoCode(areaIsoCoe)
                .stream()
                .map(experiencesService::buildExperienceDTO)
                .collect(toList());
    }

    public List<DisputedTerritoryDTO> getDisputedTerritories(String version) {
        final var clientSemanticVersion = new DefaultArtifactVersion(version);

        final var disputed = disputedTerritoryRepository
                .findAll()
                .stream()
                .filter(e -> {
                     final var minVersion = new DefaultArtifactVersion(e.getMinVersion());
                     return clientSemanticVersion.compareTo(minVersion) >= 0;
                }).toList();

        final var dtos = new ArrayList<DisputedTerritoryDTO>();
        final var cache = new HashSet<AreaSimpleDTO>();

        for (final DisputedTerritory territory : disputed) {
            final var area = buildAreaSimpleDto(cache, territory.getGeoArea());

            final var dto = dtos
                    .stream()
                    .filter((e) -> e.getArea().equals(area))
                    .findFirst();

            if (dto.isPresent()) {
                final var option = buildDisputedOption(cache, territory);
                dto.get().getOptions().add(option);
            } else {
                final var option = buildDisputedOption(cache, territory);
                final var options = new ArrayList<DisputedTerritoryOptionDTO>();
                options.add(option);
                final var newDto = new DisputedTerritoryDTO(area, options);
                dtos.add(newDto);
            }
        }

        return dtos;
    }

    private DisputedTerritoryOptionDTO buildDisputedOption(final Set<AreaSimpleDTO> cache, final DisputedTerritory territory) {
        return new DisputedTerritoryOptionDTO(buildAreaSimpleDto(cache, territory.getParent()), territory.getDefaults());
    }

    private AreaSimpleDTO buildAreaSimpleDto(final Set<AreaSimpleDTO> cache, final GeoArea area) {
        final Optional<AreaSimpleDTO> cached = cache
                .stream()
                .filter((e) -> e.getId().equals(area.getId()))
                .findFirst();

        if (cached.isPresent()) {
            return cached.get();
        }

        final AreaSimpleDTO dto = new AreaSimpleDTO(area.getId(), area.getIsoKey(), areaTranslationService.getAreaName(area));
        cache.add(dto);
        return dto;
    }

    private ETagsDTO getTheTagsForAnArea(final GeoArea area, final Boolean allowGeometry, final String platform, final String version) {
        final ETagsDTO tags = new ETagsDTO();
        final boolean hasSubdivisions = geoAreaRepository.countSubdivisionsByParentId(area.getId()).compareTo(0) > 0;
        final boolean hasInfo = areaDetailsRepository.findByGeoArea(area) != null;
        final boolean hasExperiences = experienceRepository.countExperiencesRelatedToAnArea(area).compareTo(0) > 0;

        final boolean allowSubdivisions = allowGeometry || isAllowedSubdivisionsInLegacyVersion(area.getIsoKey());

        if (allowSubdivisions && hasSubdivisions && isAllowedToSubdivisionsForArea(area, platform, version)) {
            final String subdivisionsETag =
                    ETagHashUtils.buildHashedValueForTheETagFromADate(
                            getMaxModificationTimeOfNonCityChildrenAreaByItsParent(area));
            tags.setSubdivisions(subdivisionsETag);
        }

        if (hasInfo) {
            tags.setInfo(ETagHashUtils.buildHashedValueForTheETagFromADate(
                    getMaxLastModificationTimeOfAnArea(area)));
        }

        if (hasExperiences) {
            tags.setExperiences(ETagHashUtils.buildHashedValueForTheETagFromADate(
                    getMaxModificationTimeOfExperiencesRelatedToAreaIsoCode(area.getIsoKey())));
        }

        if (dealsService.areDealsEnabled()) {
            final Date dealsMaxModifiedTime = dealsService.getMaxModificationTimeOfDealsByAreaIsoCode(area.getIsoKey());
            if (dealsMaxModifiedTime != null) {
                tags.setDeals(ETagHashUtils.buildHashedValueForTheETagFromADate(dealsMaxModifiedTime));
            }
        }

        return tags;
    }

    private boolean isAllowedSubdivisionsInLegacyVersion(final String areaIsoCode) {
        return switch (areaIsoCode) {
            case "US", "GB", "CA", "AU" -> true;
            default -> false;
        };
    }

    private boolean isAllowedToSubdivisionsForArea(final GeoArea area, final String platform, final String version) {
        final List<GeoAreaAvailability> availabilities = availabilityRepository.findAllSubdivisionsByParentIsoKey(area.getIsoKey());

        // Only For Tests
        if (availabilities == null || availabilities.size() == 0) {
            return true;
        }

        return isAvailable(availabilities.get(0), platform, version);
    }

    public GeoArea getGeoAreaByIsoCode(final String areaIsoCode) {
        return geoAreaRepository.findByIsoKey(areaIsoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));
    }

    private Date getMaxLastModificationTimeOfAnArea(final GeoArea area) {
        final var maxDate = areaDetailsRepository.findMaxLastModificationTimeOfAnArea(area);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    private Date getMaxModificationTimeOfCitiesByItsParent(final GeoArea area) {
        final Date maxDate = geoAreaRepository.findMaxModificationTimeOfCitiesByItsParent(area);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    private Date getMaxModificationTimeOfNonCityChildrenAreaByItsParent(final GeoArea area) {
        final Date maxDate = geoAreaRepository.findMaxModificationTimeOfNonCityChildrenAreaByItsParent(area);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfAFirstLevelArea() {
        final Date maxDate = geoAreaRepository.findMaxLastModificationTimeOfAFirstLevelArea();
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfAnAreaDetails(final String areaIsoCode) {
        final var area = geoAreaRepository.findByIsoKey(areaIsoCode).orElseThrow();
        final Date maxDate = areaDetailsRepository.findMaxLastModificationTimeOfAnArea(area);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfAnAreaGeometry(final String areaIsoCode) {
        final Date maxDate = geometryRepository.findMaxLastModificationTimeOfAnAreaByItsIsoCode(areaIsoCode);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfNonCityChildrenAreaByItsParentsIsoCode(final String areaIsoCode) {
        final Date maxDate = geoAreaRepository.findMaxModificationTimeOfNonCityChildrenAreaByItsParentsIsoCode(areaIsoCode);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfCitiesByItsParentsIsoCode(final String areaIsoCode) {
        final Date maxDate = geoAreaRepository.findMaxModificationTimeOfCitiesByItsParentsIsoCode(areaIsoCode);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfExperiencesRelatedToAreaIsoCode(final String isoCode) {
        final Date maxDate = experienceRepository.getMaxModificationTimeOfExperienceGeoAreaRelationByTheGeoAreasIsoCode(isoCode);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    // Top Level Areas caching
    // Even through we support client side caching via eTags, this first request to get the top level areas can be
    // quite heavy. This is a particularity bad experience since this is usually the first request a new user will see
    // This will instead cache the /areas service per language once and then only return this flat file instead of
    // having to build it every time for every new user
    private boolean cachedResponseIsUpToDate(final CachedResponse response, final String date) {
        if (response == null) {
            return false;
        }

        return response.getLastModificationTime().equals(date);
    }

    private void cacheFirstLevelAreas(final CachedResponse previousResponse, final List<RegionDTO> regions, final String lastModifiedDate, final SupportedLanguage lang) {
        try {
            final ObjectMapper mapper = new ObjectMapper();
            final String json = mapper.writeValueAsString(regions);

            final CachedResponse response = new CachedResponse(FIRST_LEVEL_PATH, json, lastModifiedDate, lang);

            if (previousResponse != null) {
                response.setId(previousResponse.getId());
            }

            responseRepository.save(response);
        } catch (final Exception e) {
            e.printStackTrace();
        }
    }

    private List<RegionDTO> restoreFirstLevelResponse(final CachedResponse response) {
        final String json = response.getJson();
        try {
            final ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, new TypeReference<List<RegionDTO>>() {
            });
        } catch (final Exception e) {
            return null;
        }
    }
}
