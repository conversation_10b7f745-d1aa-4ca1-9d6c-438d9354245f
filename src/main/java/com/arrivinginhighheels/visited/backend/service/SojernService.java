package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.dto.SojernTrackingDTO;
import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceRepository;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.Selection;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.HttpRequestUtils;
import com.arrivinginhighheels.visited.backend.utils.UrlBuilder;
import org.json.JSONObject;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Deprecated
public class SojernService {
    private static final String SOJERN_HOST = "pixel.sojern.com";
    private static final String SOJERN_PATH = "/mobile/partner/Eq7AW6B1pQNRlB8K";
    private static final String PARTNER_ID = "70KUvEbCPnGatJSR";

    private final LoggedInUserUtil loggedInUserUtil;

    private final AreaService areaService;

    private final SelectionService selectionService;

    private final ExperienceRepository experienceRepository;

    private final HttpRequestUtils httpRequestUtils;

    public SojernService(LoggedInUserUtil loggedInUserUtil, AreaService areaService, SelectionService selectionService, ExperienceRepository experienceRepository, HttpRequestUtils httpRequestUtils) {
        this.loggedInUserUtil = loggedInUserUtil;
        this.areaService = areaService;
        this.selectionService = selectionService;
        this.experienceRepository = experienceRepository;
        this.httpRequestUtils = httpRequestUtils;
    }

    public boolean sendEvent(SojernTrackingDTO event, HttpServletRequest request) {
        final URL url = buildSojernUrl(event, request);
        try {
            sendSojernRequest(url);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    private String sendSojernRequest(URL sojernUrl) throws IOException {
        HttpURLConnection connection = (HttpURLConnection)sojernUrl.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Content-Type", "application/json; utf-8");
        connection.setDoOutput(true);

        connection.connect();

        final int responseCode = connection.getResponseCode();
        InputStream inputStream;
        if (responseCode / 100 == 2) {
            inputStream = connection.getInputStream();
        } else {
            inputStream = connection.getErrorStream();
        }

        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            return response.toString();
        }
    }

    private URL buildSojernUrl(SojernTrackingDTO event, HttpServletRequest request) {
        final UrlBuilder urlBuilder = new UrlBuilder();
        return urlBuilder.schema("https")
                .host(SOJERN_HOST)
                .path(SOJERN_PATH)
                .addQueryParameter("partner_id", PARTNER_ID)
                .addQueryParameter("app_name", "Visited")
                .addQueryParameter("longname", event.getBundleId())
                .addQueryParameter("app_version", event.getAppVersion())
                .addQueryParameter("vendor_sdk_version", "Direct")
                .addQueryParameter("brand", event.getDeviceBrand())
                .addQueryParameter("carrier", event.getDeviceCarrier())
                .addQueryParameter("language", getCurrentLanguage())
                .addQueryParameter("id", event.getAdvertisingId())
                .addQueryParameter("model", event.getDeviceModel())
                .addQueryParameter("os", event.getOs())
                .addQueryParameter("osVersion", event.getOsVersion())
                .addQueryParameter("wifi", event.isUsingWifi() ? "Yes" : "No")
                .addQueryParameter("network", event.getNetworkType())
                .addQueryParameter("ip", httpRequestUtils.findIpAddress(request))
                .addQueryParameter("utcm", getCurrentUTCTime())
                .addQueryParameter("event_name", event.getEventType().id)
                .addQueryParameter("json", buildEventSpecificProperties(event, request))
                .build();
    }

    private String getCurrentLanguage() {
        Locale locale = LocaleContextHolder.getLocale();
        String lang = locale.getLanguage();

        return lang != null ? lang : "en";
    }

    private String getCurrentUTCTime() {
        final Date now = new Date();
        final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
        return formatter.format(now);
    }

    private String buildEventSpecificProperties(SojernTrackingDTO event, HttpServletRequest request) {
        final User user = loggedInUserUtil.getLoggedInUser(request);
        final JSONObject json = new JSONObject();
        addEmailHashesToJson(json, user);
        addUserOriginToJson(json, user);

        switch (event.getEventType()) {
            case HomeScreen:
                json.put("et", "hp");
                break;

            case WantTracking:
                applyWantTracking(json, event.getWantAreaCode());
                break;

            case ExperienceTracking:
                applyExperienceTracking(json, event.getWantExperienceId());
                break;
        }

        return json.toString();
    }

    private void applyWantTracking(JSONObject json, String isoCode) {
        json.put("et", "vs");

        GeoArea area = areaService.getGeoAreaByIsoCode(isoCode);
        GeoArea country = area.getTopLevelParent();
        json.put("vn1", country.getName());

        if (!area.equals(country)) {
            json.put("vs1", area.getName());
        }
    }

    private void applyExperienceTracking(JSONObject json, long experienceId) {
        final Optional<Experience> experience = experienceRepository.findById(experienceId);
        experience.ifPresent(value -> json.put("entertainment", value.getName()));
    }

    private void addUserOriginToJson(JSONObject json, User user) {
        List<Selection> liveSelections = selectionService.allAreasWhereUserHasLived(user);
        for (Selection selection : liveSelections) {
            GeoArea area = selection.getGeoArea();

            if (area.getParent() == null) {
                json.put("vn2", area.getName());
            } else {
                json.put("vs2", area.getName());
            }
        }
    }

    private void addEmailHashesToJson(JSONObject json, User user)  {
        final String email = user.getUsername();

        try {
            final String md5 = encrypt(email, "MD5");
            json.put("md5_eml", md5);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        try {
            final String sha1 = encrypt(email, "SHA1");
            json.put("sha1_eml", sha1);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        try {
            final String sha256 = encrypt(email, "SHA-256");
            json.put("sha256_eml", sha256);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

    private String encrypt(String email, String algorithm) throws NoSuchAlgorithmException {
        final MessageDigest md5 = MessageDigest.getInstance(algorithm);
        md5.update(email.getBytes());
        byte[] digest = md5.digest();
        return DigestUtils.md5DigestAsHex(digest).toUpperCase();
    }
}
