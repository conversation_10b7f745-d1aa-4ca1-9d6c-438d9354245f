package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.MultipleSelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.dto.UserBeenCountsDTO;
import com.arrivinginhighheels.visited.backend.exception.InsufficientParametersException;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.SelectionLogRepository;
import com.arrivinginhighheels.visited.backend.repository.SelectionRepository;
import com.arrivinginhighheels.visited.backend.repository.UserBeenCountRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.arrivinginhighheels.visited.backend.model.SelectionType.*;

/**
 * Service to handle the Selection operations
 */
@Service
public class SelectionService extends AbstractSelectionService {
    private final SelectionRepository selectionRepository;
    private final SelectionLogRepository selectionLogRepository;
    private final UserService userService;
    private final RankService rankService;
    private final UserBeenCountRepository userBeenCountRepository;
    private final UserPurchasesRepository userPurchasesRepository;
    private final YamlConfig yamlConfig;

    public SelectionService(
            SelectionRepository selectionRepository,
            SelectionLogRepository selectionLogRepository,
            UserService userService,
            RankService rankService,
            UserBeenCountRepository userBeenCountRepository,
            UserPurchasesRepository userPurchaseRepository,
            YamlConfig yamlConfig
    ) {
        this.selectionRepository = selectionRepository;
        this.selectionLogRepository = selectionLogRepository;
        this.userService = userService;
        this.rankService = rankService;
        this.userBeenCountRepository = userBeenCountRepository;
        this.userPurchasesRepository = userPurchaseRepository;
        this.yamlConfig = yamlConfig;
    }

    /**
     * Handle multiple selections of geoareas.
     *
     */
    @Transactional
    public List<Selection> handleMultipleSelection(final User user, final MultipleSelectionDTO selections) {
        selections.getSelectionList().forEach(s -> processSelection(user, s.getAreaIsoKey(), s.getSelectionType()));
        selectionRepository.flush();

        // recalculate the user's visited areas number
        userService.recalculateUsersTotalNumberOfAreasVisited(user);
        userService.calculateRankForTheUser(user);

        return selectionRepository.findAllByUser(user);
    }

    /**
     * Handles the selection of a geoarea and all its rules and consequences.
     *
     */
    @Transactional
    public SelectionsDTO handleSelection(final User user,
                                         final String geoAreaIsoKey,
                                         final SelectionType newSelectionType) {

        if (!geoAreaIsoKey.isEmpty()) {
            //do the actual processing of the selection
            processSelection(user, geoAreaIsoKey, newSelectionType);
            selectionRepository.flush();
        } else {
            throw new InsufficientParametersException("geo-area iso key");
        }

        // recalculate the user's visited areas number and ranking
        if (newSelectionType != SelectionType.WANT) {
            userService.recalculateUsersTotalNumberOfAreasVisited(user);
            userService.calculateRankForTheUser(user);
        }

        //get all user's selections after the change/addition of the selection...
        return getUserSelections(user);
    }


    public SelectionsDTO getUserSelections(final User user) {
        final var selections = selectionRepository.findAllByUser(user);
        cleanSelectionHierarchy(selections);

        //... and return the selections' data
        final var selectionsDTO = new SelectionsDTO(selections);
        selectionsDTO.setRank(rankService.getUserRank(user));

        return selectionsDTO;
    }

    private void cleanSelectionHierarchy(List<Selection> selections) {
        // Check if there is more than one country selected as "live"
        final var livedCountries = new ArrayList<>(selections.stream()
                .filter(s -> LIVED.equals(s.getType()) && s.getGeoArea().isTopLevelArea())
                .toList());

        if (livedCountries.size() > 1) {
            // Find the most recent selection and delete everything else
            final var mostRecentSelection = livedCountries.stream()
                    .max(Comparator.comparing(Selection::getTimestamp))
                    .orElse(null);

            livedCountries.remove(mostRecentSelection);
            selectionRepository.deleteAll(livedCountries);
            selections.removeAll(livedCountries);
        }

        // Check for duplicates and remove them
        selections.stream().collect(Collectors.groupingBy(Selection::getGeoArea)).forEach((area, areaSelections) -> {
            if (areaSelections.size() == 1) {
                return;
            }

            // Keep the highest precedence and remove the others;
            final var highestSelection = areaSelections.stream().max(Comparator.comparing(Selection::getType)).orElse(null);
            if (highestSelection == null) {
                return;
            }
            areaSelections.remove(highestSelection);
            selectionRepository.deleteAll(areaSelections);
            selections.removeAll(areaSelections);
        });
    }

    private void processSelection(final User user, final String geoAreaIsoKey, final SelectionType newSelectionType) {
        //check if the geoarea exists
        final var geoArea = getGeoAreaVerifyingIfItExists(geoAreaIsoKey);

        //executes the selection of the geoArea
        selectTheGeoArea(user, geoArea, newSelectionType);

        // if the selected geo area has a parent
        final var parentGeoArea = geoArea.getParent();
        if (parentGeoArea != null) {
            handleChangingToTheParentGeoArea(user, newSelectionType, parentGeoArea);
        }

        if (geoAreaRepository.countChildrenByParent(geoArea) > 0) {
            handleChangingToSubdivisions(user, newSelectionType, geoArea);
        }

        // TODO?  Link Cities to these changes, just like we used to do with airports
    }

    private void handleChangingToSubdivisions(User user, SelectionType newSelectionType, GeoArea parent) {
        final var subdivisionSelections = selectionRepository.findAllByUserAndGeoArea_Parent(user, parent);
        final var updatedSelections = new ArrayList<Selection>();
        for (var selection : subdivisionSelections) {
            if (newSelectionType.getWeight() < selection.getType().getWeight()) {
                selection.setType(newSelectionType);
                selection.setTimestamp(LocalDateTime.now());
                updatedSelections.add(selection);

                // Cascade changes down the tree...
                if (geoAreaRepository.countChildrenByParent(selection.getGeoArea()) > 0) {
                    handleChangingToSubdivisions(user, newSelectionType, selection.getGeoArea());
                }
            }
        }

        selectionRepository.saveAll(updatedSelections);
    }

    /**
     * Method responsible for the selection of a given geoarea*
     */
    private void selectTheGeoArea(final User user, final GeoArea geoArea, final SelectionType selectionType) {
        //check for existent selections for this geoArea to change it
        var existingSelection = findSelectionByUserAndGeoArea(user, geoArea);

        // if the selection is unchanged, just do nothing
        if (existingSelection != null && existingSelection.getType() == selectionType) {
            return;
        }

        // Don't allow Live selections to be changed.  They can only be replaced with different selections
        if (existingSelection != null && existingSelection.getType() == LIVED) {
            return;
        }

        // If the new selection is live, demote all previously selected lived to been
        if (selectionType == LIVED) {
            final var oldLived = selectionRepository.findAllByUserAndType(user, LIVED);
            oldLived.forEach(selection -> {
                selection.setType(BEEN);
                selection.setTimestamp(LocalDateTime.now());
            });
            selectionRepository.saveAll(oldLived);
        }


        final Selection selection;
        if (existingSelection == null) {
            //configure the new selection
            selection = new Selection(user, geoArea, selectionType, LocalDateTime.now());
        } else {
            cleanBeenCountersIfNeeded(geoArea, user, selectionType);

            //edit the existing selection, according to the rules for selection change
            selection = existingSelection;
            selection.setType(selectionType); //changes the selection type
            selection.setTimestamp(LocalDateTime.now()); //updates the timestamp
        }

        //persist the new/existing selection
        saveSelection(selection);
    }

    private boolean canAccessPastLived(final User user) {
        final var selections = userPurchasesRepository.findAllByUser(user);
        for (var selection : selections) {
            final var productId = selection.getProductId();
            if (productId.equals(yamlConfig.getProLifetimeBundleId()) ||
                productId.equals(yamlConfig.getProSubscriptionAnnualBundleId()) ||
                productId.equals(yamlConfig.getProSubscriptionMonthlyBundleId()) ||
                productId.equals(yamlConfig.getUnlockRegionsBundleId()) ||
                productId.equals(yamlConfig.getUnlockCitiesBundleId())) {
                return true;
            }
        }

        return false;
    }


    private void cleanBeenCountersIfNeeded(final GeoArea area, final User user, final SelectionType updatedSelection) {
        if (updatedSelection == BEEN || updatedSelection == LIVED) {
            return;
        }

        UserBeenCount count;
        try {
            final var existingCounter = userBeenCountRepository.findByUserAndGeoArea(user, area);
            if (existingCounter.isEmpty()) {
                return;
            }
            count = existingCounter.get();
        } catch (Exception e) {
            // Count doesn't exist, so don't worry about deleting it
            return;
        }

        userBeenCountRepository.delete(count);
    }

    /**
     * Treats the selection of the parent as well, changing it if needed
     *
     */
    private void handleChangingToTheParentGeoArea(final User user, final SelectionType newSelectionType, final GeoArea parentGeoArea) {
        var parentSelection = findSelectionByUserAndGeoArea(user, parentGeoArea);

        if (parentSelection == null) {
            // just create the parent selection if the new selection != CLEAR
            // (if it's clear then it shouldn't create a new one
            if (newSelectionType != SelectionType.CLEAR) {
                parentSelection = new Selection(user, parentGeoArea, newSelectionType, LocalDateTime.now());
                saveSelection(parentSelection);
            }
        } else {
            //first, get all existing selections for the parent
            final var existingSelectionsForTheParent =
                    selectionRepository.findAllByUserAndGeoArea_Parent(user, parentGeoArea);

            //check if upgrade/downgrade is needed
            //the following stream/lambdas get the higher weighted selection, if exists (returns an optional object)
            final var higherSelectionByType =
                    existingSelectionsForTheParent
                            .stream()
                            .max(Comparator.comparingInt(s -> s.getType().getWeight()));

            if (higherSelectionByType.isPresent()) {
                final var newHighestType = higherSelectionByType.get().getType();
                if (parentSelection.getType() != newHighestType) { // if the parent selection is higher, shouldn't be changed
                    // needs changing of the type for the country/parent due to upgrading/downgrading of the states/children
                    parentSelection.setType(newHighestType);
                    parentSelection.setTimestamp(LocalDateTime.now());
                    saveSelection(parentSelection);
                }
                // else just leave as it is,
                // because the country/parent is not supposed
                // to be changed as it already has the correct selection type
            } else {
                // should delete the parent if the new selection has a CLEAR type,
                // because there are no more children to it
                if (newSelectionType == SelectionType.CLEAR) {
                    clearSelection(parentSelection);
                }
            }
        }

        if (parentGeoArea.getParent() != null) {
            handleChangingToTheParentGeoArea(user, newSelectionType, parentGeoArea.getParent());
        }
    }

    /**
     * Saves the selection, also saving a log
     *
     */
    private void saveSelection(final Selection selection) {
        if (selection.getType() == null) {
            throw new RuntimeException("Selection Type cannot be null!");
        }

        //if it's to clear the selection, go to the specific method that handles this
        if (SelectionType.CLEAR.equals(selection.getType())) {
            clearSelection(selection);
            return;
        }

        //...else, execute the logic to save the selection and its logs
        selectionRepository.save(selection);
        selectionLogRepository.save(new SelectionLog(selection));
    }

    /**
     * Deletes a selection, also logging the exclusion with a CLEAR type.
     */
    private void clearSelection(final Selection selection) {
        selectionLogRepository.save(new SelectionLog(selection, SelectionType.CLEAR, LocalDateTime.now()));
        selectionRepository.delete(selection);
    }

    /**
     * Simple select by user in the selection logs
     */
    public List<SelectionLog> getAllLogsForTheUser(final User user) {
        return selectionLogRepository.findAllByUserIdOrderByTimestampAsc(user.getId());
    }

    /**
     * Deletes all the user selection logs.
     * Is different from the "clear" method, as it acts only on the log domain,
     * removing everything for the user.
     */
    @Transactional
    public void resetUserSelectionLogs(final User user) {
        //delete all the selection logs
        selectionLogRepository.deleteByUserId(user.getId());
        selectionLogRepository.flush();
    }

    /**
     * Deletes all user selections.
     */
    @Transactional
    public void resetUserSelections(final User user) {
        selectionRepository.deleteByUser(user);
        selectionRepository.flush();

        resetUserSelectionLogs(user);

        userBeenCountRepository.deleteByUser(user);
        userBeenCountRepository.flush();

        userService.recalculateUsersTotalNumberOfAreasVisited(user);
        userService.calculateRankForTheUser(user);
    }

    /**
     * Queries the DB for the selection with the determined geoarea.
     * This is a bugfix for the case when cleared
     * selections were put in the database by mistake.
     * This method prevents the usage of these records, making the logic
     * correct even with this inconsistency in the DB.
     *
     */
    private Selection findSelectionByUserAndGeoArea(final User user, final GeoArea geoArea) {
        final List<Selection> selections = selectionRepository.findAllByUserAndGeoArea(user, geoArea);
        if (selections.isEmpty()) {
            return null;
        }

        // Clean up duplicates if they exist
        if (selections.size() > 1) {
            Logger.getGlobal().log(Level.INFO, "Found more than one selection for " + user.getUsername() + " and " + geoArea.getName() + ". Removing duplicates.");
            var highestPrecedence = selections.stream().max(Comparator.comparing(Selection::getType));
            if (highestPrecedence.isPresent()) {
                var selectionToKeep = highestPrecedence.get();
                selections.remove(selectionToKeep);
                selectionRepository.deleteAll(selections);

                return selectionToKeep;
            }
        }

        return selections.stream()
                .filter(g -> !g.getType().equals(SelectionType.CLEAR))
                .findFirst()
                .orElse(null);
    }

    List<Selection> allAreasWhereUserHasLived(final User user) {
        return selectionRepository.findAllByUserAndType(user, LIVED);
    }

    public UserBeenCountsDTO getAllBeenCountsForUser(final User user) {
        final List<UserBeenCount> counts = userBeenCountRepository.findAllByUser(user);
        final Map<String, Integer> countMap = new HashMap<>();
        for (final UserBeenCount count : counts) {
            countMap.put(count.getGeoArea().getIsoKey(), count.getCount());
        }

        return new UserBeenCountsDTO(countMap);
    }

    public void updateUserBeenCounters(final User user, final Map<String, Integer> counts) {
        for (final String isoCode : counts.keySet()) {
            final GeoArea geoArea = getGeoAreaVerifyingIfItExists(isoCode);

            final Selection existingSelection = findSelectionByUserAndGeoArea(user, geoArea);
            if (existingSelection == null || existingSelection.getType() != BEEN) {
                selectTheGeoArea(user, geoArea, BEEN);
            }

            UserBeenCount count;

            Supplier<UserBeenCount> countBuilder = () -> {
                var counter = new UserBeenCount();
                counter.setUser(user);
                counter.setGeoArea(geoArea);
                return counter;
            };

            try {
             var maybeCount =  userBeenCountRepository.findByUserAndGeoArea(user, geoArea);
             count = maybeCount.orElseGet(countBuilder);
            } catch (Exception e) {
                count = countBuilder.get();
            }

            final Integer value = counts.get(isoCode);

            if (count == null) {
                count = countBuilder.get();
            }

            if (value <= 0) {
                try {
                    userBeenCountRepository.delete(count);
                } catch (Exception e) {
                    // Do nothing.  Entity does not exist in the database
                }
                continue;
            }

            count.setTimestamp(LocalDateTime.now());
            count.setCount(counts.get(isoCode));

            userBeenCountRepository.save(count);
        }

    }


    public Selection getSelectionsForArea(final User user, final GeoArea area) {
        return selectionRepository.findByUserAndGeoArea(user, area);
    }
}
