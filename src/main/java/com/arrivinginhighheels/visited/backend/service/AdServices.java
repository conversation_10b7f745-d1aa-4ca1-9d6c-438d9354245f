package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.dto.AdInventoryDTO;
import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.AdCategory;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.repository.AdCategoryRepository;
import com.arrivinginhighheels.visited.backend.utils.DateTools;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class AdServices {

    private final AdCategoryRepository repository;

    private final DateTools dateTools;

    public AdServices(AdCategoryRepository repository, DateTools dateTools) {
        this.repository = repository;
        this.dateTools = dateTools;
    }

    public List<AdInventoryDTO> findInventoriesForProvider(String provider, String os) {
        final List<AdCategory> categories = repository.findByProviderAndOs(provider, os);

        Map<String, AdInventoryDTO> dtos = new HashMap<>();

        for (AdCategory category : categories) {
            AdInventoryDTO dto = dtos.get(category.getHash());

            if (dto == null) {
                dto = new AdInventoryDTO();
                dto.setName(category.getName());
                dto.setHash(category.getHash());
            }

            GeoArea area = category.getGeoArea();
            if (area != null) {
                dto.addAreaCode(area.getIsoKey());
            }

            Experience experience = category.getExperience();
            if (experience != null) {
                dto.addExperienceId(experience.getId());
            }

            dtos.put(category.getHash(), dto);
        }

        return new ArrayList<>(dtos.values());
    }

    public Date getMaxLastModificationTimeOfAProviderAndOs(String provider, String os) {
        Date maxDate = repository.getMaxModificationTimeByProviderAndOs(provider, os);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

}
