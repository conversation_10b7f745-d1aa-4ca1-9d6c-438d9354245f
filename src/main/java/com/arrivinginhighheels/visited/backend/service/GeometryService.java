package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.EnclosedWhiteListDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaOoesNotHaveSubdivisionsException;
import com.arrivinginhighheels.visited.backend.repository.EnclosedAreaWhiteListRepository;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.utils.DateTools;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.Enumeration;
import java.util.List;

import static java.util.stream.Collectors.toList;
import static org.springframework.http.HttpHeaders.IF_NONE_MATCH;

@Service
public class GeometryService {

    private final GeoAreaRepository geoAreaRepository;

    private final EnclosedAreaWhiteListRepository enclosedAreaWhiteListRepository;

    private final YamlConfig yamlConfig;

    private final DateTools dateTools;

    public GeometryService(GeoAreaRepository geoAreaRepository, EnclosedAreaWhiteListRepository enclosedAreaWhiteListRepository, YamlConfig yamlConfig, DateTools dateTools) {
        this.geoAreaRepository = geoAreaRepository;
        this.enclosedAreaWhiteListRepository = enclosedAreaWhiteListRepository;
        this.yamlConfig = yamlConfig;
        this.dateTools = dateTools;
    }


    public ResponseEntity<Object> fetchS3File(boolean useCDN, String filename, HttpServletRequest request) throws IOException, URISyntaxException {
        String host = useCDN ? "https://d2p8afmxmwigex.cloudfront.net" : yamlConfig.getHost();
        String url = host + "/geometry/" + filename + ".json";
        URL urlObj = new URL(url);
        URLConnection urlCon = urlObj.openConnection();

        String eTag = urlCon.getHeaderField("ETag");

        if (thereIsAnETagMatchInTheRequest(request, eTag)) {
            return ResponseEntity.status(HttpStatus.NOT_MODIFIED).build();
        }


        URI s3 = new URI(url);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setLocation(s3);
        httpHeaders.set("Authentication", ""); //Make sure the api's auth header doesn't bleed into S3
        return new ResponseEntity<Object>(httpHeaders, HttpStatus.SEE_OTHER);
    }

    public ResponseEntity<Object> getSubdivisionGeometryFromS3(String areaIsoCode, HttpServletRequest request) throws IOException, URISyntaxException {
        final var area = geoAreaRepository.findByIsoKey(areaIsoCode).orElseThrow(() -> new GeoAreaNotFoundException(areaIsoCode));

        if (area.getSubdivisions() == null) {
            throw new GeoAreaOoesNotHaveSubdivisionsException(areaIsoCode);
        }

        return fetchS3File(true, areaIsoCode, request);
    }


    private boolean thereIsAnETagMatchInTheRequest(HttpServletRequest request, String hashedValueOfTheETag) {
        boolean thereIsATagMatching = false;
        Enumeration<String> ifNoneMatchHeaders = request.getHeaders(IF_NONE_MATCH);
        if (ifNoneMatchHeaders != null && ifNoneMatchHeaders.hasMoreElements()) {
            while (ifNoneMatchHeaders.hasMoreElements()) {
                String ifNoneMatchHeader = ifNoneMatchHeaders.nextElement();
                if (ifNoneMatchHeader.equalsIgnoreCase(hashedValueOfTheETag)) {
                    thereIsATagMatching = true;
                    break;
                }
            }
        }
        return thereIsATagMatching;
    }

    public List<EnclosedWhiteListDTO> getEnclosedWhiteList() {
        return enclosedAreaWhiteListRepository.findAll()
                .stream()
                .map(list -> new EnclosedWhiteListDTO()
                        .setAreaKey(list.getArea().getIsoKey())
                        .setSurroundingKey(list.getSurroundingArea().getIsoKey()))
                .collect(toList());
    }

    public Date getMaxModificationTimeOfWhiteList() {
        final Date date = enclosedAreaWhiteListRepository.findMaxLastModificationTime();
        return dateTools.nullSafeLastModifiedTime(date);
    }
}


