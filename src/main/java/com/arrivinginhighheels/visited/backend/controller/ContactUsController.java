package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.features.authentication.BrandAmbassadorRequest;
import com.arrivinginhighheels.visited.backend.service.ContactUsService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(path = "/v2/contactUs")
public class ContactUsController {

    private final ContactUsService contactUsService;

    public ContactUsController(ContactUsService contactUsService) {
        this.contactUsService = contactUsService;
    }

    @PostMapping(path = "/becomeBrandAmbassador", produces = "application/json")
    public ResponseEntity<Map<String, Boolean>> signupForMigration(
            @RequestBody BrandAmbassadorRequest request) {
        contactUsService.becomeBrandAmbassador(request);
        return ResponseEntity.ok(Map.of("success", true));
    }
}
