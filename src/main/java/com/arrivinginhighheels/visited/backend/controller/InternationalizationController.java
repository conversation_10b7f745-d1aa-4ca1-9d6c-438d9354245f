package com.arrivinginhighheels.visited.backend.controller;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Locale;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.LANGUAGES_URL;

@RestController
@RequestMapping(path = LANGUAGES_URL)
public class InternationalizationController {

    @RequestMapping(path="/current", method = RequestMethod.GET)
    public String getCurrentLanguage() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale.getLanguage();
    }
}
