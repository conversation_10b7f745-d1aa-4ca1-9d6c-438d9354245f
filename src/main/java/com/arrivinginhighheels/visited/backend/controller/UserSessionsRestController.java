package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.UserSessionsDTO;
import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.SessionRepository;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.UserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.GET_USER_SESSIONS_URL;

/**
 * REST controller for the admin's API methods for user sessions.
 */
@RestController
public class UserSessionsRestController {

    private final UserService userService;

    private final UserBuilder userBuilder;

    private final SessionRepository sessionRepository;

    public UserSessionsRestController(UserService userService, UserBuilder userBuilder, SessionRepository sessionRepository) {
        this.userService = userService;
        this.userBuilder = userBuilder;
        this.sessionRepository = sessionRepository;
    }

    /**
     * Get the user sessions. Only an user with ADMIN access level can use this method
     * @param username
     * @return
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @RequestMapping(path = GET_USER_SESSIONS_URL, method = RequestMethod.GET)
    public UserSessionsDTO getUserSessions(@PathVariable String username) {
        User user = userService.findUser(username);
        List<Session> sessions = sessionRepository.findByUser(user);

        return new UserSessionsDTO(userBuilder.createDTOFromUser(user), sessions);
    }
}
