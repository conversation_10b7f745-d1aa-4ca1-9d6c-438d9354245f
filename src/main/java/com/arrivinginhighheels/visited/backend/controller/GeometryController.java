package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.EnclosedWhiteListDTO;
import com.arrivinginhighheels.visited.backend.service.GeometryService;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.GEOMETRY_URL;

@RestController
@RequestMapping(path = GEOMETRY_URL)
public class GeometryController {

    private final GeometryService geometryService;
    private final ResponseHelper responseHelper;

    public GeometryController(GeometryService geometryService, ResponseHelper responseHelper) {
        this.geometryService = geometryService;
        this.responseHelper = responseHelper;
    }

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<Object> getLatestGeometry(HttpServletRequest request) throws URISyntaxException, IOException {
        return geometryService.fetchS3File(true, "countries", request);
    }

    @RequestMapping(path = "/{areaIsoCode}/subdivisions", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<Object> getSubdivisionsGeometry(@PathVariable String areaIsoCode, HttpServletRequest request) throws IOException, URISyntaxException {
        return geometryService.getSubdivisionGeometryFromS3(areaIsoCode, request);

    }

    @RequestMapping(path = "/whitelist", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<EnclosedWhiteListDTO>> getWhitelist(HttpServletRequest request) {
        Date lastModified = geometryService.getMaxModificationTimeOfWhiteList();
        return responseHelper.wrap(request, lastModified, ResponseHelper.standardCacheLength,
                (r) -> geometryService.getEnclosedWhiteList());
    }
}
