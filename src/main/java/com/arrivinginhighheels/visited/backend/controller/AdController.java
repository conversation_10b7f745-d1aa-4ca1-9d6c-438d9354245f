package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.AdInventoryDTO;
import com.arrivinginhighheels.visited.backend.service.AdServices;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AD_URL;

@RestController
@RequestMapping(path = AD_URL)
public class AdController {

    private final AdServices services;

    private final ResponseHelper responseHelper;

    public AdController(AdServices services, ResponseHelper responseHelper) {
        this.services = services;
        this.responseHelper = responseHelper;
    }

    @RequestMapping(path = "/inventories/{provider}", method = RequestMethod.GET, produces = "application/json")
    ResponseEntity<List<AdInventoryDTO>> retrieveInventoriesByProvider(@PathVariable String provider, HttpServletRequest request) {
        final String os = request.getHeader("x-app-platform");
        if (os == null) {
            throw new RuntimeException("Missing 'x-app-platform header'. Value must be iOS or Android");
        }
        final Date lastModified = services.getMaxLastModificationTimeOfAProviderAndOs(provider, os);

        return responseHelper.wrap(request, lastModified,
                (r) -> services.findInventoriesForProvider(provider, os));
    }
}
