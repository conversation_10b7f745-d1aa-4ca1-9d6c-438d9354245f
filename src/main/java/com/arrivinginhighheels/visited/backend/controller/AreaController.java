package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.*;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceDTO;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.service.AreaService;
import com.arrivinginhighheels.visited.backend.service.translations.AreaTranslationService;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AREAS_URL;

@RestController
@RequestMapping(path = AREAS_URL)
public class AreaController {

    private final ResponseHelper responseHelper;
    private final LoggedInUserUtil loggedInUserUtil;
    private final AreaService areaService;
    private final AreaTranslationService areaTranslationService;

    public AreaController(
            ResponseHelper responseHelper, LoggedInUserUtil loggedInUserUtil,
            AreaService areaService,
            AreaTranslationService areaTranslationService) {
        this.responseHelper = responseHelper;
        this.loggedInUserUtil = loggedInUserUtil;
        this.areaService = areaService;
        this.areaTranslationService = areaTranslationService;
    }

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<RegionDTO>> getFirstLevelAreasOrganizedByRegion(
            @RequestParam(value = "regions", required = false) final Boolean regions,
            final HttpServletRequest request) {

        final Boolean allowRegions = regions != null && regions;
        final String platform = request.getHeader("x-app-platform");
        final String version = request.getHeader("x-app-version");

        final Date lastModificationTime = areaService.getMaxModificationTimeOfAFirstLevelArea();
        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getFirstLevelAreasOrganizedByRegion(allowRegions, platform, version));
    }

    @RequestMapping(path = "updated", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<AreaUpdatesDTO> getAllUpdatedAreas(
            @RequestParam(value = "since", required = false) final String sinceDate, final HttpServletRequest request) {

        final String platform = request.getHeader("x-app-platform");
        final String version = request.getHeader("x-app-version");
        final Date lastModifiedDate = parseOptionalDateString(sinceDate);
        var updates = areaService.getLatestChanges(lastModifiedDate, platform, version);
        return responseHelper.standardCacheableResponse(updates);
    }

    private Date parseOptionalDateString(final String sinceDate) {
        if (sinceDate == null) {
            return new Date();
        }

        try {
            final int index = sinceDate.lastIndexOf('.');

            // Strip out milliseconds if needed
            final String dateStamp;
            if (index == -1) {
                dateStamp = sinceDate;
            } else {
                dateStamp = sinceDate.substring(0, index);
            }

            final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            return formatter.parse(dateStamp);
        } catch (final ParseException e) {
            return new Date();
        }
    }

    @RequestMapping(path = "/{areaIsoCode}/subdivisions", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<AreaDTO>> getAreasSubdivisions(
            @PathVariable final String areaIsoCode,
            @RequestParam(value = "regions", required = false) final Boolean regions,
            final HttpServletRequest request) {
        final Date lastModificationTime =
                areaService.getMaxModificationTimeOfNonCityChildrenAreaByItsParentsIsoCode(areaIsoCode);
        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getSubdivisionsOfAnAreaByTheAreasIsoCode(areaIsoCode, regions));
    }


    @RequestMapping(path = "/{areaIsoCode}/details", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<AreaDetailsDto> getAreaDetails(
            @PathVariable final String areaIsoCode,
            final HttpServletRequest request,
            @RequestParam(required = false) Double resolution
    ) {
        final Date lastModificationTime = areaService.getMaxModificationTimeOfAnAreaDetails(areaIsoCode);

        if (resolution == null) {
            resolution = 1.0;
        }

        final Double finalResolution = resolution;
        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getAreasDetailsByItsIsoCode(areaIsoCode, finalResolution));
    }

    @RequestMapping(path = "/{areaIsoCode}/experiences", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<ExperienceDTO>> getAreasExperiences(@PathVariable final String areaIsoCode, final HttpServletRequest request) {
        final Date lastModificationTime = areaService.getMaxModificationTimeOfExperiencesRelatedToAreaIsoCode(areaIsoCode);

        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getExperiencesRelateToAnAreaByIsoCode(areaIsoCode));
    }

    @RequestMapping(path = "/{areaIsoCode}", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<AreaDTO> getAreaBasicData(@PathVariable final String areaIsoCode, final HttpServletRequest request) {
        final GeoArea area = areaService.getGeoAreaByIsoCode(areaIsoCode);
        if (area == null) {
            throw new GeoAreaNotFoundException(areaIsoCode);
        }

        final var platform = request.getHeader("x-app-platform");
        final var version = request.getHeader("x-app-version");

        final var lastModificationTime = area.getLastModificationTime();
        final var lang = areaTranslationService.getCurrentLanguage();

        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.buildAreaDTOFromGeoArea(area, lang, false, platform, version));
    }

    @RequestMapping(path = "/{areaIsoCode}/cities", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<AreaDTO>> getAreasCities(@PathVariable final String areaIsoCode, final HttpServletRequest request) {
        final Date lastModificationTime = areaService.getMaxModificationTimeOfCitiesByItsParentsIsoCode(areaIsoCode);

        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getCitiesOfAnAreaByTheAreasIsoCode(areaIsoCode));
    }

    @RequestMapping(path = "/{areaIsoCode}/topPlaces", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<AreaDTO>> getAreasTopPlacesPeopleThatLivesInGoesTo(@PathVariable final String areaIsoCode,
                                                                                  final HttpServletRequest request) {
        final Date lastModificationTime = areaService.getMaxModificationTimeOfAFirstLevelArea();

        return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                (r) -> areaService.getAnAreaTopPlacesPeopleThatLivesInGoesTo(areaIsoCode));
    }

    @RequestMapping(path = "/{areaIsoCode}/notes", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<AreaUserNotesDTO> getAnAreasNotesForTheUser(@PathVariable final String areaIsoCode, final HttpServletRequest request) {
        final User loggedInUser = loggedInUserUtil.getLoggedInUser(request);
        var note = areaService.getUserNotesForAnAreaByItsIsoCode(loggedInUser, areaIsoCode);
        return responseHelper.doNotCacheResponse(note);
    }

    @RequestMapping(path = "/{areaIsoCode}/notes", method = RequestMethod.POST, produces = "application/json")
    public AreaUserNotesDTO insertOrUpdateUserNotesForAGivenArea(@PathVariable final String areaIsoCode,
                                                                 @RequestBody final AreaUserNotesDTO userNotesDTO,
                                                                 final HttpServletRequest request) {
        final User loggedInUser = loggedInUserUtil.getLoggedInUser(request);
        return areaService.addOrUpdateUserNotesForAGivenArea(loggedInUser, areaIsoCode, userNotesDTO);
    }


    @RequestMapping(path = "/disputed", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<DisputedTerritoryDTO>> getDisputedTerritories(
            @RequestHeader(value = "x-app-version") String version
    ) {
        var territories = areaService.getDisputedTerritories(version);
        return responseHelper.standardCacheableResponse(territories);
    }
}
