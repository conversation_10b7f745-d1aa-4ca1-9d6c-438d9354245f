package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.RankDTO;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.service.RankService;
import com.arrivinginhighheels.visited.backend.service.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.RANK_URL;

/**
 * REST Controller for the Rank API method.
 */
@RestController
public class RankRestController {

    private final LoggedInUserUtil loggedInUserUtil;

    private final RankService rankService;

    private final UserService userService;

    public RankRestController(LoggedInUserUtil loggedInUserUtil, RankService rankService, UserService userService) {
        this.loggedInUserUtil = loggedInUserUtil;
        this.rankService = rankService;
        this.userService = userService;
    }

    @GetMapping(path = RANK_URL)
    public RankDTO getRankForUser(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        userService.calculateRankForTheUser(user);
        return rankService.getUserRank(user);
    }
}
