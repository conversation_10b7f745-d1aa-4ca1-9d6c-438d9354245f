package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.DealsByAreaDTO;
import com.arrivinginhighheels.visited.backend.dto.DealsByExperienceDTO;
import com.arrivinginhighheels.visited.backend.service.DealsService;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.DEALS_URL;

@RestController
@RequestMapping(path = DEALS_URL)
public class DealsController {

    private final DealsService service;

    private final ResponseHelper responseHelper;

    public DealsController(DealsService service, ResponseHelper responseHelper) {
        this.service = service;
        this.responseHelper = responseHelper;
    }

    @RequestMapping(path = "/area/{areaIsoCode}")
    public ResponseEntity<DealsByAreaDTO> getAllDealsByArea(@PathVariable String areaIsoCode, HttpServletRequest request) {
        Date lastModified = service.getMaxModificationTimeOfDealsByAreaIsoCode(areaIsoCode);
        if (lastModified == null) {
            return ResponseEntity.noContent().build();
        }

        return responseHelper.wrap(request, lastModified,
                (r) -> service.findByArea(areaIsoCode));
    }

    @RequestMapping(path = "/experience/{experienceId}")
    public ResponseEntity<DealsByExperienceDTO> getAllDealsByExperience(@PathVariable Long experienceId, HttpServletRequest request) {
        Date lastModified = service.getMaxModificationTimeOfDealsByExperienceId(experienceId);

        if (lastModified == null) {
            return ResponseEntity.noContent().build();
        }

        return responseHelper.wrap(request, lastModified,
                (r) -> service.findByExperience(experienceId));
    }
}
