package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.config.Constants;
import com.arrivinginhighheels.visited.backend.dto.SelectionLogsDTO;
import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.model.SelectionLog;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * REST controller for handling the selection logs retrieval and resetting
 */
@RestController
public class UserSelectionLogsRestController {

    private final UserRepository userRepository;

    private final SelectionService selectionService;

    public UserSelectionLogsRestController(UserRepository userRepository, SelectionService selectionService) {
        this.userRepository = userRepository;
        this.selectionService = selectionService;
    }

    /**
     * Retrieves all the selection logs for the user with the informed username. Admin access level required
     *
     * @param username
     * @return
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @RequestMapping(path = Constants.Routes.GET_USER_SELECTION_LOGS_URL, method = RequestMethod.GET)
    public SelectionLogsDTO getUserSelectionLogs(@PathVariable String username) {
        var user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UserNotFoundByUsernameException(username));

        List<SelectionLog> selections = selectionService.getAllLogsForTheUser(user);

        return new SelectionLogsDTO(user, selections);
    }

    /**
     * Clears all the selection logs for the user with the informed username. Admin access level required
     *
     * @param username
     * @return
     */
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @ResponseStatus(HttpStatus.ACCEPTED)
    @RequestMapping(path = Constants.Routes.RESET_USER_SELECTION_LOGS_URL, method = RequestMethod.PUT)
    public SelectionLogsDTO resetUserSelectionLogs(@PathVariable String username) {
        var user = userRepository.findByUsername(username)
                .orElseThrow(() -> new UserNotFoundByUsernameException(username));

        selectionService.resetUserSelectionLogs(user);

        return new SelectionLogsDTO(user, new ArrayList<>());
    }

}
