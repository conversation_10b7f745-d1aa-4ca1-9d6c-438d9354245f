package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.SojernTrackingDTO;
import com.arrivinginhighheels.visited.backend.service.SojernService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SOJERN_URL;

@Deprecated
@RestController
@RequestMapping(path = SOJERN_URL)
public class SojernController {

    private final SojernService service;

    public SojernController(SojernService service) {
        this.service = service;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    Boolean trackSojernEvent(
            @Valid @RequestBody SojernTrackingDTO trackingDetails,
            HttpServletRequest request) {
        return service.sendEvent(trackingDetails, request);
    }
}
