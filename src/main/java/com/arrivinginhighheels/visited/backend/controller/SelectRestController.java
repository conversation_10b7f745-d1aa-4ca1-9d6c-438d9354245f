package com.arrivinginhighheels.visited.backend.controller;

import com.arrivinginhighheels.visited.backend.dto.MultipleSelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.dto.UserBeenCountsDTO;
import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.model.Selection;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.service.RankService;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import com.arrivinginhighheels.visited.backend.service.UserService;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.*;

/**
 * Controller for the geoareas selection operations
 */
@RestController
public class SelectRestController {

        private final LoggedInUserUtil loggedInUserUtil;

        private final SelectionService selectionService;

        private final UserService userService;

        private final RankService rankService;

        public SelectRestController(LoggedInUserUtil loggedInUserUtil, SelectionService selectionService,
                        UserService userService, RankService rankService) {
                this.loggedInUserUtil = loggedInUserUtil;
                this.selectionService = selectionService;
                this.userService = userService;
                this.rankService = rankService;
        }

        @RequestMapping(path = SELECT_URL, method = RequestMethod.GET)
        public SelectionsDTO getSelections(final HttpServletRequest request) {
                final User user = loggedInUserUtil.getLoggedInUser(request);
                return selectionService.getUserSelections(user);
        }

        @RequestMapping(path = SELECT_URL, method = RequestMethod.POST)
        public SelectionsDTO select(final HttpServletRequest request,
                        @Valid @RequestBody final SelectionDTO selection) {

                final User user = loggedInUserUtil.getLoggedInUser(request);

                if (selection.getSelectionType() == null) {
                        throw new RuntimeException("Missing selectionType. Value must be LIVED, BEEN, WANT or CLEAR");
                }

                return selectionService.handleSelection(user, selection.getAreaIsoKey(), selection.getSelectionType());
        }

        /**
         * Handles the selection of more than one Geo Areas at once.
         *
         */
        @RequestMapping(path = SELECT_MULTIPLE_URL, method = RequestMethod.POST)
        public SelectionsDTO selectMultiple(final HttpServletRequest request,
                        @Valid @RequestBody final MultipleSelectionDTO selections) {

                final User user = loggedInUserUtil.getLoggedInUser(request);

                final List<Selection> selectionList = selectionService.handleMultipleSelection(user, selections);

                final SelectionsDTO selectionsDTO = new SelectionsDTO(selectionList);
                selectionsDTO.setRank(rankService.getUserRank(user));
                return selectionsDTO;
        }

        /**
         * Clears all the selected geoAreas for the user with the informed username.
         * Admin access level required
         */
        @ResponseStatus(HttpStatus.ACCEPTED)
        @PreAuthorize("hasRole('ROLE_ADMIN')")
        @RequestMapping(path = RESET_USER_SELECTIONS_URL, method = RequestMethod.PUT)
        public SelectionsDTO resetUserSelections(@PathVariable final String username) {

                final User user = userService.findUser(username);

                if (user == null) {
                        throw new UserNotFoundByUsernameException(username); // 404
                }

                selectionService.resetUserSelections(user);

                return new SelectionsDTO();
        }

        @RequestMapping(path = SELECT_URL + "/counts", method = RequestMethod.GET)
        public UserBeenCountsDTO getBeenCounters(final HttpServletRequest request) {
                final User user = loggedInUserUtil.getLoggedInUser(request);
                return selectionService.getAllBeenCountsForUser(user);
        }

        @RequestMapping(path = SELECT_URL + "/counts", method = RequestMethod.POST)
        public boolean updateBeenCounters(final HttpServletRequest request,
                        @RequestBody final Map<String, Integer> counts) {
                final User user = loggedInUserUtil.getLoggedInUser(request);
                selectionService.updateUserBeenCounters(user, counts);
                return true;
        }
}
