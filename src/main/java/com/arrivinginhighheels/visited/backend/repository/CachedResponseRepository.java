package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.CachedResponse;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.repository.CrudRepository;

public interface CachedResponseRepository extends CrudRepository<CachedResponse, Long> {
    CachedResponse findByPathAndSupportedLanguage(String path, SupportedLanguage lang);
}
