package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaAvailability;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface GeoAreaAvailabilityRepository extends JpaRepository<GeoAreaAvailability, Long> {
    GeoAreaAvailability findByGeoArea(GeoArea area);

    @Query("select a from GeoAreaAvailability a where a.geoArea.parent.isoKey = ?1")
    List<GeoAreaAvailability> findAllSubdivisionsByParentIsoKey(String isoKey);
}
