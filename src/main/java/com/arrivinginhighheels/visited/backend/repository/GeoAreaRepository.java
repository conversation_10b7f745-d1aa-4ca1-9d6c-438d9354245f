package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface GeoAreaRepository extends JpaRepository<GeoArea, Long> {

    Optional<GeoArea> findByIsoKey(String isoKey);

    List<GeoArea> findAllByLastModificationTimeGreaterThan(Date date);

    @Query("select a from GeoArea a where a.parent.isoKey = ?1")
    List<GeoArea> findAllSubdivisionsByParentIsoKey(String parentAreaIsoCode);

    @Query("select count(a.id) from GeoArea a where a.parent.id = ?1")
    Integer countSubdivisionsByParentId(Long parentAreaId);

    @Query("select a from GeoArea a where a.parent.isoKey = ?1 and a.type = 'CITY'")
    List<GeoArea> findAllCitiesByParentIsoKey(String areaIsoCode);

    @Query("""
            select t.beenGeoArea
            from TopPlaces t
            where t.livedGeoArea = ?1
              and t.beenGeoArea.parent is null
            order by t.numberOfUsersBeen DESC
            """)
    List<GeoArea> findAllTopPlacesThatAreCountriesFromLivedGeoArea(GeoArea livedArea, Pageable p);

    default List<GeoArea> findTop10TopPlacesThatAreCountriesFromLivedGeoArea(GeoArea livedArea) {
        return findAllTopPlacesThatAreCountriesFromLivedGeoArea(livedArea, PageRequest.of(0,10));
    }

    @Query("select max(g.lastModificationTime) from GeoArea g where g.parent is null")
    Date findMaxLastModificationTimeOfAFirstLevelArea();

    @Query("select max(g.lastModificationTime) from GeoArea g where g.parent.isoKey = ?1 and g.type <> 'CITY'")
    Date findMaxModificationTimeOfNonCityChildrenAreaByItsParentsIsoCode(String areaIsoCode);

    @Query("select max(g.lastModificationTime) from GeoArea g where g.parent.isoKey = ?1 and g.type = 'CITY'")
    Date findMaxModificationTimeOfCitiesByItsParentsIsoCode(String areaIsoCode);

    @Query("select max(g.lastModificationTime) from GeoArea g where g.parent = ?1 and g.type <> 'CITY'")
    Date findMaxModificationTimeOfNonCityChildrenAreaByItsParent(GeoArea area);

    @Query("select max(g.lastModificationTime) from GeoArea g where g.parent = ?1 and g.type = 'CITY'")
    Date findMaxModificationTimeOfCitiesByItsParent(GeoArea area);

    Optional<GeoArea> findFirstByIsoKeyContainingIgnoreCaseAndParentIsNull(String countryCode);

    @Query("select count(g.id) from GeoArea g where g.parent = ?1")
    Integer countChildrenByParent(GeoArea area);
}
