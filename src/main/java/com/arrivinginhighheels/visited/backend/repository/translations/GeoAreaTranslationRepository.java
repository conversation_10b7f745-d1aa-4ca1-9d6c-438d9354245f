package com.arrivinginhighheels.visited.backend.repository.translations;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface GeoAreaTranslationRepository extends JpaRepository<GeoAreaTranslation, Long> {
    GeoAreaTranslation findByGeoAreaAndSupportedLanguage(GeoArea geoArea, SupportedLanguage supportedLanguage);

    @Query("select a from GeoAreaTranslation a where a.geoArea.id = ?1")
    List<GeoAreaTranslation> findAllGeoAreaId(Long geoAreaId);

    List<GeoAreaTranslation> findBySupportedLanguageAndGeoAreaIdIn(SupportedLanguage supportedLanguage, List<Long> geoAreaIds);
}
