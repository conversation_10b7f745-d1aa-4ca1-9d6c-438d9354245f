package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserRepository extends JpaRepository<User, Long> {

    Optional<User> findByUsername(String username);

    Long getRankForTheUser(Long id);

    Integer getTotalNumberOfAreasVisitedForTheUser(Long idUser);

    void delete(User user);

    @Query("""
    select u.username
    from User u
    INNER join Selection s on  u = s.user
    where
        s.type = 'LIVED' AND
        s.geoArea.id = 9 AND
        u.unsubscribed = FALSE AND
        u.username not like '%anonymous.com%' and
        u.username NOT LIKE '%.uk' AND
        u.username NOT LIKE '%.de' AND
        u.username NOT LIKE '%.it' AND
        u.username NOT LIKE '%.nl' AND
        u.username NOT LIKE '%.no' AND
        u.username NOT LIKE '%.pl' AND
        u.username NOT LIKE '%.pt' AND
        u.username NOT LIKE '%.fr' AND
        u.username NOT LIKE '%.es' AND
        u.username NOT LIKE '%.se' AND
        u.username NOT LIKE '%.hr' AND
        u.username NOT LIKE '%.be' AND
        u.username NOT LIKE '%.ca' AND
        u.username NOT LIKE '%.au' AND
        u.username NOT LIKE '%.jp' AND
        u.username NOT LIKE '%.kr' AND
        u.username NOT LIKE '%.at' AND
        u.username NOT LIKE '%.cz' AND
        u.username NOT LIKE '%.dk' AND
        u.username NOT LIKE '%.eu' AND
        u.username NOT LIKE '%.fl' AND
        u.username NOT LIKE '%.gr' AND
        u.username NOT LIKE '%.nz' AND
        u.username NOT LIKE '%.ro' AND
        u.username NOT LIKE '%.is' AND
        u.username NOT LIKE '%.hu' AND
        u.username NOT LIKE '%.is' AND
        u.username NOT LIKE '%.ie' AND
        u.username NOT LIKE '%.ch' AND
        u.username NOT LIKE '%.tr' AND
        u.username NOT LIKE '%.ua'
    """)
    List<String> selectAmericanEmailList();
}
