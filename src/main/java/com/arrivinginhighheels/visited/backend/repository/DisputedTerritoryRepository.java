package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.DisputedTerritory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;

public interface DisputedTerritoryRepository extends JpaRepository<DisputedTerritory, Long> {
    @Query("select max(dt.lastModificationTime) from DisputedTerritory dt")
    Date getMaxModificationTime();
}
