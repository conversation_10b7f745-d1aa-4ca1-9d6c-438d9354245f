package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Repository for User Session objects
 */
public interface SessionRepository extends JpaRepository<Session, Long> {
    List<Session> findByUser(User user);

    @Transactional
    Long deleteByUser(User user);

    @Modifying
    @Query(value = "DELETE from sessions where user_id = ?1", nativeQuery = true)
    void deleteByUserId(Long id);
}
