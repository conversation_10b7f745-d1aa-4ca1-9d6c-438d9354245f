package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.UserBeenCount;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UserBeenCountRepository extends JpaRepository<UserBeenCount, Long> {
    List<UserBeenCount> findAllByUser(User user);

    Optional<UserBeenCount> findByUserAndGeoArea(User user, GeoArea geoArea);

    Long deleteByUser(User user);
}
