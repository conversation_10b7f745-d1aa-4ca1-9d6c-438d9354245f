package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.Selection;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface SelectionRepository extends JpaRepository<Selection, Long> {

    List<Selection> findAllByUser(User user);

    List<Selection> findAllByUserAndGeoArea(User user, GeoArea geoArea);

    List<Selection> findAllByUserAndType(User user, SelectionType type);

    Selection findByUserAndGeoArea(User user, GeoArea area);

    @Transactional
    Long deleteByUser(User user);

    List<Selection> findAllByUserAndGeoArea_Parent(User user, GeoArea parentGeoArea);

    List<Selection> findAllByUserAndTypeAndGeoArea(User user, SelectionType type, GeoArea geoArea);

    @Modifying
    @Query(value = "DELETE from selections where user_id = ?1", nativeQuery = true)
    void deleteByUserId(Long id);


}
