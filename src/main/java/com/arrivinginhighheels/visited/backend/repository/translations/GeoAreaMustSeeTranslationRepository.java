package com.arrivinginhighheels.visited.backend.repository.translations;

import com.arrivinginhighheels.visited.backend.model.GeoAreaMustSee;
import com.arrivinginhighheels.visited.backend.model.GeoAreaMustSeeTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GeoAreaMustSeeTranslationRepository extends JpaRepository<GeoAreaMustSeeTranslation, Long> {

    GeoAreaMustSeeTranslation findByGeoAreaMustSeeAndSupportedLanguage(GeoAreaMustSee geoAreaMustSee, SupportedLanguage lang);

}
