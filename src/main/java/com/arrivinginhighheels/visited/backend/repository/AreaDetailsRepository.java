package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.AreaDetails;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;

public interface AreaDetailsRepository extends JpaRepository<AreaDetails, Long> {

    @Query("select ad from AreaDetails ad where ad.geoArea = ?1")
    AreaDetails findByGeoArea(GeoArea area);

    @Query("select ad.lastModificationTime from AreaDetails ad where ad.geoArea = ?1")
    Date findMaxLastModificationTimeOfAnAreaByGeoArea(GeoArea area);

    @Query("select ad.lastModificationTime from AreaDetails ad where ad.geoArea = ?1")
    Date findMaxLastModificationTimeOfAnArea(GeoArea area);
}
