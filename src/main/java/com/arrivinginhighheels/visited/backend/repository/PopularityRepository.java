package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.Popularity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface PopularityRepository extends JpaRepository<Popularity, Long> {

    @Query("select p from Popularity p where p.geoArea.isoKey = ?1")
    Popularity findByGeoAreaIsoKey(String areaIsoCode);

    @Query("select coalesce(max(p.rank), 0) + 1 from Popularity p")
    Integer getLastPositionInRanking();
}
