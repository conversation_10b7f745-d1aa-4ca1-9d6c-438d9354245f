package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.Deal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface DealsRepository extends JpaRepository<Deal, Long> {

    @Query("select d from Deal d where d.geoArea.id = ?1")
    List<Deal> findAllByAreaId(Long areaId);

    @Query("select d from Deal d where d.experience.id = ?1")
    List<Deal> findAllByExperienceId(Long experienceId);

    @Query( "select max(d.lastModificationTime) " +
            "     from Deal d " +
            "    where d.geoArea.isoKey = ?1")
    Date getMaxModificationTimeOfDealsByAreaIsoCode(String areaIsoCode);

    @Query( "select max(d.lastModificationTime) " +
            "     from Deal d " +
            "    where d.experience.id = ?1")
    Date getMaxModificationTimeOfDealsByExperienceId(Long experienceId);

}
