package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.AdCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface AdCategoryRepository extends JpaRepository<AdCategory, Long> {

    @Query( "select max(ac.lastModificationTime) " +
            "     from AdCategory ac " +
            "    where ac.provider = ?1 and ac.os = ?2")
    Date getMaxModificationTimeByProviderAndOs(String provider, String os);

    List<AdCategory> findByProviderAndOs(String provider, String os);
}
