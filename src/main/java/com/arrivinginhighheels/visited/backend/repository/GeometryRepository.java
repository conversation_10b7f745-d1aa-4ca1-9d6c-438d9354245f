package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.Geometry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface GeometryRepository extends JpaRepository<Geometry, Long> {
    List<Geometry> findAllByLastModificationTimeGreaterThan(Date date);

    @Query("select g.lastModificationTime from Geometry g where g.file = ?1")
    Date findMaxLastModificationTimeOfAnAreaByItsIsoCode(String areaIsoCode);
}
