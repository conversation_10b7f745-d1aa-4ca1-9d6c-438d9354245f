package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaUserNotes;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface GeoAreaUserNotesRepository extends JpaRepository<GeoAreaUserNotes, Long> {

    GeoAreaUserNotes findByGeoAreaAndUser(GeoArea geoArea, User user);

    @Modifying
    @Query(value = "DELETE from geoarea_user_notes where user_id = ?1", nativeQuery = true)
    void deleteByUserId(Long id);

}
