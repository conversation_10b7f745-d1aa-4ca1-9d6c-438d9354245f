package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.SelectionLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * Repository for the user selection logs
 */
public interface SelectionLogRepository extends JpaRepository<SelectionLog, Long> {

    List<SelectionLog> findAllByUserIdOrderByTimestampAsc(Long userId);

    @Modifying
    @Query(value = "DELETE from selection_logs where user_id = ?1", nativeQuery = true)
    void deleteByUserId(Long userId);

}
