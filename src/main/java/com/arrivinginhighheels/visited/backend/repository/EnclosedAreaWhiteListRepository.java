package com.arrivinginhighheels.visited.backend.repository;

import com.arrivinginhighheels.visited.backend.model.EnclosedAreaWhiteList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;

public interface EnclosedAreaWhiteListRepository extends JpaRepository<EnclosedAreaWhiteList, Long> {
    @Query("select max(eaw.lastModificationTime) from EnclosedAreaWhiteList eaw")
    Date findMaxLastModificationTime();
}
