package com.arrivinginhighheels.visited.backend.repository.translations;

import com.arrivinginhighheels.visited.backend.model.Region;
import com.arrivinginhighheels.visited.backend.model.RegionTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface RegionTranslationRepository extends JpaRepository<RegionTranslation, Long> {

    RegionTranslation findByRegionAndSupportedLanguage(Region region, SupportedLanguage supportedLanguage);

    @Query("select r from RegionTranslation r where r.region.id = ?1")
    List<RegionTranslation> findAllRegionById(Long id);
}
