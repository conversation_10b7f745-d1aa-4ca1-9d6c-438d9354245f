package com.arrivinginhighheels.visited.backend;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.utils.CustomHeaderLocaleResolver;
import org.apache.catalina.filters.RemoteIpFilter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.filter.ShallowEtagHeaderFilter;
import org.springframework.web.servlet.LocaleResolver;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sesv2.SesV2Client;

@SpringBootApplication
@EnableAsync
@EnableScheduling
@EntityScan(basePackages = "com.arrivinginhighheels.visited.backend")
public class VisitedBackendApplication {

	private final YamlConfig config;

	public VisitedBackendApplication(YamlConfig config) {
		this.config = config;
	}

	public static void main(String[] args) {
		SpringApplication.run(VisitedBackendApplication.class, args);
	}

	@Bean
	public LocaleResolver localeResolver() {
		return new CustomHeaderLocaleResolver();
	}

	@Bean
	public FilterRegistrationBean<ShallowEtagHeaderFilter> etagFilterRegistrationBean() {
		final var eTagFilter = new ShallowEtagHeaderFilter();
		final var registration = new FilterRegistrationBean<ShallowEtagHeaderFilter>();
		registration.setFilter(eTagFilter);
		registration.addUrlPatterns("/*");
		return registration;
	}

	@Bean
	public FilterRegistrationBean<CharacterEncodingFilter> utf8FilterRegistrationBean() {
		final var filter = new CharacterEncodingFilter();
		filter.setEncoding("utf-8");
		filter.setForceEncoding(true);

		final var registrationBean = new FilterRegistrationBean<CharacterEncodingFilter>();
		registrationBean.setFilter(filter);
		registrationBean.addUrlPatterns("/*");
		return registrationBean;
	}

	@Bean
	public JavaMailSender getJavaMailSender() {
		final var mailSender = new JavaMailSenderImpl();
		mailSender.setHost(config.getEmailHost());
		mailSender.setPort(config.getEmailPort());
		mailSender.setUsername(config.getEmailAddress());
		mailSender.setPassword(config.getEmailPassword());

		final var properties = mailSender.getJavaMailProperties();
		properties.put("mail.transport.protocol", "smtp");
		properties.put("mail.smtp.auth", "true");
		properties.put("mail.smtp.starttls.enable", "true");
		properties.put("mail.debug", "true");

		return mailSender;
	}

	@Bean
	public SesV2Client primarySesClient(YamlConfig config) {
		return SesV2Client.builder()
				.region(Region.of(config.getAws1Region()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create(config.getAws1Key(), config.getAws1Secret())
				))
				.build();
	}

	@Bean
	public SesV2Client secondarySesClient(YamlConfig config) {
		return SesV2Client.builder()
				.region(Region.of(config.getAws2Region()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create(config.getAws2Key(), config.getAws2Secret())
				))
				.build();
	}

	@Bean
	public RemoteIpFilter remoteIpFilter() {
		return new RemoteIpFilter();
	}

}
