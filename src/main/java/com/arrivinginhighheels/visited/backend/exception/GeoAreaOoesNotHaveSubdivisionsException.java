package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class GeoAreaOoesNotHaveSubdivisionsException extends RuntimeException {
    public GeoAreaOoesNotHaveSubdivisionsException(String isoKeyForTheSelectedArea) {
        super("There are no subdivisions for GeoArea with the ISO Key " + isoKeyForTheSelectedArea);
    }
}
