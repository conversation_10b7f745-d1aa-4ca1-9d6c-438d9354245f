package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.io.Serializable;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ResourceNotFoundException extends RuntimeException {

    private final String resourceName;
    private final Serializable resourceId;

    public ResourceNotFoundException(String resourceName, Serializable resourceId) {
        super("Could not find " + resourceName + " with the id/code " + resourceId);
        this.resourceName = resourceName;
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public Serializable getResourceId() {
        return resourceId;
    }
}
