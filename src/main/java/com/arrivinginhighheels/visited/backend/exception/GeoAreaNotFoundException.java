package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Exception to be throw in a search for a geo area with no results.
 */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class GeoAreaNotFoundException extends RuntimeException {

    public GeoAreaNotFoundException(String isoKeyForTheSelectedArea) {
        super("Could not find any GeoArea with the ISO Key " + isoKeyForTheSelectedArea);
    }
}

