package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class UserNotFoundByUsernameException extends RuntimeException {

    public UserNotFoundByUsernameException(String username) {
        super("Could not find user with the username " + username);
    }

}
