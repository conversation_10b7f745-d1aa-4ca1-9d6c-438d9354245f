package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class UserNotFoundException extends ResourceNotFoundException {

    private static final String RESOURCE_NAME = "User";

    public UserNotFoundException(Long resourceId) {
        super(RESOURCE_NAME, resourceId);
    }
}
