package com.arrivinginhighheels.visited.backend.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.Arrays;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class InsufficientParametersException extends RuntimeException {

    private final String[] parametersNotFound;

    public InsufficientParametersException(String... requiredParams) {
        super("Insufficient parameters informed. Required params: " + Arrays.toString(requiredParams));
        this.parametersNotFound = requiredParams;
    }

    public String[] getParametersNotFound() {
        return parametersNotFound;
    }
}
