package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SojernEvent;

import jakarta.validation.constraints.NotNull;

public class SojernTrackingDTO {
    @NotNull
    private String bundleId;

    @NotNull
    private String os;

    @NotNull
    private String osVersion;

    @NotNull
    private String appVersion;

    @NotNull
    private String deviceBrand;

    @NotNull
    private String deviceCarrier;

    @NotNull
    private String deviceModel;

    @NotNull
    private String advertisingId;

    @NotNull
    private String networkType;

    @NotNull
    private SojernEvent eventType;

    private String wantAreaCode;
    private long wantExperienceId;

    public SojernTrackingDTO() {
    }

    public String getBundleId() {
        return bundleId;
    }

    public void setBundleId(String bundleId) {
        this.bundleId = bundleId;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getDeviceBrand() {
        return deviceBrand;
    }

    public void setDeviceBrand(String deviceBrand) {
        this.deviceBrand = deviceBrand;
    }

    public String getDeviceCarrier() {
        return deviceCarrier;
    }

    public void setDeviceCarrier(String deviceCarrier) {
        this.deviceCarrier = deviceCarrier;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getAdvertisingId() {
        return advertisingId;
    }

    public void setAdvertisingId(String advertisingId) {
        this.advertisingId = advertisingId;
    }

    public boolean isUsingWifi() {
        return networkType.equals("WiFi");
    }

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }

    public SojernEvent getEventType() {
        return eventType;
    }

    public void setEventType(SojernEvent eventType) {
        this.eventType = eventType;
    }

    public String getWantAreaCode() {
        return wantAreaCode;
    }

    public void setWantAreaCode(String wantAreaCode) {
        this.wantAreaCode = wantAreaCode;
    }

    public long getWantExperienceId() {
        return wantExperienceId;
    }

    public void setWantExperienceId(long wantExperienceId) {
        this.wantExperienceId = wantExperienceId;
    }
}
