package com.arrivinginhighheels.visited.backend.dto;

import java.util.Map;

public class DealsByExperienceDTO {
    private String url;
    private Map<String, String> areas;

    public DealsByExperienceDTO() {
    }

    public DealsByExperienceDTO(String url, Map<String, String> areas) {
        this.url = url;
        this.areas = areas;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<String, String> getAreas() {
        return areas;
    }

    public void setAreas(Map<String, String> areas) {
        this.areas = areas;
    }

    @Override
    public String toString() {
        return "DealsByExperienceDTO{" +
                "url='" + url + '\'' +
                ", areas=" + areas +
                '}';
    }
}
