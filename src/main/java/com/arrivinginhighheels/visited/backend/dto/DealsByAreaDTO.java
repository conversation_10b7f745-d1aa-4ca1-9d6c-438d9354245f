package com.arrivinginhighheels.visited.backend.dto;
import java.util.Map;

public class DealsByAreaDTO {
    private String url;
    private Map<Long, String> experiences;

    public DealsByAreaDTO() {}

    public DealsByAreaDTO(String url, Map<Long, String> experiences) {
        this.url = url;
        this.experiences = experiences;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Map<Long, String> getExperiences() {
        return experiences;
    }

    public void setExperiences(Map<Long, String> experiences) {
        this.experiences = experiences;
    }

    @Override
    public String toString() {
        return "DealsByAreaDTO{" +
                "url='" + url + '\'' +
                ", experiences=" + experiences +
                '}';
    }
}
