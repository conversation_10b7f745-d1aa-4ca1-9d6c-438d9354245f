package com.arrivinginhighheels.visited.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.*;

@JsonInclude(NON_NULL)
public class AdInventoryDTO {
    private String name;
    private String hash;
    private List<String> areaCodes;
    private List<Long> experienceIds;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public List<String> getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(List<String> areaCodes) {
        this.areaCodes = areaCodes;
    }

    public List<Long> getExperienceIds() {
        return experienceIds;
    }

    public void setExperienceIds(List<Long> experienceIds) {
        this.experienceIds = experienceIds;
    }

    public void addAreaCode(String isoKey) {
        if (areaCodes == null) {
            areaCodes = new ArrayList<>();
        }

        areaCodes.add(isoKey);
    }

    public void addExperienceId(Long id) {
        if (experienceIds == null) {
            experienceIds = new ArrayList<>();
        }

        experienceIds.add(id);
    }
}
