package com.arrivinginhighheels.visited.backend.dto;

public class EnclosedWhiteListDTO {
    private String areaKey;
    private String surroundingKey;

    public EnclosedWhiteListDTO() {
    }

    public EnclosedWhiteListDTO(String areaKey, String surroundingKey) {
        this.areaKey = areaKey;
        this.surroundingKey = surroundingKey;
    }

    public String getAreaKey() {
        return areaKey;
    }

    public EnclosedWhiteListDTO setAreaKey(String areaKey) {
        this.areaKey = areaKey;
        return this;
    }

    public String getSurroundingKey() {
        return surroundingKey;
    }

    public EnclosedWhiteListDTO setSurroundingKey(String surroundingKey) {
        this.surroundingKey = surroundingKey;
        return this;
    }
}
