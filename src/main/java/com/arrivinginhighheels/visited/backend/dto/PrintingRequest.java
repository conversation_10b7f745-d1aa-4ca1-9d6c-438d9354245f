package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SelectionType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
public class PrintingRequest {
    private MapType mapType;
    private List<SelectionType> selectionTypes;
    private Optional<Map<String, Object>> selections;
    private Map<String, Object> disputedTerritoryPreferences;
    private AddressDto shippingAddress;
    private PaletteDto palette;
}

