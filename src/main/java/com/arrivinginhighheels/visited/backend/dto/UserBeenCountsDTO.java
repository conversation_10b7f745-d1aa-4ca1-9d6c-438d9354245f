package com.arrivinginhighheels.visited.backend.dto;

import java.util.Map;

public class UserBeenCountsDTO {
    Map<String, Integer> counts;

    public UserBeenCountsDTO(Map<String, Integer> counts) {
        this.counts = counts;
    }

    public Map<String, Integer> getCounts() {
        return counts;
    }

    public void setCounts(Map<String, Integer> counts) {
        this.counts = counts;
    }
}
