package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;

import java.util.List;

/**
 * Represents an user collection to the API user
 */
public class UsersDTO {

    private List<UserDTO> users;

    public UsersDTO() {
    }

    public UsersDTO(List<UserDTO> users) {
        this.users = users;
    }

    public List<UserDTO> getUsers() {
        return users;
    }

    public void setUsers(List<UserDTO> users) {
        this.users = users;
    }
}
