package com.arrivinginhighheels.visited.backend.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class DisputedTerritoryDTO {
    private AreaSimpleDTO area;
    private List<DisputedTerritoryOptionDTO> options;

    public DisputedTerritoryDTO(final AreaSimpleDTO area, final List<DisputedTerritoryOptionDTO> options) {
        this.area = area;
        this.options = options;
    }

}

