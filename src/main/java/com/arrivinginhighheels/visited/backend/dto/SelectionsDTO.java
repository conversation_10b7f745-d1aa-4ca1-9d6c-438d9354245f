package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.Selection;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DTO that represents the selections of geoareas to an user.
 */
@Setter
@Getter
public class SelectionsDTO {
    private List<String> livedAreas = new ArrayList<>();
    private List<String> beenAreas = new ArrayList<>();
    private List<String> wantAreas = new ArrayList<>();
    private List<String> pastLivedAreas = new ArrayList<>();
    private RankDTO rank;

    public SelectionsDTO() {
    }

    public SelectionsDTO(final List<Selection> selectionList) {
        livedAreas = getListOfIsoKeysBySelectionType(selectionList, SelectionType.LIVED);
        beenAreas = getListOfIsoKeysBySelectionType(selectionList, SelectionType.BEEN);
        wantAreas = getListOfIsoKeysBySelectionType(selectionList, SelectionType.WANT);
        pastLivedAreas = getListOfIsoKeysBySelectionType(selectionList, SelectionType.PAST_LIVED);
    }

    private List<String> getListOfIsoKeysBySelectionType(final List<Selection> selectionList, final SelectionType type) {
        return selectionList.stream()
                .filter((selection -> selection.getType() == type))
                .map(selection -> selection.getGeoArea().getIsoKey())
                .collect(Collectors.toList());
    }

    @Override
    public String toString() {
        return "SelectionsDTO{" +
                "livedAreas=" + livedAreas +
                ", beenAreas=" + beenAreas +
                ", wantAreas=" + wantAreas +
                ", rank=" + rank +
                '}';
    }
}
