package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.features.privacy.TriStateValue;

import jakarta.validation.constraints.NotNull;

public class PrivacyAgreementDTO {

    @NotNull
    private TriStateValue optIn;

    @NotNull
    private TriStateValue terms;

    public PrivacyAgreementDTO() {
    }

    public TriStateValue getOptIn() {
        return optIn;
    }

    public void setOptIn(TriStateValue optIn) {
        this.optIn = optIn;
    }

    public TriStateValue getTerms() {
        return terms;
    }

    public void setTerms(TriStateValue terms) {
        this.terms = terms;
    }

}
