package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SelectionLog;
import com.arrivinginhighheels.visited.backend.model.User;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Represents the Selection Logs for the API user
 */
public class SelectionLogsDTO {

    private List<SelectionLogDTO> history = new ArrayList<>();
    private String userName;

    public SelectionLogsDTO() {
    }

    public SelectionLogsDTO(User user, List<SelectionLog> theLogs) {
        this.userName = user.getUsername();
        this.history = theLogs.stream()
                              .map(SelectionLogDTO::new)
                              .collect(Collectors.toList());
    }

    public List<SelectionLogDTO> getHistory() {
        return history;
    }

    public void setHistory(List<SelectionLogDTO> history) {
        this.history = history;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
