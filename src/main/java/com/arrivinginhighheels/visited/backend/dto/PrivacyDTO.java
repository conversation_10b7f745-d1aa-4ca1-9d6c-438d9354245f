package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.features.privacy.PrivacyAgreement;
import com.arrivinginhighheels.visited.backend.features.privacy.TriStateValue;

import java.util.Date;

public class PrivacyDTO extends PrivacyAgreementDTO {

    private boolean required;
    private Date timestamp;

    public PrivacyDTO() {
    }

    public PrivacyDTO(PrivacyAgreement privacyAgreement) {
        this.setRequired(privacyAgreement.getRequired());
        this.setOptIn(privacyAgreement.getOptin());
        this.setTerms(privacyAgreement.getTerms());
        this.setTimestamp(privacyAgreement.getTimestamp());
    }

    public PrivacyDTO(boolean required, TriStateValue optIn, TriStateValue terms) {
        this.setRequired(required);
        this.setOptIn(optIn);
        this.setTerms(terms);
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }
}
