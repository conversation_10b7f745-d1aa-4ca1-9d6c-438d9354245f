package com.arrivinginhighheels.visited.backend.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter @Setter @NoArgsConstructor
public class PaletteDto {
    private String name;
    private List<Integer> water;
    private List<Integer> land;
    private List<Integer> live;
    private List<Integer> lived;
    private List<Integer> been;
    private List<Integer> want;
    private List<Integer> border;
    private List<Integer> label;
}
