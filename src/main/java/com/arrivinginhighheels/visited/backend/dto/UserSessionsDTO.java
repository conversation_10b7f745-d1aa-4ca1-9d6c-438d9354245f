package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Represents the user sessions for the API.
 */
@Setter
@Getter
public class UserSessionsDTO {

    private UserDTO user;
    private List<Session> sessions;

    public UserSessionsDTO(UserDTO user, List<Session> sessions) {
        this.user = user;
        this.sessions = sessions;
    }

}
