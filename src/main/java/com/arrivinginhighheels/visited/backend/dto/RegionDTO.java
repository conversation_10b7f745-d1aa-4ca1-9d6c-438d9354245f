package com.arrivinginhighheels.visited.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegionDTO {

    private Long id;
    private String name;
    private List<AreaDTO> areas;
    private Map<String, String> translations;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AreaDTO> getAreas() {
        return areas;
    }

    public void setAreas(List<AreaDTO> areas) {
        this.areas = areas;
    }

    public RegionDTO id(final Long id) {
        this.id = id;
        return this;
    }

    public RegionDTO name(final String name) {
        this.name = name;
        return this;
    }

    public RegionDTO areas(final List<AreaDTO> areas) {
        this.areas = areas;
        return this;
    }

    public Map<String, String> getTranslations() {
        return translations;
    }

    public RegionDTO setTranslations(Map<String, String> translations) {
        this.translations = translations;
        return this;
    }

    @Override
    public String toString() {
        return "RegionDTO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", areas=" + areas +
                '}';
    }
}
