package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.features.books.BookDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import java.util.List;
import java.util.Optional;

@JsonInclude(Include.NON_EMPTY)
public record AreaDetailsDto(
    Long id,
    String isoCode,
    String type,
    String name,
    Integer popularity,
    Double size,
    Long population,
    List<String> mustSee,
    String thumbnailUrl,
    String thumbnailBlurHash,
    Optional<BookDto> bookLink
) {}
