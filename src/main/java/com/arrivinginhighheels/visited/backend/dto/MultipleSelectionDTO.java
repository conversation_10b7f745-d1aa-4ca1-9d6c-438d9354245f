package com.arrivinginhighheels.visited.backend.dto;

import java.util.ArrayList;
import java.util.List;

/**
 * DTO for selecting multiple geoareas at once
 */
public class MultipleSelectionDTO {

    private List<SelectionDTO> selectionList = new ArrayList<>();

    public MultipleSelectionDTO() {
    }

    public MultipleSelectionDTO(List<SelectionDTO> selectionList) {
        this.selectionList = selectionList;
    }

    public List<SelectionDTO> getSelectionList() {
        return selectionList;
    }

    public void setSelectionList(List<SelectionDTO> selectionList) {
        this.selectionList = selectionList;
    }
}
