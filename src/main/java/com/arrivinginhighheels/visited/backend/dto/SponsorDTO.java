package com.arrivinginhighheels.visited.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SponsorDTO {
    private String name;
    private String url;
    private String promotionalText;

    public SponsorDTO() { }

    public SponsorDTO(String name, String url, String promotionalText) {
        this.name = name;
        this.url = url;
        this.promotionalText = promotionalText;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPromotionalText() {
        return promotionalText;
    }

    public void setPromotionalText(String promotionalText) {
        this.promotionalText = promotionalText;
    }

    @Override
    public String toString() {
        return "SponsorDTO{" +
                "name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", promotionalText='" + promotionalText + '\'' +
                '}';
    }
}
