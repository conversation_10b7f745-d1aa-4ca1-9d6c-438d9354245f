package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SelectionType;
import lombok.Getter;

/**
 * Represents a selection, but with simpler fields, to be sent by the API
 */
@Getter
public class SelectionDTO {
    private String areaIsoKey;
    private SelectionType selectionType;

    public SelectionDTO() {
    }

    public SelectionDTO(String areaIsoKey, SelectionType selectionType) {
        this.areaIsoKey = areaIsoKey;
        this.selectionType = selectionType;
    }

    public void setAreaIsoKey(String areaIsoKey) {
        this.areaIsoKey = areaIsoKey;
    }

    public void setSelectionType(SelectionType selectionType) {
        this.selectionType = selectionType;
    }

    public SelectionDTO areaIsoKey(final String areaIsoKey) {
        this.areaIsoKey = areaIsoKey;
        return this;
    }

    public SelectionDTO selectionType(final SelectionType selectionType) {
        this.selectionType = selectionType;
        return this;
    }

    @Override
    public String toString() {
        return "SelectionDTO{" +
                "areaIsoKey='" + areaIsoKey + '\'' +
                ", selectionType=" + selectionType +
                '}';
    }
}
