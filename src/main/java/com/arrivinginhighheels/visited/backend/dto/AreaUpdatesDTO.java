package com.arrivinginhighheels.visited.backend.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AreaUpdatesDTO {
    @Setter
    private Date lastModifiedDate;
    private List<AreaDTO> areas;
    private List<RegionDTO> regions;
    private Map<String, String> geometry;
    private Map<String, String> images;

    public AreaUpdatesDTO(Date lastModifiedDate, List<AreaDTO> areas, List<RegionDTO> regions) {
        this.lastModifiedDate = lastModifiedDate;

        if (areas != null && !areas.isEmpty()) {
            this.areas = areas;
        }

        if (regions != null && !regions.isEmpty()) {
            this.regions = regions;
        }
    }

    public void setAreas(List<AreaDTO> areas) {

        if (areas != null && !areas.isEmpty()) {
            this.areas = areas;
        }
    }

    public void setRegions(List<RegionDTO> regions) {
        if (regions != null && !regions.isEmpty()) {
            this.regions = regions;
        }
    }

    public void setGeometry(Map<String, String> geometry) {
        if (geometry != null && !geometry.isEmpty()) {
            this.geometry = geometry;
        }
    }

    public void setImages(Map<String, String> images) {
        if (images != null && !images.isEmpty()) {
            this.images = images;
        }
    }
}
