package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter @Setter @NoArgsConstructor @JsonInclude(JsonInclude.Include.NON_NULL)
public class PrintedOrderDto {
    private Long orderId;
    private String user;
    private MapType type;
    private List<SelectionType> useSelections;
    private Map<String, Object> data;
    private Map<String, Object> disputedTerritories;
    private PaletteDto palette;
    private AddressDto shippingAddress;
    private Long languageId;
}
