package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.SelectionLog;
import com.arrivinginhighheels.visited.backend.model.SelectionType;

import java.time.LocalDateTime;

/**
 * Represents an user selection log for the outside world.
 */
public class SelectionLogDTO {

    private String geoAreaIsoKey;
    private SelectionType type;
    private LocalDateTime timestamp;

    public SelectionLogDTO() {
    }

    public SelectionLogDTO(SelectionLog theLog) {
        this.geoAreaIsoKey = theLog.getGeoArea().getIsoKey();
        this.type = theLog.getType();
        this.timestamp = theLog.getTimestamp();
    }

    public String getGeoAreaIsoKey() {
        return geoAreaIsoKey;
    }

    public void setGeoAreaIsoKey(String geoAreaIsoKey) {
        this.geoAreaIsoKey = geoAreaIsoKey;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(SelectionType type) {
        this.type = type;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
