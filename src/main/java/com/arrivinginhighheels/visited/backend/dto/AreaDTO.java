package com.arrivinginhighheels.visited.backend.dto;

import com.arrivinginhighheels.visited.backend.model.CountryContinent;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.Region;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class AreaDTO {

    private Long id;

    @Deprecated
    private Long continentId;
    private List<Long> continentIds;

    private Long parentId;
    private String iso;
    private String name;
    private String type;
    private String flag;
    private String flagName;
    private List<AreaLabelDTO> labels;
    private Double[] bounds;
    private Double[] viewBounds;
    private ETagsDTO eTags;
    private Boolean sovereign;
    private Long sovereignParentId;
    private Map<String, String> translations;

    public AreaDTO id(final Long id) {
        this.id = id;
        return this;
    }

    public AreaDTO continentIds(GeoArea area) {
        final var regions = area.getContinents();
        if (regions == null || regions.isEmpty()) {
            this.continentIds = null;
            return this;
        }

        continentIds = regions.stream()
                .map(CountryContinent::getContinent)
                .map(Region::getId)
                .collect(toList());
        return this;
    }

    @Deprecated
    public AreaDTO continentId(GeoArea area) {
        final var region = area.getContinents();
        if (region == null || region.isEmpty()) {
            this.continentId = null;
            return this;
        }

        this.continentId = region.stream().findFirst().orElseThrow().getContinent().getId();
        return this;
    }

    public AreaDTO parentId(GeoArea area) {
        final GeoArea parent = area.getParent();
        if (parent == null) {
            this.parentId = null;
        } else {
            this.parentId = parent.getId();
        }

        return this;
    }

    public AreaDTO iso(final String iso) {
        this.iso = iso;
        return this;
    }

    public AreaDTO name(final String name) {
        this.name = name;
        return this;
    }

    public AreaDTO type(final String type) {
        this.type = type;
        return this;
    }

    public AreaDTO flag(final String flag) {
        this.flag = flag;
        return this;
    }

    public AreaDTO labels(final List<AreaLabelDTO> labels) {
        this.labels = labels;
        return this;
    }

    public AreaDTO bounds(final Double[] bounds) {
        this.bounds = bounds;
        return this;
    }

    public AreaDTO viewBounds(final Double[] viewBounds) {
        this.viewBounds = viewBounds;
        return this;
    }

    public AreaDTO eTags(final ETagsDTO eTags) {
        this.eTags = eTags;
        return this;
    }

    public AreaDTO setSovereign(Boolean sovereign) {
        this.sovereign = sovereign;
        return this;
    }

    public AreaDTO setSovereignParentId(Long sovereignParentId) {
        this.sovereignParentId = sovereignParentId;
        return this;
    }

    public AreaDTO flagFileName(String filename) {
        this.flagName = filename;
        return this;
    }

    public AreaDTO translations(Map<String, String> translations) {
        this.translations = translations;
        return this;
    }

    @Override
    public String toString() {
        return "AreaDTO{" +
                "id=" + id +
                ", continentId=" + continentId +
                ", parentId=" + parentId +
                ", iso='" + iso + '\'' +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", flag='" + flag + '\'' +
                ", flagName='" + flagName + '\'' +
                ", labels=" + labels +
                ", bounds=" + Arrays.toString(bounds) +
                ", eTags=" + eTags +
                ", sovereign=" + sovereign +
                ", sovereignParentId=" + sovereignParentId +
                '}';
    }
}
