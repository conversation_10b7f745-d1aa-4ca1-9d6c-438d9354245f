package com.arrivinginhighheels.visited.backend.dto;

import java.util.Arrays;

public class AreaLabelDTO {

    private Long id;
    private Integer resolution;
    private Double[] coordinate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getResolution() {
        return resolution;
    }

    public void setResolution(Integer resolution) {
        this.resolution = resolution;
    }

    public Double[] getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(Double[] coordinate) {
        this.coordinate = coordinate;
    }

    public AreaLabelDTO id(final Long id) {
        this.id = id;
        return this;
    }

    public AreaLabelDTO resolution(final Integer resolution) {
        this.resolution = resolution;
        return this;
    }

    public AreaLabelDTO coordinate(final Double[] coordinate) {
        this.coordinate = coordinate;
        return this;
    }

    @Override
    public String toString() {
        return "AreaLabelDTO{" +
                "id='" + id + '\'' +
                ", resolution='" + resolution + '\'' +
                ", coordinate=" + Arrays.toString(coordinate) +
                '}';
    }
}
