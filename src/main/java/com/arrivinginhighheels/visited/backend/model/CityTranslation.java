package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;

@Setter
@Getter
@Entity
@Table(name = "city_translations", uniqueConstraints = {
        @UniqueConstraint(name = "AK_CityTranslations", columnNames = { "city_id", "supported_language_id" })
})
public class CityTranslation {
    @Id
    @SequenceGenerator(name = "city_translations_id_seq", sequenceName = "city_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "city_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private City city;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    private String name;

    public CityTranslation() {
    }

    @Override
    public String toString() {
        return "CityTranslation{" +
                "id=" + id +
                ", city=" + city +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
