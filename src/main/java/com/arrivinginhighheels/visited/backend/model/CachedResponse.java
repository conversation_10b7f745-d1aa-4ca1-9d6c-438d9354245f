package com.arrivinginhighheels.visited.backend.model;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import org.springframework.context.annotation.Lazy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity
@Table(name = "cached_response")
public class CachedResponse {

    @Id
    @SequenceGenerator(name = "cached_responses_id_seq", sequenceName = "cached_responses_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cached_responses_id_seq")
    private Long id;

    @Column(name = "path", nullable = false)
    private String path;

    @Lazy
    @Column(name = "json", nullable = false)
    private byte[] json;

    @Column(name = "last_modification_time", nullable = false)
    private String lastModificationTime;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    public CachedResponse() {
    }

    public CachedResponse(String path, String json, String lastModificationTime, SupportedLanguage supportedLanguage) {
        this.path = path;
        this.json = json.getBytes(StandardCharsets.UTF_8);
        this.lastModificationTime = lastModificationTime;
        this.supportedLanguage = supportedLanguage;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getJson() {
        return new String(json, StandardCharsets.UTF_8);
    }

    public void setJson(String json) {
        this.json = json.getBytes(StandardCharsets.UTF_8);
    }

    public String getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(String lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "CachedResponse{" +
                "id=" + id +
                ", path='" + path + '\'' +
                ", json=" + Arrays.toString(json) +
                ", lastModificationTime=" + lastModificationTime +
                ", supportedLanguage=" + supportedLanguage +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof CachedResponse response))
            return false;

        if (id != null ? !id.equals(response.id) : response.id != null)
            return false;
        if (path != null ? !path.equals(response.path) : response.path != null)
            return false;
        if (!Arrays.equals(json, response.json))
            return false;
        if (lastModificationTime != null ? !lastModificationTime.equals(response.lastModificationTime)
                : response.lastModificationTime != null)
            return false;
        return supportedLanguage != null ? supportedLanguage.equals(response.supportedLanguage)
                : response.supportedLanguage == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (path != null ? path.hashCode() : 0);
        result = 31 * result + Arrays.hashCode(json);
        result = 31 * result + (lastModificationTime != null ? lastModificationTime.hashCode() : 0);
        result = 31 * result + (supportedLanguage != null ? supportedLanguage.hashCode() : 0);
        return result;
    }
}
