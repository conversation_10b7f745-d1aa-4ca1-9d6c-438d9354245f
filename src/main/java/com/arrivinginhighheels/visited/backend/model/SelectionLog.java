package com.arrivinginhighheels.visited.backend.model;

import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Represents a user selection logged by the API
 */
@Entity
@Table(name = "selection_logs")
public class SelectionLog {

    @Id
    @SequenceGenerator(name = "selection_logs_id_seq", sequenceName = "selection_logs_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "selection_logs_id_seq", strategy = GenerationType.SEQUENCE)
    private Long id;

    // @NotNull
    // @ManyToOne(optional = false)
    // private User user;

    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @NotNull
    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private SelectionType type;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    @Column(nullable = false, length = 10)
    private String date;

    public SelectionLog() {
    }

    public SelectionLog(Long userId, GeoArea geoArea, SelectionType type, LocalDateTime timestamp) {
        this.userId = userId;
        this.geoArea = geoArea;
        this.type = type;
        this.timestamp = timestamp;
    }

    public SelectionLog(Selection theSelection) {
        this.userId = theSelection.getUser().getId();
        this.geoArea = theSelection.getGeoArea();
        this.type = theSelection.getType();
        this.setTimestamp(theSelection.getTimestamp());
    }

    public SelectionLog(Selection theSelection, SelectionType selectionType, LocalDateTime timestamp) {
        this.userId = theSelection.getUser().getId();
        this.geoArea = theSelection.getGeoArea();

        this.setTimestamp(timestamp);
        this.type = selectionType;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        if (timestamp != null) {
            this.date = timestamp.format(DateTimeFormatter.ISO_DATE);
        } else {
            this.date = null;
        }
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    // public User getUser() {
    // return user;
    // }
    //
    // public void setUser(User user) {
    // this.user = user;
    // }

    @NotNull
    public Long getUserId() {
        return userId;
    }

    public void setUserId(@NotNull Long userId) {
        this.userId = userId;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(SelectionType type) {
        this.type = type;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Override
    public boolean equals(Object o) {

        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        SelectionLog that = (SelectionLog) o;

        if (id != null ? !id.equals(that.id) : that.id != null)
            return false;
        if (userId != null ? !userId.equals(that.userId) : that.userId != null)
            return false;
        if (geoArea != null ? !geoArea.equals(that.geoArea) : that.geoArea != null)
            return false;
        if (type != that.type)
            return false;
        return timestamp != null ? timestamp.equals(that.timestamp) : that.timestamp == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (geoArea != null ? geoArea.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "SelectionLog{" +
                "id=" + id +
                ", userId=" + userId +
                ", geoArea=" + geoArea +
                ", type=" + type +
                ", timestamp=" + timestamp +
                '}';
    }
}
