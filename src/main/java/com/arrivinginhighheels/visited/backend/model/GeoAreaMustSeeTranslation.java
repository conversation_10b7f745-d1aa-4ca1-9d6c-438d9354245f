package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;

@Entity
@Table(name = "geoarea_must_see_translations", uniqueConstraints = {
        @UniqueConstraint(name = "AK_GeoAreaMustSeeTranslations", columnNames = { "geo_area_must_see_id",
                "supported_language_id" })
})
public class GeoAreaMustSeeTranslation {

    @Id
    @SequenceGenerator(name = "geoarea_must_see_translations_id_seq", sequenceName = "geoarea_must_see_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_must_see_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private GeoAreaMustSee geoAreaMustSee;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoAreaMustSee getGeoAreaMustSee() {
        return geoAreaMustSee;
    }

    public void setGeoAreaMustSee(GeoAreaMustSee geoAreaMustSee) {
        this.geoAreaMustSee = geoAreaMustSee;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "GeoAreaTranslation{" +
                "id=" + id +
                ", geoAreaMustSee=" + geoAreaMustSee +
                ", supportedLanguage=" + supportedLanguage +
                ", description='" + description + '\'' +
                '}';
    }
}
