package com.arrivinginhighheels.visited.backend.model;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "experiences")
public class Experience {
    @Id
    @SequenceGenerator(name = "experiences_id_seq", sequenceName = "experiences_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "experiences_id_seq")
    private Long id;

    @NotNull
    @Column(length = 250, nullable = false)
    private String name;

    @Column(length = 40, nullable = false)
    private String file;

    @Column(name = "icon_url", length = 250)
    private String iconUrl;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public Experience() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (file != null ? file.hashCode() : 0);
        result = 31 * result + (iconUrl != null ? iconUrl.hashCode() : 0);
        result = 31 * result + (lastModificationTime != null ? lastModificationTime.hashCode() : 0);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        Experience that = (Experience) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(file, that.file)
                && Objects.equals(iconUrl, that.iconUrl)
                && Objects.equals(lastModificationTime, that.lastModificationTime);
    }

    @Override
    public String toString() {
        return "Experience{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", file='" + file + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
