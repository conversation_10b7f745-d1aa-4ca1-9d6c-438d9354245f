package com.arrivinginhighheels.visited.backend.model;

import com.arrivinginhighheels.visited.backend.dto.Currency;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "geoarea_currencies")
public class GeoAreaCurrency {

    @Id
    @Column(name = "geo_area_id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "geo_area_id", updatable = false, insertable = false)
    private GeoArea geoArea;

    @Enumerated(EnumType.STRING)
    private Currency currency;
}
