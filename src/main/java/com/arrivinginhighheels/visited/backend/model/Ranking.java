package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;

@Entity
public class Ranking {

    @Id
    private Long position;

    @Column(name = "num_areas_visited", nullable = false, unique = true)
    private Integer numAreasVisited;

    @Column(name = "num_users", nullable = false)
    private Integer numUsers;

    public Ranking() {
    }

    public Long getPosition() {
        return position;
    }

    public void setPosition(Long position) {
        this.position = position;
    }

    public Integer getNumAreasVisited() {
        return numAreasVisited;
    }

    public void setNumAreasVisited(Integer numAreasVisited) {
        this.numAreasVisited = numAreasVisited;
    }

    public Integer getNumUsers() {
        return numUsers;
    }

    public void setNumUsers(Integer numUsers) {
        this.numUsers = numUsers;
    }

    @Override
    public String toString() {
        return "Ranking{" +
                "position=" + position +
                ", numAreasVisited=" + numAreasVisited +
                ", numUsers=" + numUsers +
                '}';
    }
}
