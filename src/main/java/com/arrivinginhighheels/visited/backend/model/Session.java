package com.arrivinginhighheels.visited.backend.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Represents a user session, logged to the DB
 */
@Entity
@Table(name = "sessions")
public class Session {

    @Id
    @SequenceGenerator(name = "sessions_id_seq", sequenceName = "sessions_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sessions_id_seq")
    private Long id;

    @JsonIgnore
    @NotNull
    @ManyToOne(optional = false)
    private User user;

    @NotNull
    @Column(length = 550, nullable = false)
    private String token;

    @NotNull
    @Embedded
    private Platform platform;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime startDateTime;

    @Column(nullable = false, length = 10)
    private String date;

    public Session() {
    }

    public Session(User user, String token, Platform platform, LocalDateTime startDateTime) {
        this.user = user;
        this.token = token;
        this.platform = platform;
        this.setStartDateTime(startDateTime);
    }

    public void setStartDateTime(LocalDateTime startDateTime) {
        this.startDateTime = startDateTime;
        if (startDateTime != null) {
            this.date = startDateTime.format(DateTimeFormatter.ISO_DATE);
        } else {
            this.date = null;
        }
    }

    public LocalDateTime getStartDateTime() {
        return startDateTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Platform getPlatform() {
        return platform;
    }

    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        Session session = (Session) o;

        if (id != null ? !id.equals(session.id) : session.id != null)
            return false;
        if (user != null ? !user.equals(session.user) : session.user != null)
            return false;
        if (token != null ? !token.equals(session.token) : session.token != null)
            return false;
        if (platform != null ? !platform.equals(session.platform) : session.platform != null)
            return false;
        return startDateTime != null ? startDateTime.equals(session.startDateTime) : session.startDateTime == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (user != null ? user.hashCode() : 0);
        result = 31 * result + (token != null ? token.hashCode() : 0);
        result = 31 * result + (platform != null ? platform.hashCode() : 0);
        result = 31 * result + (startDateTime != null ? startDateTime.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Session{" +
                "id=" + id +
                ", user=" + user +
                ", token='" + token + '\'' +
                ", platform=" + platform +
                ", startDateTime=" + startDateTime +
                '}';
    }
}
