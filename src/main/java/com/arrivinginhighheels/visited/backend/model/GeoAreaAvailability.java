package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity
@Table(name = "geoarea_availability")
public class GeoAreaAvailability {

    @Id
    @SequenceGenerator(name = "geoarea_availability_id_seq", sequenceName = "geoarea_availability_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_availability_id_seq")
    private Long id;

    @OneToOne(optional = false)
    private GeoArea geoArea;

    @Column(length = 15, nullable = true, name = "min_ios_version")
    private String minIosVersion;

    @Column(length = 15, nullable = true, name = "min_android_version")
    private String minAndroidVersion;

    @Column(name = "requires_purchase", nullable = false)
    private boolean requiresPurchase;

    public GeoAreaAvailability() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public String getMinIosVersion() {
        return minIosVersion;
    }

    public void setMinIosVersion(String minIosVersion) {
        this.minIosVersion = minIosVersion;
    }

    public String getMinAndroidVersion() {
        return minAndroidVersion;
    }

    public void setMinAndroidVersion(String minAndroidVersion) {
        this.minAndroidVersion = minAndroidVersion;
    }

    public boolean isRequiresPurchase() {
        return requiresPurchase;
    }

    public void setRequiresPurchase(boolean requiresPurchase) {
        this.requiresPurchase = requiresPurchase;
    }

    @Override
    public String toString() {
        return "GeoAreaAvailability{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", minIosVersion='" + minIosVersion + '\'' +
                ", minAndroidVersion='" + minAndroidVersion + '\'' +
                ", requiresPurchase=" + requiresPurchase +
                '}';
    }
}
