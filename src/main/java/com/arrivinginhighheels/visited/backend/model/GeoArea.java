package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Represents any geographical area in the world map that has an iso key.
 */
@Entity
@Getter
@Setter
@Table(name = "geoareas")
@AllArgsConstructor
@NoArgsConstructor
public class GeoArea {

    @Id
    @SequenceGenerator(name = "geoareas_id_seq", sequenceName = "geoareas_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoareas_id_seq")
    private Long id;

    @OneToMany(mappedBy = "country", cascade = CascadeType.ALL)
    @OrderBy("primary DESC")
    private List<CountryContinent> continents;

    @NotNull
    @Column(name = "iso_key", length = 15, nullable = false, unique = true)
    private String isoKey;

    @NotNull
    @Column(length = 250, nullable = false)
    private String name;

    @Column(name = "flag_url", length = 250)
    private String flagUrl;

    @Column(name = "flag_file_name", length = 24)
    private String flagFileName;

    @Column(name = "geometry_etag", length = 40)
    private String geometryEtag;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "geo_area_type", length = 25, nullable = false)
    private GeoAreaType type;

    @ManyToOne(fetch = FetchType.EAGER)
    private GeoArea parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    private List<GeoArea> subdivisions;

    @OneToMany(mappedBy = "geoArea", cascade = CascadeType.ALL)
    private List<GeoAreaLabel> labels;

    @Column(name = "bounding_min_long", precision = 19, scale = 10)
    private BigDecimal boundingMinLong;

    @Column(name = "bounding_max_long", precision = 19, scale = 10)
    private BigDecimal boundingMaxLong;

    @Column(name = "bounding_min_lat", precision = 19, scale = 10)
    private BigDecimal boundingMinLat;

    @Column(name = "bounding_max_lat", precision = 19, scale = 10)
    private BigDecimal boundingMaxLat;

    @Column(name = "view_bounds_north_lat", precision = 19, scale = 10)
    private BigDecimal viewBoundsNorthLat;

    @Column(name = "view_bounds_west_long", precision = 19, scale = 10)
    private BigDecimal viewBoundsWestLong;

    @Column(name = "view_bounds_south_lat", precision = 19, scale = 10)
    private BigDecimal viewBoundsSouthLat;

    @Column(name = "view_bounds_east_long", precision = 19, scale = 10)
    private BigDecimal viewBoundsEastLong;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    @NotNull
    @ColumnDefault("false")
    @Column(name = "is_sovereign")
    private boolean isSovereign;

    @Column(name = "sovereign_parent_id")
    private Long sovereignParentId;

    public Double[] boundsAsDoubleArray() {
        if (boundingMaxLat != null
                && boundingMaxLong != null
                && boundingMinLat != null
                && boundingMinLong != null) {
            return new Double[] {
                    boundingMinLong.doubleValue(),
                    boundingMinLat.doubleValue(),
                    boundingMaxLong.doubleValue(),
                    boundingMaxLat.doubleValue()
            };
        }
        return new Double[] {};
    }

    public Double[] viewBoundsAsDoubleArray() {
        if (viewBoundsNorthLat != null
                && viewBoundsWestLong != null
                && viewBoundsSouthLat != null
                && viewBoundsEastLong != null) {
            return new Double[] {
                    viewBoundsWestLong.doubleValue(),
                    viewBoundsNorthLat.doubleValue(),
                    viewBoundsEastLong.doubleValue(),
                    viewBoundsSouthLat.doubleValue()
            };
        }
        return null;
    }

    public boolean isTopLevelArea() {
        return parent == null;
    }

    public GeoArea getTopLevelParent() {
        GeoArea parent = getParent();
        if (parent == null) {
            return this;
        }

        return parent.getTopLevelParent();
    }

    @Override
    public final boolean equals(Object o) {
        if (!(o instanceof GeoArea geoArea)) return false;

        return isSovereign() == geoArea.isSovereign() && Objects.equals(getId(), geoArea.getId()) && Objects.equals(getContinents(), geoArea.getContinents()) && Objects.equals(getIsoKey(), geoArea.getIsoKey()) && Objects.equals(getName(), geoArea.getName()) && Objects.equals(getFlagUrl(), geoArea.getFlagUrl()) && Objects.equals(getFlagFileName(), geoArea.getFlagFileName()) && Objects.equals(getGeometryEtag(), geoArea.getGeometryEtag()) && getType() == geoArea.getType() && Objects.equals(getParent(), geoArea.getParent()) && Objects.equals(getSubdivisions(), geoArea.getSubdivisions()) && Objects.equals(getLabels(), geoArea.getLabels()) && Objects.equals(getBoundingMinLong(), geoArea.getBoundingMinLong()) && Objects.equals(getBoundingMaxLong(), geoArea.getBoundingMaxLong()) && Objects.equals(getBoundingMinLat(), geoArea.getBoundingMinLat()) && Objects.equals(getBoundingMaxLat(), geoArea.getBoundingMaxLat()) && Objects.equals(getViewBoundsNorthLat(), geoArea.getViewBoundsNorthLat()) && Objects.equals(getViewBoundsWestLong(), geoArea.getViewBoundsWestLong()) && Objects.equals(getViewBoundsSouthLat(), geoArea.getViewBoundsSouthLat()) && Objects.equals(getViewBoundsEastLong(), geoArea.getViewBoundsEastLong()) && Objects.equals(getLastModificationTime(), geoArea.getLastModificationTime()) && Objects.equals(getSovereignParentId(), geoArea.getSovereignParentId());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getId());
        result = 31 * result + Objects.hashCode(getContinents());
        result = 31 * result + Objects.hashCode(getIsoKey());
        result = 31 * result + Objects.hashCode(getName());
        result = 31 * result + Objects.hashCode(getFlagUrl());
        result = 31 * result + Objects.hashCode(getFlagFileName());
        result = 31 * result + Objects.hashCode(getGeometryEtag());
        result = 31 * result + Objects.hashCode(getType());
        result = 31 * result + Objects.hashCode(getParent());
        result = 31 * result + Objects.hashCode(getSubdivisions());
        result = 31 * result + Objects.hashCode(getLabels());
        result = 31 * result + Objects.hashCode(getBoundingMinLong());
        result = 31 * result + Objects.hashCode(getBoundingMaxLong());
        result = 31 * result + Objects.hashCode(getBoundingMinLat());
        result = 31 * result + Objects.hashCode(getBoundingMaxLat());
        result = 31 * result + Objects.hashCode(getViewBoundsNorthLat());
        result = 31 * result + Objects.hashCode(getViewBoundsWestLong());
        result = 31 * result + Objects.hashCode(getViewBoundsSouthLat());
        result = 31 * result + Objects.hashCode(getViewBoundsEastLong());
        result = 31 * result + Objects.hashCode(getLastModificationTime());
        result = 31 * result + Boolean.hashCode(isSovereign());
        result = 31 * result + Objects.hashCode(getSovereignParentId());
        return result;
    }

    @Override
    public String toString() {
        return "GeoArea{" +
                "id=" + id +
                ", continents=" + continents +
                ", isoKey='" + isoKey + '\'' +
                ", name='" + name + '\'' +
                ", flagUrl='" + flagUrl + '\'' +
                ", flagFileName='" + flagFileName + '\'' +
                ", geometryEtag='" + geometryEtag + '\'' +
                ", type=" + type +
                ", parent=" + parent +
                ", subdivisions=" + subdivisions +
                ", labels=" + labels +
                ", boundingMinLong=" + boundingMinLong +
                ", boundingMaxLong=" + boundingMaxLong +
                ", boundingMinLat=" + boundingMinLat +
                ", boundingMaxLat=" + boundingMaxLat +
                ", viewBoundsNorthLat=" + viewBoundsNorthLat +
                ", viewBoundsWestLong=" + viewBoundsWestLong +
                ", viewBoundsSouthLat=" + viewBoundsSouthLat +
                ", viewBoundsEastLong=" + viewBoundsEastLong +
                ", lastModificationTime=" + lastModificationTime +
                ", isSovereign=" + isSovereign +
                ", sovereignParentId=" + sovereignParentId +
                '}';
    }
}
