package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "supported_languages")
public class SupportedLanguage {
    public static final String ENGLISH_CODE = "en";

    @Id
    @Column(columnDefinition = "BIGINT")
    private Long id;

    @Column(name = "code", nullable = false, unique = true, length = 10, columnDefinition = "VARCHAR(10)")
    private String code;

    @Column(name = "name", nullable = false, length = 150, columnDefinition = "VARCHAR(150)")
    private String name;

    public SupportedLanguage() {
    }

    @Override
    public String toString() {
        return "SupportedLanguage{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                '}';
    }


    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SupportedLanguage that)) return false;

        return getId().equals(that.getId()) && getCode().equals(that.getCode()) && getName().equals(that.getName());
    }

    @Override
    public int hashCode() {
        int result = getId().hashCode();
        result = 31 * result + getCode().hashCode();
        result = 31 * result + getName().hashCode();
        return result;
    }

    public boolean isEnglish() {
        return code.equals(ENGLISH_CODE);
    }
}
