package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;

import jakarta.persistence.*;

@Getter
@Entity
@Table(name = "experience_book_links")
public class ExperienceBookLink {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private BookLink book;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "experience_id")
    private Experience experience;

    @Override
    public String toString() {
        return "ExperienceBookLink{" +
                "id=" + id +
                ", book=" + book +
                ", experience=" + experience +
                '}';
    }
}
