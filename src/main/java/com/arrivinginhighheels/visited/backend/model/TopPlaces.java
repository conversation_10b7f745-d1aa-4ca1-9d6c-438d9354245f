package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity
@Table(name = "top_places")
public class TopPlaces {

    @Id
    @SequenceGenerator(name = "top_places_id_seq", sequenceName = "top_places_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "top_places_id_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "lived_geo_area_id")
    private GeoArea livedGeoArea;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "been_geo_area_id")
    private GeoArea beenGeoArea;

    @Column(name = "number_of_users_been")
    private Integer numberOfUsersBeen;

    public TopPlaces() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getLivedGeoArea() {
        return livedGeoArea;
    }

    public void setLivedGeoArea(GeoArea livedGeoArea) {
        this.livedGeoArea = livedGeoArea;
    }

    public GeoArea getBeenGeoArea() {
        return beenGeoArea;
    }

    public void setBeenGeoArea(GeoArea beenGeoArea) {
        this.beenGeoArea = beenGeoArea;
    }

    public Integer getNumberOfUsersBeen() {
        return numberOfUsersBeen;
    }

    public void setNumberOfUsersBeen(Integer numberOfUsersBeen) {
        this.numberOfUsersBeen = numberOfUsersBeen;
    }

    @Override
    public String toString() {
        return "TopPlaces{" +
                "id=" + id +
                ", livedGeoArea=" + livedGeoArea +
                ", beenGeoArea=" + beenGeoArea +
                ", numberOfUsersBeen=" + numberOfUsersBeen +
                '}';
    }
}
