package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;

@Entity
@Table(name = "inspiration_translations")
public class InspirationTranslation {
    @Id
    @SequenceGenerator(name = "inspiration_translations_id_seq", sequenceName = "inspiration_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "inspiration_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private Inspiration inspiration;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Inspiration getInspiration() {
        return inspiration;
    }

    public void setInspiration(Inspiration inspiration) {
        this.inspiration = inspiration;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "InspirationTranslation{" +
                "id=" + id +
                ", inspiration=" + inspiration +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
