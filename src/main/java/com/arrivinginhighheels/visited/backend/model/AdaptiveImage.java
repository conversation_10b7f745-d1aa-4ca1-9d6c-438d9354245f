package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@Setter
@Getter
@Entity
@Table(name = "adaptive_images")
public class AdaptiveImage {
    @Id
    @SequenceGenerator(name="adaptive_image_id_seq", sequenceName="adaptive_image_id_seq", allocationSize=1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="adaptive_image_id_seq")
    private Long id;

    @NotNull
    @Column(name="one_x_url", length = 150, nullable = false)
    private String oneUrl;

    @NotNull
    @Column(name="one_point_five_x_url", length = 150, nullable = false)
    private String onePointFiveUrl;

    @NotNull
    @Column(name="two_x_url", length = 150, nullable = false)
    private String twoUrl;

    @NotNull
    @Column(name="three_x_url", length = 150, nullable = false)
    private String threeUrl;

    @NotNull
    @Column(name="four_x_url", length = 150, nullable = false)
    private String fourUrl;

    @NotNull
    @Column(name="blur_hash", length = 30, nullable = false)
    private String blurHash;

    public AdaptiveImage() {
    }

    @Override
    public String toString() {
        return "AdaptiveImage{" +
                "id=" + id +
                ", oneUrl='" + oneUrl + '\'' +
                ", onePointFiveUrl='" + onePointFiveUrl + '\'' +
                ", twoUrl='" + twoUrl + '\'' +
                ", threeUrl='" + threeUrl + '\'' +
                ", fourUrl='" + fourUrl + '\'' +
                '}';
    }
}
