package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "country_continent")
public class CountryContinent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "country_id")
    private GeoArea country;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "continent_id")
    private Region continent;

    private boolean primary;

    @Override
    public String toString() {
        return "CountryContinent{" +
                "id=" + id +
                ", country=" + country +
                ", continent=" + continent +
                ", primary=" + primary +
                '}';
    }

    @Override
    public final boolean equals(Object o) {
        if (!(o instanceof CountryContinent that)) return false;

        return isPrimary() == that.isPrimary() && Objects.equals(getId(), that.getId()) && Objects.equals(getCountry(), that.getCountry()) && Objects.equals(getContinent(), that.getContinent());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getId());
        result = 31 * result + Objects.hashCode(getCountry());
        result = 31 * result + Objects.hashCode(getContinent());
        result = 31 * result + Boolean.hashCode(isPrimary());
        return result;
    }
}
