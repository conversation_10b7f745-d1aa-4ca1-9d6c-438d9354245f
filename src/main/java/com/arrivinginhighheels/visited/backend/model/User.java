package com.arrivinginhighheels.visited.backend.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.arrivinginhighheels.visited.backend.config.Constants.NativeQueries.USER_GET_RANK_SQL;
import static com.arrivinginhighheels.visited.backend.config.Constants.NativeQueries.USER_GET_TOTAL_AREAS_VISITED_SQL;

/**
 * Represents a user for the system.
 */
@Setter
@Getter
@Entity
@Table(name="users")
@NamedNativeQueries({
        @NamedNativeQuery(name = "User.getRankForTheUser", query = USER_GET_RANK_SQL),
        @NamedNativeQuery(name = "User.getTotalNumberOfAreasVisitedForTheUser", query = USER_GET_TOTAL_AREAS_VISITED_SQL)
})
public class User implements UserDetails, Serializable {

    @Id
    @SequenceGenerator(name="users_id_seq", sequenceName="users_id_seq", allocationSize=1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="users_id_seq")
    private Long id;

    @NotNull
    @Size(min = 4, max = 500)
    @Column(length = 500, unique = true, nullable = false)
    private String username;

    @Column(length = 100, nullable = false)
    @NotNull
    @Size(min = 4, max = 100)
    private String password;

    @NotNull
    @Column
    private Boolean enabled;

    @Column(name="num_areas_visited", nullable = true)
    private Integer numberOfAreasVisited;

    @Column(name="ranking", nullable = true)
    private Long ranking;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    @NotNull
    private Date lastPasswordResetDate;

    @Column(name="unsubscribed")
    private Boolean unsubscribed = false;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "users_authorities",
            joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "authority_id", referencedColumnName = "id")})
    private List<Authority> userAuthorities;

    @Getter
    @JsonIgnore
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<Session> sessions;

    @Getter
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "creation_date")
    private Date creationDate;

    @Override
    @Transient
    @JsonIgnore
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    @Transient
    @JsonIgnore
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    @Transient
    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    @Transient
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return userAuthorities.stream()
                              .map(userAuthority -> new SimpleGrantedAuthority(userAuthority.getName().name()))
                              .collect(Collectors.toList());
    }

    @Transient
    @JsonIgnore
    public boolean isAdmin() {
        return userAuthorities.stream()
                              .anyMatch(auth -> auth.getName().equals(AuthorityName.ROLE_ADMIN));
    }

    @JsonIgnore
    public String getPassword() {
        return password;
    }

    @JsonIgnore
    public Date getLastPasswordResetDate() {
        return lastPasswordResetDate;
    }

    public User() {
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", enabled=" + enabled +
                ", numberOfAreasVisited=" + Optional.of(numberOfAreasVisited).orElse(0) +
                ", ranking=" + Optional.of(ranking).orElse(0L) +
                ", lastPasswordResetDate=" + lastPasswordResetDate +
                ", creationDate=" + creationDate +
                ", userAuthorities=" + userAuthorities +
                '}';
    }
}
