package com.arrivinginhighheels.visited.backend.model;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "inspirations")
public class Inspiration {

    @Id
    @SequenceGenerator(name = "inspirations_id_seq", sequenceName = "inspirations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "inspirations_id_seq")
    private Long id;

    @NotNull
    @Column(length = 100, nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "geo_area_id", updatable = false, insertable = false)
    private GeoArea geoArea;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "adaptive_image_id", updatable = false, insertable = false)
    private AdaptiveImage image;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public Inspiration() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public AdaptiveImage getImage() {
        return image;
    }

    public void setImage(AdaptiveImage image) {
        this.image = image;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        Inspiration that = (Inspiration) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(geoArea, that.geoArea)
                && Objects.equals(image, that.image) && Objects.equals(lastModificationTime, that.lastModificationTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, geoArea, image, lastModificationTime);
    }

    @Override
    public String toString() {
        return "Inspiration{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", geoArea=" + geoArea +
                ", image=" + image +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
