package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;

import jakarta.persistence.*;

@Getter
@Entity
@Table(name = "area_book_links")
public class AreaBookLink {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private BookLink book;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "area_id")
    private GeoArea area;

    @Override
    public String toString() {
        return "AreaBookLink{" +
                "id=" + id +
                ", book=" + book +
                ", area=" + area +
                '}';
    }
}
