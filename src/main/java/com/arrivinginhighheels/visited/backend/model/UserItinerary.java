package com.arrivinginhighheels.visited.backend.model;

import com.vladmihalcea.hibernate.type.array.StringArrayType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "user_itinerary")
@AllArgsConstructor
@NoArgsConstructor
public class UserItinerary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn()
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "geo_area_id")
    private GeoArea geoArea;

    @Column(name = "start_date")
    @Temporal(TemporalType.DATE)
    private Date startDate;

    @Column(name = "end_date")
    @Temporal(TemporalType.DATE)
    private Date endDate;

    @Type(StringArrayType.class)
    @Column(name = "hotels")
    private String[] hotels;

    @Type(StringArrayType.class)
    @Column(name = "notes")
    private String[] notes;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    @Override
    public String toString() {
        return "UserItinerary{" +
                "id=" + id +
                ", user=" + user +
                ", geoArea=" + geoArea +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", hotels=" + hotels +
                ", notes=" + notes +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof UserItinerary that))
            return false;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getUser(), that.getUser())
                && Objects.equals(getGeoArea(), that.getGeoArea())
                && Objects.equals(getStartDate(), that.getStartDate())
                && Objects.equals(getEndDate(), that.getEndDate()) && Objects.equals(getHotels(), that.getHotels())
                && Objects.equals(getNotes(), that.getNotes())
                && Objects.equals(getLastModificationTime(), that.getLastModificationTime());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getUser(), getGeoArea(), getStartDate(), getEndDate(), getHotels(), getNotes(),
                getLastModificationTime());
    }
}
