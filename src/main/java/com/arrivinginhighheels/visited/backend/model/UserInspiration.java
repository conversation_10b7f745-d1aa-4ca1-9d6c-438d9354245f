package com.arrivinginhighheels.visited.backend.model;

import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationSelectionType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

@Entity
@Table(name = "user_inspirations")
public class UserInspiration {

    @Id
    @SequenceGenerator(name = "user_inspirations_id_seq", sequenceName = "user_inspirations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_inspirations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(unique = true)
    private User user;

    @ManyToOne(optional = false)
    private Inspiration inspiration;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private InspirationSelectionType type;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public UserInspiration() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Inspiration getInspiration() {
        return inspiration;
    }

    public void setInspiration(Inspiration inspiration) {
        this.inspiration = inspiration;
    }

    public InspirationSelectionType getType() {
        return type;
    }

    public void setType(InspirationSelectionType type) {
        this.type = type;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "UserInspiration{" +
                "id=" + id +
                ", user=" + user +
                ", inspiration=" + inspiration +
                ", type=" + type +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
