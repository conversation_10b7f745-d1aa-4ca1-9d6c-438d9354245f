package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

@Setter
@Getter
@Entity
@Table(name = "user_been_counters")
public class UserBeenCount {

    @Id
    @SequenceGenerator(name = "user_been_counters_id_seq", sequenceName = "user_been_counters_id_seq", allocationSize = 1)
    @Column(nullable = false)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_been_counters_id_seq")
    private Long id;

    @NotNull
    @ManyToOne(optional = false)
    private User user;

    @NotNull
    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @Column(name = "count")
    private int count;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    public UserBeenCount() {
    }

    public UserBeenCount(Long id, User user, GeoArea geoArea, int count, LocalDateTime timestamp) {
        this.id = id;
        this.user = user;
        this.geoArea = geoArea;
        this.count = count;
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        UserBeenCount that = (UserBeenCount) o;
        return count == that.count && Objects.equals(id, that.id) && Objects.equals(user, that.user)
                && Objects.equals(geoArea, that.geoArea) && Objects.equals(timestamp, that.timestamp);
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (user != null ? user.hashCode() : 0);
        result = 31 * result + (geoArea != null ? geoArea.hashCode() : 0);
        result = 31 * result + count;
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }
}
