package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;

@Entity
@Table(name = "experience_translations", uniqueConstraints = {
        @UniqueConstraint(name = "AK_ExperienceTranslations", columnNames = { "experience_id",
                "supported_language_id" })
})
public class ExperienceTranslation {

    @Id
    @SequenceGenerator(name = "experience_translations_id_seq", sequenceName = "experience_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "experience_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private Experience experience;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Experience getExperience() {
        return experience;
    }

    public void setExperience(Experience experience) {
        this.experience = experience;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ExperienceTranslation{" +
                "id=" + id +
                ", experience=" + experience +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
