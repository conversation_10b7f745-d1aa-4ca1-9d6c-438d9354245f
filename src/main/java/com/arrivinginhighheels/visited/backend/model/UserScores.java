package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "user_scores")
@Getter
@Setter
@NoArgsConstructor
public class UserScores {

    @Id
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "score")
    private Double score;

    @Column(name = "sum")
    private Long sum;

}
