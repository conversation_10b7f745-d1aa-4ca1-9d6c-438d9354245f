package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Getter
@Entity
@Table(name = "user_cities")
public class UserCity {
    @Id
    @SequenceGenerator(name = "user_places_id_seq", sequenceName = "user_places_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_places_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn()
    private User user;

    @ManyToOne(optional = false)
    private City city;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private SelectionType type;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    public UserCity() {
    }

    public UserCity(final User user, final City city, final SelectionType type) {
        this.user = user;
        this.city = city;
        this.type = type;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setUser(final User user) {
        this.user = user;
    }

    public void setPlace(final City city) {
        this.city = city;
    }

    public void setType(final SelectionType type) {
        this.type = type;
    }

    public void setTimestamp(final LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "UserPlace{" +
                "id=" + id +
                ", user=" + user +
                ", place=" + city +
                ", type=" + type +
                ", timestamp=" + timestamp +
                '}';
    }
}
