package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.util.Date;

@Getter
@Entity
@Table(name = "experience_geo_areas", uniqueConstraints = {
        @UniqueConstraint(name = "AK_ExperienceGeoArea", columnNames = { "geo_area_id", "experience_id" })
})
public class ExperienceGeoArea {
    @Id
    @SequenceGenerator(name = "experience_geo_areas_id_seq", sequenceName = "experience_geo_areas_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "experience_geo_areas_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @ManyToOne(optional = false)
    private Experience experience;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public ExperienceGeoArea() {
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public void setExperience(Experience experience) {
        this.experience = experience;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "ExperienceGeoArea{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", experience=" + experience +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
