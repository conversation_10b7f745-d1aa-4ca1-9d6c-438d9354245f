package com.arrivinginhighheels.visited.backend.model;

import java.util.Date;
import java.util.Objects;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

@Entity
@Table(name = "sponsors")
public class Sponsor {
    @Id
    @SequenceGenerator(name = "sponsor_id_seq", sequenceName = "sponsor_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sponsor_id_seq")
    private Long id;

    @NotNull
    @Column(length = 100, nullable = false)
    private String name;

    @NotNull
    @Column(length = 255, nullable = true)
    private String url;

    @NotNull
    @Column(length = 255, nullable = true)
    private String urlIos;

    @NotNull
    @Column(length = 255, nullable = true)
    private String urlAndroid;

    @Column(nullable = true, columnDefinition = "text", name = "promotional_text")
    private String promotionalText;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public Sponsor() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPromotionalText() {
        return promotionalText;
    }

    public void setPromotionalText(String promotionalText) {
        this.promotionalText = promotionalText;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    public String getUrlIos() {
        return urlIos;
    }

    public void setUrlIos(String urlIos) {
        this.urlIos = urlIos;
    }

    public String getUrlAndroid() {
        return urlAndroid;
    }

    public void setUrlAndroid(String urlAndroid) {
        this.urlAndroid = urlAndroid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        Sponsor sponsor = (Sponsor) o;
        return Objects.equals(id, sponsor.id) && Objects.equals(name, sponsor.name) && Objects.equals(url, sponsor.url)
                && Objects.equals(urlIos, sponsor.urlIos) && Objects.equals(urlAndroid, sponsor.urlAndroid)
                && Objects.equals(promotionalText, sponsor.promotionalText)
                && Objects.equals(lastModificationTime, sponsor.lastModificationTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, url, urlIos, urlAndroid, promotionalText, lastModificationTime);
    }

    @Override
    public String toString() {
        return "Sponsor{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", urlIos='" + urlIos + '\'' +
                ", urlAndroid='" + urlAndroid + '\'' +
                ", promotionalText='" + promotionalText + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
