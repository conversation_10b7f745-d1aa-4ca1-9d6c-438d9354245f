package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Entity
@Table(name = "popularity")
public class Popularity {

    @Id
    @Column(name = "geo_area_id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "geo_area_id", updatable = false, insertable = false)
    private GeoArea geoArea;

    @Column(name = "geo_area_iso_key")
    private String geoAreaIsoKey;

    @Column(name = "geo_area_name")
    private String geoAreaName;

    private Long total;

    private Integer rank;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public String getGeoAreaIsoKey() {
        return geoAreaIsoKey;
    }

    public void setGeoAreaIsoKey(String geoAreaIsoKey) {
        this.geoAreaIsoKey = geoAreaIsoKey;
    }

    public String getGeoAreaName() {
        return geoAreaName;
    }

    public void setGeoAreaName(String geoAreaName) {
        this.geoAreaName = geoAreaName;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public Popularity id(final Long id) {
        this.id = id;
        return this;
    }

    public Popularity geoArea(final GeoArea geoArea) {
        this.geoArea = geoArea;
        return this;
    }

    public Popularity geoAreaIsoKey(final String geoAreaIsoKey) {
        this.geoAreaIsoKey = geoAreaIsoKey;
        return this;
    }

    public Popularity geoAreaName(final String geoAreaName) {
        this.geoAreaName = geoAreaName;
        return this;
    }

    public Popularity total(final Long total) {
        this.total = total;
        return this;
    }

    public Popularity rank(final Integer rank) {
        this.rank = rank;
        return this;
    }

    @Override
    public String toString() {
        return "Popularity{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", geoAreaIsoKey='" + geoAreaIsoKey + '\'' +
                ", geoAreaName='" + geoAreaName + '\'' +
                ", total=" + total +
                ", rank=" + rank +
                '}';
    }
}
