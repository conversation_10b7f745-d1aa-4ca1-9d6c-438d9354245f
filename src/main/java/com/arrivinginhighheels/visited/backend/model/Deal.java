package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "deals")
public class Deal {

    @Id
    @SequenceGenerator(name = "deals_id_seq", sequenceName = "deals_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "deals_id_seq")
    private long id;

    @NotNull
    @Column(name = "url")
    private String url;

    @ManyToOne
    private GeoArea geoArea;

    @ManyToOne
    private Experience experience;

    @NotNull
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public Deal() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public Experience getExperience() {
        return experience;
    }

    public void setExperience(Experience experience) {
        this.experience = experience;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        Deal deal = (Deal) o;
        return id == deal.id && Objects.equals(url, deal.url) && Objects.equals(geoArea, deal.geoArea)
                && Objects.equals(experience, deal.experience)
                && Objects.equals(lastModificationTime, deal.lastModificationTime);
    }

    @Override
    public int hashCode() {
        int result = (int) (id ^ (id >>> 32));
        result = 31 * result + (url != null ? url.hashCode() : 0);
        result = 31 * result + (geoArea != null ? geoArea.hashCode() : 0);
        result = 31 * result + (experience != null ? experience.hashCode() : 0);
        result = 31 * result + (lastModificationTime != null ? lastModificationTime.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Deal{" +
                "id=" + id +
                ", url='" + url + '\'' +
                ", geoArea=" + geoArea +
                ", experience=" + experience +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
