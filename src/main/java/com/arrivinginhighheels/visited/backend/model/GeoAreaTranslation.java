package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;

@Entity
@Table(name = "geoarea_translations", uniqueConstraints = {
        @UniqueConstraint(name = "AK_GeoAreaTranslations", columnNames = { "geo_area_id", "supported_language_id" })
})
public class GeoAreaTranslation {

    @Id
    @SequenceGenerator(name = "geoarea_translations_id_seq", sequenceName = "geoarea_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "GeoAreaTranslation{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
