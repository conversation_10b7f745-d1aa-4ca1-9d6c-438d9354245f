package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.validation.constraints.NotNull;

/**
 * Platform type.
 */
@Embeddable
public class Platform {

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(length = 50, nullable = false)
    private OSType os;

    @NotNull
    @Column(length = 50, nullable = false)
    private String version;

    public Platform() {
    }

    public Platform(OSType os, String version) {
        this.os = os;
        this.version = version;
    }

    public OSType getOs() {
        return os;
    }

    public void setOs(OSType os) {
        this.os = os;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        Platform platform = (Platform) o;

        if (os != null ? !os.equals(platform.os) : platform.os != null)
            return false;
        return version != null ? version.equals(platform.version) : platform.version == null;
    }

    @Override
    public int hashCode() {
        int result = os != null ? os.hashCode() : 0;
        result = 31 * result + (version != null ? version.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Platform{" +
                "os='" + os + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
