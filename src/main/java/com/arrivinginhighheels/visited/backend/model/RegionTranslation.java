package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "region_translations", uniqueConstraints = {
        @UniqueConstraint(name = "AK_RegionTranslations", columnNames = { "region_id", "supported_language_id" })
})
public class RegionTranslation {

    @Id
    @SequenceGenerator(name = "region_translations_id_seq", sequenceName = "region_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "region_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private Region region;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public RegionTranslation() {
    }

    @Override
    public String toString() {
        return "RegionTranslation{" +
                "id=" + id +
                ", region=" + region +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
