package com.arrivinginhighheels.visited.backend.model;

import com.arrivinginhighheels.visited.backend.dto.AreaLabelDTO;

import jakarta.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "geoarea_labels")
public class GeoAreaLabel {

    @Id
    @SequenceGenerator(name = "geoarea_labels_id_seq", sequenceName = "geoarea_labels_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_labels_id_seq")
    private Long id;

    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    private GeoArea geoArea;

    @Column(nullable = false, precision = 19, scale = 14)
    private BigDecimal latitude;

    @Column(nullable = false, precision = 19, scale = 14)
    private BigDecimal longitude;

    @Column(nullable = false)
    private Integer resolution;

    public GeoAreaLabel() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public Integer getResolution() {
        return resolution;
    }

    public void setResolution(Integer resolution) {
        this.resolution = resolution;
    }

    @Override
    public String toString() {
        return "GeoAreaLabel{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", resolution=" + resolution +
                '}';
    }

    public AreaLabelDTO asAreaLabelDTO() {
        return new AreaLabelDTO().id(getId())
                .resolution(getResolution())
                .coordinate(new Double[] { getLongitude().doubleValue(), getLatitude().doubleValue() });
    }
}
