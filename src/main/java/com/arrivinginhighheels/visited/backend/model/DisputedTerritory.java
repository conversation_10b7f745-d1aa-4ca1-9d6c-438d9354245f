package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;
import java.util.Objects;

@Setter
@Getter
@Entity
@Table(name = "disputed_territories")
public class DisputedTerritory {
    @Id
    @SequenceGenerator(name = "disputed_territories_id_seq", sequenceName = "disputed_territories_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "disputed_territories_id_seq")
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    private GeoArea geoArea;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "parent_area_id")
    private GeoArea parent;

    private Boolean defaults;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    @Column(length = 15, updatable = false, name = "min_version")
    private String minVersion;

    public DisputedTerritory() {
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final DisputedTerritory that = (DisputedTerritory) o;
        return Objects.equals(id, that.id) && Objects.equals(geoArea, that.geoArea)
                && Objects.equals(parent, that.parent) && Objects.equals(defaults, that.defaults)
                && Objects.equals(lastModificationTime, that.lastModificationTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, geoArea, parent, defaults, lastModificationTime);
    }

    @Override
    public String toString() {
        return "DisputedTerritory{" +
                "id=" + id +
                ", area=" + geoArea +
                ", parent=" + parent +
                ", defaults=" + defaults +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
