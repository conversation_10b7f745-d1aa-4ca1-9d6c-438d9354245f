package com.arrivinginhighheels.visited.backend.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Entity
@Table(name = "cities")
@NoArgsConstructor
@AllArgsConstructor
public class City {
    @Id
    @SequenceGenerator(name = "cities_id_seq", sequenceName = "cities_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "cities_id_seq")
    private Long id;

    @NotNull
    @Column(length = 100, nullable = false)
    private String name;

    @Column(name = "lat", precision = 19, scale = 10)
    private BigDecimal latitude;

    @Column(name = "long", precision = 19, scale = 10)
    private BigDecimal longitude;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "level_one_geo_area_id", updatable = false, insertable = false)
    private GeoArea geoAreaLevelOne;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "level_two_geo_area_id", updatable = false, insertable = false)
    private GeoArea geoAreaLevelTwo;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public City(Long id, String name, BigDecimal latitude, BigDecimal longitude,
                GeoArea geoAreaLevelOne, GeoArea geoAreaLevelTwo) {
        this.id = id;
        this.name = name;
        this.latitude = latitude;
        this.longitude = longitude;
        this.geoAreaLevelOne = geoAreaLevelOne;
        this.geoAreaLevelTwo = geoAreaLevelTwo;
    }

    @Override
    public String toString() {
        return "City{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", geoAreaLevelOne=" + geoAreaLevelOne +
                ", geoAreaLevelTwo=" + geoAreaLevelTwo +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
