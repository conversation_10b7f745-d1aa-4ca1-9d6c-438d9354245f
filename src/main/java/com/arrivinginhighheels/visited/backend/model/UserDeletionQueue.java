package com.arrivinginhighheels.visited.backend.model;

import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

@Entity
@Table(name = "user_deletion_queue")
public class UserDeletionQueue {

    @Id
    @SequenceGenerator(name = "user_deletion_queue_id_seq", sequenceName = "user_deletion_queue_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "user_deletion_queue_id_seq", strategy = GenerationType.SEQUENCE)
    private Long id;

    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    public UserDeletionQueue() {
    }

    public UserDeletionQueue(@NotNull Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @NotNull
    public Long getUserId() {
        return userId;
    }

    public void setUserId(@NotNull Long userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        UserDeletionQueue that = (UserDeletionQueue) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(userId, that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, userId);
    }

    @Override
    public String toString() {
        return "UserDeletionQueue{" +
                "id=" + id +
                ", userId=" + userId +
                '}';
    }
}
