package com.arrivinginhighheels.visited.backend.model;

import com.arrivinginhighheels.visited.backend.features.todoLists.TodoList;
import lombok.Getter;

import jakarta.persistence.*;

@Getter
@Entity
@Table(name = "todo_list_book_links")
public class TodoListBookLink {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "todo_list_book_links_id_gen")
    @SequenceGenerator(name = "todo_list_book_links_id_gen", sequenceName = "todo_list_book_links_id_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "book_id")
    private BookLink book;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "todo_list_id")
    private TodoList todoList;

    @Override
    public String toString() {
        return "TodoListBookLink{" +
                "id=" + id +
                ", book=" + book +
                ", todoList=" + todoList +
                '}';
    }
}
