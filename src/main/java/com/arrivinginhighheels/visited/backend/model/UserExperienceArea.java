package com.arrivinginhighheels.visited.backend.model;

import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_experience_areas")
public class UserExperienceArea {

    @Id
    @SequenceGenerator(name = "user_experiences_area_id_seq", sequenceName = "user_experiences_area_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_experiences_area_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(unique = false)
    private User user;

    @ManyToOne(optional = false)
    private Experience experience;

    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private SelectionType type;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    public UserExperienceArea() {
    }

    public UserExperienceArea(User user, Experience experience, GeoArea geoArea, SelectionType selectionType) {
        this.user = user;
        this.experience = experience;
        this.geoArea = geoArea;
        this.type = selectionType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Experience getExperience() {
        return experience;
    }

    public void setExperience(Experience experience) {
        this.experience = experience;
    }

    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(SelectionType type) {
        this.type = type;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "UserExperienceArea{" +
                "id=" + id +
                ", user=" + user +
                ", experience=" + experience +
                ", geoArea=" + geoArea +
                ", type=" + type +
                ", timestamp=" + timestamp +
                '}';
    }
}
