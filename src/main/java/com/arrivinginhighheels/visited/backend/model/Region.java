package com.arrivinginhighheels.visited.backend.model;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Represents a region of the world, containing the countries
 */
@Entity
@Table(name = "regions")
public class Region {

    @Id
    @SequenceGenerator(name = "regions_id_seq", sequenceName = "regions_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "regions_id_seq")
    private Long id;

    @NotNull
    @Column(length = 100, nullable = false)
    private String name;

    @OneToMany(mappedBy = "continent", cascade = CascadeType.ALL)
    private List<CountryContinent> countries;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    // boilerplate-code
    public Region() {
    }

    public Region(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<CountryContinent> getCountries() {
        return countries;
    }

    public void setCountries(List<CountryContinent> countries) {
        this.countries = countries;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        Region region = (Region) o;
        return Objects.equals(id, region.id) && Objects.equals(name, region.name)
                && Objects.equals(countries, region.countries)
                && Objects.equals(lastModificationTime, region.lastModificationTime);
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (countries != null ? countries.hashCode() : 0);
        result = 31 * result + (lastModificationTime != null ? lastModificationTime.hashCode() : 0);
        return result;
    }
}
