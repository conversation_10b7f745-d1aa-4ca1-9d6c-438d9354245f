package com.arrivinginhighheels.visited.backend.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "geoarea_details")
public class AreaDetails {

    @Id
    @Column(name = "geo_area_id", nullable = false)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "geo_area_id", updatable = false, insertable = false)
    private GeoArea geoArea;

    private BigDecimal size;

    private Long population;

    @OneToMany(mappedBy = "areaDetails", cascade = CascadeType.ALL)
    private List<GeoAreaMustSee> mustSees;

    @JoinColumn(name = "thumbnail_url_id", updatable = false, insertable = false)
    @ManyToOne(fetch = FetchType.EAGER)
    private AdaptiveImage thumbnail;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public AreaDetails() {
    }

    @Override
    public String toString() {
        return "AreaDetails{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", size=" + size +
                ", population=" + population +
                ", mustSees=" + mustSees +
                ", thumbnail=" + thumbnail +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
