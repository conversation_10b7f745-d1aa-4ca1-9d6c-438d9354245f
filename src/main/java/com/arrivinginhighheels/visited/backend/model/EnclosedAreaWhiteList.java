package com.arrivinginhighheels.visited.backend.model;

import java.util.Date;

import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity
@Table(name = "geoareas_enclosed_whitelist")
public class EnclosedAreaWhiteList {
    @Id
    @SequenceGenerator(name = "geoareas_enclosed_whitelist_id_seq", sequenceName = "geoareas_enclosed_whitelist_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoareas_enclosed_whitelist_id_seq")
    private Long id;

    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    @JoinColumn(name = "geo_area_id", updatable = false, insertable = false)
    private GeoArea area;

    @ManyToOne(optional = false, fetch = FetchType.EAGER)
    @JoinColumn(name = "surrounding_geo_area_id", updatable = false, insertable = false)
    private GeoArea surroundingArea;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public EnclosedAreaWhiteList() {
    }

    public EnclosedAreaWhiteList(Long id, GeoArea area, GeoArea surroundingArea, Date lastModificationTime) {
        this.id = id;
        this.area = area;
        this.surroundingArea = surroundingArea;
        this.lastModificationTime = lastModificationTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeoArea getArea() {
        return area;
    }

    public void setArea(GeoArea area) {
        this.area = area;
    }

    public GeoArea getSurroundingArea() {
        return surroundingArea;
    }

    public void setSurroundingArea(GeoArea surroundingArea) {
        this.surroundingArea = surroundingArea;
    }

    public Date getLastModificationTime() {
        return lastModificationTime;
    }

    public void setLastModificationTime(Date lastModificationTime) {
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "EnclosedAreaWhiteList{" +
                "id=" + id +
                ", area=" + area +
                ", surroundingArea=" + surroundingArea +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
