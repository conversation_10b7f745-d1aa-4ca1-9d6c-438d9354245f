package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "ad_categories")
public class AdCategory {
    @Id
    @SequenceGenerator(name = "ad_category_id_seq", sequenceName = "ad_category_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ad_category_id_seq")
    private Long id;

    @NotNull
    @Column(length = 250, nullable = false)
    private String provider;

    @NotNull
    @Column(name = "inventory_name", length = 250)
    private String name;

    @NotNull
    @Column(name = "inventory_hash", length = 250)
    private String hash;

    @ManyToOne
    private GeoArea geoArea;

    @ManyToOne
    private Experience experience;

    @Column(length = 32, nullable = false)
    private String os;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    public AdCategory() {
    }


    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof AdCategory that))
            return false;

        if (id != null ? !id.equals(that.id) : that.id != null)
            return false;
        if (provider != null ? !provider.equals(that.provider) : that.provider != null)
            return false;
        if (name != null ? !name.equals(that.name) : that.name != null)
            return false;
        if (hash != null ? !hash.equals(that.hash) : that.hash != null)
            return false;
        if (geoArea != null ? !geoArea.equals(that.geoArea) : that.geoArea != null)
            return false;
        if (experience != null ? !experience.equals(that.experience) : that.experience != null)
            return false;
        return lastModificationTime != null ? lastModificationTime.equals(that.lastModificationTime)
                : that.lastModificationTime == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (provider != null ? provider.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (hash != null ? hash.hashCode() : 0);
        result = 31 * result + (geoArea != null ? geoArea.hashCode() : 0);
        result = 31 * result + (experience != null ? experience.hashCode() : 0);
        result = 31 * result + (lastModificationTime != null ? lastModificationTime.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "AdCategory{" +
                "id=" + id +
                ", provider='" + provider + '\'' +
                ", name='" + name + '\'' +
                ", hash='" + hash + '\'' +
                ", geoArea=" + geoArea +
                ", experience=" + experience +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
