package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;

@Entity
@Table(name = "geoarea_must_see")
public class GeoAreaMustSee {

    @Id
    @SequenceGenerator(name = "geoarea_must_see_id_seq", sequenceName = "geoarea_must_see_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_must_see_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(name = "geo_area_id")
    private AreaDetails areaDetails;

    @Column(nullable = false, length = 250)
    private String description;

    public GeoAreaMustSee() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public AreaDetails getAreaDetails() {
        return areaDetails;
    }

    public void setAreaDetails(AreaDetails areaDetails) {
        this.areaDetails = areaDetails;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "GeoAreaMustSee{" +
                "id=" + id +
                ", areaDetails=" + areaDetails +
                ", description='" + description + '\'' +
                '}';
    }
}
