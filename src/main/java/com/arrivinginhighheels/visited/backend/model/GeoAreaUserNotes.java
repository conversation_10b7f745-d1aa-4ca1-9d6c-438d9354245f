package com.arrivinginhighheels.visited.backend.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@Entity
@Table(name = "geoarea_user_notes", uniqueConstraints = {
        @UniqueConstraint(name = "AK_GeoAreaUserNotes", columnNames = { "geo_area_id", "user_id" })
})
public class GeoAreaUserNotes {

    @Id
    @SequenceGenerator(name = "geoarea_user_notes_id_seq", sequenceName = "geoarea_user_notes_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "geoarea_user_notes_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @ManyToOne(optional = false)
    private User user;

    @Column(nullable = false, columnDefinition = "text")
    private String notes;

    public GeoAreaUserNotes() {
    }

    public GeoAreaUserNotes id(final Long id) {
        this.id = id;
        return this;
    }

    public GeoAreaUserNotes geoArea(final GeoArea geoArea) {
        this.geoArea = geoArea;
        return this;
    }

    public GeoAreaUserNotes user(final User user) {
        this.user = user;
        return this;
    }

    public GeoAreaUserNotes notes(final String notes) {
        this.notes = notes;
        return this;
    }

    @Override
    public String toString() {
        return "GeoAreaUserNotes{" +
                "id=" + id +
                ", geoArea=" + geoArea +
                ", user=" + user +
                ", notes='" + notes + '\'' +
                '}';
    }
}
