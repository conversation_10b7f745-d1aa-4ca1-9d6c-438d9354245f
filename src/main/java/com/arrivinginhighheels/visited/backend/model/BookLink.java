package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "book_links")
@Getter
public class BookLink {
    @Id
    @SequenceGenerator(name = "book_links_id_seq", sequenceName = "book_links_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "book_links_id_seq")
    private Long id;

    private String name;

    private String url;

    private String imageUrl;

    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_modified")
    private Date lastModificationTime;

    public BookLink() {
    };

    public BookLink(
            Long id,
            String name,
            String url,
            String imageUrl,
            Date lastModificationTime) {
        this.id = id;
        this.name = name;
        this.url = url;
        this.imageUrl = imageUrl;
        this.lastModificationTime = lastModificationTime;
    }

    @Override
    public String toString() {
        return "BookLink{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", url='" + url + '\'' +
                ", imageUrl='" + imageUrl + '\'' +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
