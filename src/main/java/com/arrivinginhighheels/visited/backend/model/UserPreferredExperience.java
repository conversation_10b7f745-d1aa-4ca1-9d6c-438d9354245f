package com.arrivinginhighheels.visited.backend.model;

import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_preferred_experiences", uniqueConstraints = {
        @UniqueConstraint(name = "AK_UserExperience", columnNames = { "user_id", "experience_id" })
})
public class UserPreferredExperience {

    @Id
    @SequenceGenerator(name = "user_preferred_experiences_id_seq", sequenceName = "user_preferred_experiences_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_preferred_experiences_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    @JoinColumn(unique = false)
    private User user;

    @ManyToOne(optional = false)
    private Experience experience;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    public UserPreferredExperience() {
    }

    public UserPreferredExperience(Long id, User user, Experience experience, LocalDateTime timestamp) {
        this.id = id;
        this.user = user;
        this.experience = experience;
        this.timestamp = timestamp;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Experience getExperience() {
        return experience;
    }

    public void setExperience(Experience experience) {
        this.experience = experience;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "UserPreferredExperience{" +
                "id=" + id +
                ", user=" + user +
                ", experience=" + experience +
                ", timestamp=" + timestamp +
                '}';
    }
}
