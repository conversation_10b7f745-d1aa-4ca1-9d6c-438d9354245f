package com.arrivinginhighheels.visited.backend.model;

import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Represents a user selected area, with a given type
 */
@Getter
@Entity
@Table(name = "selections")
public class Selection {

    @Setter
    @Id
    @SequenceGenerator(name = "selections_id_seq", sequenceName = "selections_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "selections_id_seq", strategy = GenerationType.SEQUENCE)
    private Long id;

    @Setter
    @NotNull
    @ManyToOne(optional = false)
    private User user;

    @Setter
    @NotNull
    @ManyToOne(optional = false)
    private GeoArea geoArea;

    @Setter
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private SelectionType type;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    @Setter
    @Column(nullable = false, length = 10)
    private String date;

    // boilerplate-code
    public Selection() {
    }

    public Selection(User user, GeoArea geoArea, SelectionType type, LocalDateTime timestamp) {
        this.user = user;
        this.geoArea = geoArea;
        this.type = type;
        this.setTimestamp(timestamp);
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        if (timestamp != null) {
            this.date = timestamp.format(DateTimeFormatter.ISO_DATE);
        } else {
            this.date = null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        Selection selection = (Selection) o;

        if (id != null ? !id.equals(selection.id) : selection.id != null)
            return false;
        if (user != null ? !user.equals(selection.user) : selection.user != null)
            return false;
        if (geoArea != null ? !geoArea.equals(selection.geoArea) : selection.geoArea != null)
            return false;
        if (type != selection.type)
            return false;
        return timestamp != null ? timestamp.equals(selection.timestamp) : selection.timestamp == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (user != null ? user.hashCode() : 0);
        result = 31 * result + (geoArea != null ? geoArea.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "Selection{" +
                "id=" + id +
                ", user=" + user +
                ", geoArea=" + geoArea +
                ", type=" + type +
                ", timestamp=" + timestamp +
                '}';
    }
}
