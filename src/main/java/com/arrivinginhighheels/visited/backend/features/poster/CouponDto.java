package com.arrivinginhighheels.visited.backend.features.poster;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Builder
@Getter
@Setter
@AllArgsConstructor
public final class CouponDto {
    private final String id;
    private final String name;
    private final String code;
    private final BigDecimal percentOff;
    private final Long amountOff;
    private final PriceDto discount;
}
