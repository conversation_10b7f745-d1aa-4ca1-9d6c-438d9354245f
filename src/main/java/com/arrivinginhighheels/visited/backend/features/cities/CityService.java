package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.SelectionRepository;
import com.arrivinginhighheels.visited.backend.service.AbstractSelectionService;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CityService extends AbstractSelectionService {
    private final CityRepository cityRepository;
    private final CityTranslationService cityTranslationService;
    private final UserCitiesRepository userCitiesRepository;
    private final SelectionService selectionService;
    private final SelectionRepository areaSelectionRepository;

    public CityService(
            CityRepository cityRepository,
            CityTranslationService cityTranslationService,
            UserCitiesRepository userCitiesRepository,
            SelectionService selectionService,
            SelectionRepository repository) {
        this.cityRepository = cityRepository;
        this.cityTranslationService = cityTranslationService;
        this.userCitiesRepository = userCitiesRepository;
        this.selectionService = selectionService;
        this.areaSelectionRepository = repository;
    }

    public CityDto fetchCityDtoById(final Long id) {
        final var city = fetchCityById(id);
        return buildCityDto(city, cityTranslationService.getCityName(city));
    }

    public Optional<CityDto> fetchLivedCity(final User user) {
        final var cities = userCitiesRepository.findAllByUserAndType(user, SelectionType.LIVED);
        if (cities == null || cities.isEmpty()) {
            return Optional.empty();
        }

        final var city = cities.stream().findFirst().get().getCity();
        return Optional.of(buildCityDto(city, cityTranslationService.getCityName(city)));
    }

    public List<CityDto> fetchCitiesForIsoCode(final String isoCode) {
        final var area = geoAreaRepository.findByIsoKey(isoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(isoCode));

        // TODO: Optimize to be a single db call
        final var cities = cityRepository.findAllByGeoAreaLevelOne(area);
        final var translations = cityTranslationService.getTranslations(cities);

        return cities.stream().map((e) -> buildCityDto(e, translations.get(e))).collect(Collectors.toList());
    }

    public List<CityDto> searchForCity(final String query) {
        final var currentLanguage = cityTranslationService.getCurrentLanguage();
        if (currentLanguage.isEnglish()) {
            final var cities = cityRepository.fuzzySearchByName(query);
            return cities.stream().map(this::buildCityDto).toList();
        }

        final var localizedResults = cityRepository.fuzzySearchByNameAndLanguage(query, currentLanguage.getId());
        return localizedResults
                .stream()
                .map(e -> {
                            var city = new CityDto()
                                    .setId((Long) e[0])
                                    .setName((String) e[1])
                                    .setCoordinate(((BigDecimal) e[2]).doubleValue(), ((BigDecimal) e[3]).doubleValue())
                                    .setLevelOneIso((String) e[4]);

                            //TODO: It would be nice to do this in a single query...
                            var secondLevelId = e[5];
                            if (secondLevelId != null) {
                                Long id  = null;
                                if (secondLevelId instanceof Long) {
                                    id = (Long) secondLevelId;
                                } else if (secondLevelId instanceof BigInteger) {
                                    id = ((BigInteger) secondLevelId).longValue();
                                }

                                if (id != null) {
                                    var area = geoAreaRepository.getReferenceById(id);
                                    city = city.setLevelTwoIso(area.getIsoKey());
                                }
                            }

                            return city;
                        }
//                        .setLevelTwoIso(e.levelTwoIsoCode())
                )
                .toList();
    }

    public UserCitiesDTO getCitiesForUser(final User user) {
        final var selections = userCitiesRepository.findAllByUser(user);

        final var cities = selections.stream().map(UserCity::getCity).collect(Collectors.toList());

        final var localized = cityTranslationService.getTranslations(cities);

        CityDto live = null;
        final List<CityDto> beenCities = new ArrayList<>();
        final List<CityDto> wantCities = new ArrayList<>();

        for (final UserCity selection : selections) {
            final SelectionType type = selection.getType();
            final City city = selection.getCity();
            final CityDto dto = buildCityDto(city, localized.get(city));

            switch (type) {
                case LIVED -> live = dto;
                case WANT -> wantCities.add(dto);
                case BEEN -> beenCities.add(dto);
                default -> {
                }
            }
        }
        return new UserCitiesDTO(live, beenCities, wantCities);
    }

    public UserCitiesUpdatedDTO handleSelection(final UserCitySelectionDTO selection, final User user) {
        final var type = selection.getType();
        final var city = fetchCityById(selection.getCityId());

        UserCity userCity;

        try {
            final var maybeUserCity = userCitiesRepository.findByUserAndCity(user, city);
            userCity = maybeUserCity.orElseGet(() -> new UserCity(user, city, type));
        } catch (Exception e) {
            userCity = new UserCity(user, city, type);
        }

        // Don't allow existing live cities to be changed, causing the hierarchy to be buggered
        // Live cities can only be replaced by another city
        if (userCity.getType() == SelectionType.LIVED && type != SelectionType.LIVED) {
            return new UserCitiesUpdatedDTO(getCitiesForUser(user), null);
        }

        if (type == SelectionType.LIVED) {
            demoteExistingLivedCity(user);
        }

        userCity.setTimestamp(LocalDateTime.now());
        userCity.setType(type);
        saveUserCity(userCity);

        final var currentCities = getCitiesForUser(user);
        final var selectionUpdate = updateGeoAreaSelections(user, city, type);
        return new UserCitiesUpdatedDTO(currentCities, selectionUpdate);
    }

    public UserCitiesUpdatedDTO handleBatchSelection(final User user, final UserCitiesBatchSelectDTO batchDto) {
        final var updates = batchDto.getUpdates();
        if (updates == null) {
            final var selectionDto = selectionService.getUserSelections(user);
            return new UserCitiesUpdatedDTO(null, selectionDto);
        }

        // Delete "Clear" selections
        final var toDelete = updates.get(SelectionType.CLEAR);
        if (toDelete != null && !toDelete.isEmpty()) {
            final var userCitiesToDelete = userCitiesRepository.findAllByUserAndCityIdIn(user, toDelete);
            userCitiesRepository.deleteAll(userCitiesToDelete);
        }

        var allIdsToUpdate = new ArrayList<>(Stream.concat(updates.getOrDefault(SelectionType.BEEN, Collections.emptyList()).stream(), updates.getOrDefault(SelectionType.WANT, Collections.emptyList()).stream()).toList());

        // Update Existing Selections
        if (!allIdsToUpdate.isEmpty()) {
            final List<UserCity> userCities = doBatchCityUpdate(user, batchDto, allIdsToUpdate);
            doBatchGeoAreasUpdates(user, userCities);
        }

        final var selectionDto = selectionService.getUserSelections(user);
        return new UserCitiesUpdatedDTO(null, selectionDto);
    }

    private List<UserCity> doBatchCityUpdate(User user, UserCitiesBatchSelectDTO batchDto, ArrayList<Long> allIds) {
        final var userCities = userCitiesRepository.findAllByUserAndCityIdIn(user, allIds);
        for (final var existingCity : userCities) {
            final var cityId = existingCity.getCity().getId();
            final var selectionType = batchDto.findSelection(cityId);
            if (selectionType == SelectionType.CLEAR) {
                continue;
            }
            existingCity.setType(selectionType);
            existingCity.setTimestamp(LocalDateTime.now());
            allIds.remove(cityId);
        }

        // Add new selections
        final var citiesToAdd = cityRepository.findAllByIdIn(allIds);
        for (final var city : citiesToAdd) {
            final var selection = batchDto.findSelection(city.getId());
            if (selection == SelectionType.CLEAR) {
                continue;
            }
            var userCity = new UserCity();
            userCity.setUser(user);
            userCity.setType(selection);
            userCity.setPlace(city);
            userCity.setTimestamp(LocalDateTime.now());
            userCities.add(userCity);
        }

        if (!userCities.isEmpty()) {
            userCitiesRepository.saveAll(userCities);
        }
        return userCities;
    }

    private void doBatchGeoAreasUpdates(User user, List<UserCity> userCities) {
        final var allSelections = areaSelectionRepository.findAllByUser(user);

        for (final var userCity : userCities) {
            final var selectionType = userCity.getType();
            final var existing = allSelections.stream().filter(e -> e.getGeoArea().equals(userCity.getCity().getGeoAreaLevelOne())).findFirst();

            if (existing.isEmpty()) {
                var newSelection = new Selection();
                newSelection.setUser(user);
                newSelection.setType(selectionType);
                newSelection.setGeoArea(userCity.getCity().getGeoAreaLevelOne());
                newSelection.setTimestamp(LocalDateTime.now());
                allSelections.add(newSelection);
                continue;
            }

            final var selection = existing.get();

            if (selectionType.getWeight() > selection.getType().getWeight()) {
                selection.setType(selectionType);
                selection.setTimestamp(LocalDateTime.now());
            }

        }

        areaSelectionRepository.saveAll(allSelections);
    }

    private SelectionsDTO updateGeoAreaSelections(final User user, final City city, final SelectionType selectionType) {
        if (selectionType == SelectionType.CLEAR) {
            return null;
        }

        final var area = city.getGeoAreaLevelOne();
        final var existing = selectionService.getSelectionsForArea(user, area);

        if (existing == null) {
            return null;
        }

        if (existing.getType().getWeight() >= selectionType.getWeight()) {
            return null;
        }

        return selectionService.handleSelection(user, city.getGeoAreaLevelOne().getIsoKey(), selectionType);
    }

    private void demoteExistingLivedCity(final User user) {
        final var selections = userCitiesRepository.findAllByUserAndType(user, SelectionType.LIVED);

        for (final var selection : selections) {
            selection.setType(SelectionType.BEEN);
            selection.setTimestamp(LocalDateTime.now());
            userCitiesRepository.save(selection);
        }
    }

    private void saveUserCity(final UserCity selection) {
        if (selection.getType().equals(SelectionType.CLEAR)) {
            try {
                userCitiesRepository.delete(selection);
                return;
            } catch (Exception e) {
                // The city never existed so there is nothing to do
                return;
            }
        }

        userCitiesRepository.save(selection);
    }

//    @Deprecated
//    // TODO: This needs to be replaced with a single database call
//    private CityDto buildCityDto(final City city) {
//        final var localizedName = cityTranslationService.getCityName(city);
//        return buildCityDto(city, localizedName);
//    }

    private CityDto buildCityDto(final City city) {
        return buildCityDto(city, null);
    }

    private CityDto buildCityDto(final City city, final String localizedName) {
        final var dto = new CityDto().setId(city.getId()).setName(localizedName != null ? localizedName : city.getName()).setLevelOneIso(city.getGeoAreaLevelOne().getIsoKey()).setCoordinate(city.getLongitude().doubleValue(), city.getLatitude().doubleValue());

        final var secondLevel = city.getGeoAreaLevelTwo();
        if (secondLevel != null) {
            dto.setLevelTwoIso(secondLevel.getIsoKey());
        }

        return dto;
    }

    private City fetchCityById(final Long id) {
        final var city = cityRepository.findById(id);

        if (city.isEmpty()) {
            throw new CityNotFoundException(id);
        }

        return city.get();
    }

    public Date getMaxModificationTimeOfCitiesByIsoCode(final String areaIsoCode) {
        final var area = getGeoAreaVerifyingIfItExists(areaIsoCode);
        return cityRepository.getMaxModificationTimeOfCitiesByArea(area);
    }
}


