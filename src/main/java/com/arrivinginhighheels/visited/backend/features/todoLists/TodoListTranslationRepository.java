package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

public interface TodoListTranslationRepository extends JpaRepository<TodoListTranslation, Long> {
    TodoListTranslation findByListAndSupportedLanguage(TodoList list, SupportedLanguage supportedLanguage);
}
