package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface PrintingRepository extends JpaRepository<PrintingOrder, Long> {

    void deleteByUser(User user);

    List<PrintingOrder> findAllBySubmittedToPrinterIsNullAndChargeIdIsNotNull();

    Optional<PrintingOrder> findFirstByPaymentIntentId(String paymentIntendId);
}
