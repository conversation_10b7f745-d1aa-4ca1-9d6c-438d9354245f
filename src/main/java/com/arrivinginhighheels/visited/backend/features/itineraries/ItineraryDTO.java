package com.arrivinginhighheels.visited.backend.features.itineraries;

import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceDTO;
import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationDTO;
import com.arrivinginhighheels.visited.backend.features.todoLists.TodoListItemDTO;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter @Setter @NoArgsConstructor
public class ItineraryDTO {
    List<InspirationDTO> inspirations;
    List<ExperienceDTO> experiences;
    List<TodoListItemDTO> listItems;
}
