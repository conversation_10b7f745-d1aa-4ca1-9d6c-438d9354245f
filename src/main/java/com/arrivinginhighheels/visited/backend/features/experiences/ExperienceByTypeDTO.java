package com.arrivinginhighheels.visited.backend.features.experiences;

import java.util.ArrayList;
import java.util.List;

public class ExperienceByTypeDTO {
    private List<String> beenAreas = new ArrayList<>();
    private List<String> wantAreas = new ArrayList<>();

    public ExperienceByTypeDTO() {
    }

    public ExperienceByTypeDTO(List<String> beenAreas, List<String> wantAreas) {
        this.beenAreas = beenAreas;
        this.wantAreas = wantAreas;
    }

    public List<String> getBeenAreas() {
        return beenAreas;
    }

    public void setBeenAreas(List<String> beenAreas) {
        this.beenAreas = beenAreas;
    }

    public List<String> getWantAreas() {
        return wantAreas;
    }

    public void setWantAreas(List<String> wantAreas) {
        this.wantAreas = wantAreas;
    }

    @Override
    public String toString() {
        return "ExperienceByTypeDTO{" +
                "beenAreas=" + beenAreas +
                ", wantAreas=" + wantAreas +
                '}';
    }
}
