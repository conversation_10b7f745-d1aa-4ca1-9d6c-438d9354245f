package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.*;

@NoArgsConstructor
@Getter
@Setter
@EqualsAndHashCode
@ToString
@JsonInclude(NON_NULL)
public class TodoListItemDTO {
    private Long id;
    private String name;
    private String imageUrl;
    private String imageBlurHash;
    private String isoCode;
    private List<Double> coordinate;
    private List<Long> listIds;
    private Integer popularity;
    private Long cityId;
}
