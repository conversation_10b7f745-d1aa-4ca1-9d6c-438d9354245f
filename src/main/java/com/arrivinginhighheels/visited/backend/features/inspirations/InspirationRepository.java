package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.Inspiration;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface InspirationRepository extends JpaRepository<Inspiration, Long> {

    @Query("select i.lastModificationTime from Inspiration i where i.id = ?1")
    Date getMaxModificationTimeOfInspirationById(Long id);

    @Query(value = "SELECT i FROM Inspiration i WHERE i.id NOT IN (SELECT u.inspiration.id FROM UserInspiration u WHERE u.user = ?1)")
    List<Inspiration> getAllUnselectedInspirations(User user);

    @Query(value = "SELECT i FROM Inspiration i WHERE i.id NOT IN (SELECT u.inspiration.id FROM UserInspiration u WHERE u.user = ?1)")
    List<Inspiration> getNextInspirations(User user, Pageable page);

    @Query(value = """
    SELECT i FROM Inspiration i
    WHERE (i.geoArea = ?2 or i.geoArea.parent = ?2)
      AND i.id NOT IN (SELECT u.inspiration.id FROM UserInspiration u WHERE u.user = ?1)
    """)
    List<Inspiration> getNextInspirationForUserAndArea(User user, GeoArea area, Pageable pageable);

    // Activate this when we want to get smarted about getting all the data in one database call
//    @Query(value = """
//    SELECT i, t, area FROM Inspiration i
//    INNER JOIN InspirationTranslation t on t.inspiration = i
//    INNER JOIN GeoAreaTranslation area on t.inspiration.geoArea = area
//    WHERE (i.geoArea = ?2 or i.geoArea.parent = ?2)
//      AND t.supportedLanguage = ?3
//      AND area.supportedLanguage = ?4
//      AND i.id NOT IN (SELECT u.inspiration.id FROM UserInspiration u WHERE u.user = ?1)
//    """)
//    List<Object[]> getNextInspirationForUserAndAreaAndLanguage(User user, GeoArea area, SupportedLanguage language, Pageable pageable);
}
