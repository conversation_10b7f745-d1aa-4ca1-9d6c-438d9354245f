package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;

import jakarta.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "todo_list_item_translations")
public class TodoListItemTranslation {

    @Id
    @SequenceGenerator(name = "list_item_translations_id_seq", sequenceName = "list_item_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "list_item_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private TodoListItem listItem;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public TodoListItemTranslation() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TodoListItem getListItem() {
        return listItem;
    }

    public void setListItem(TodoListItem listItem) {
        this.listItem = listItem;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoListItemTranslation that = (TodoListItemTranslation) o;
        return Objects.equals(id, that.id) && Objects.equals(listItem, that.listItem)
                && Objects.equals(supportedLanguage, that.supportedLanguage) && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, listItem, supportedLanguage, name);
    }

    @Override
    public String toString() {
        return "TodoListItemTranslation{" +
                "id=" + id +
                ", item=" + listItem +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
