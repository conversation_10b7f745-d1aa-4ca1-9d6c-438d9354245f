package com.arrivinginhighheels.visited.backend.features.privacy;

import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

public interface PrivacyAgreementRepository extends JpaRepository<PrivacyAgreement, Long> {

    PrivacyAgreement findByUser(User user);

    void deleteByUser(User user);

    @Modifying
    @Query(value = "DELETE from casl_agreements where user_id = ?1", nativeQuery = true)
    void deleteByUserId(Long userId);
}
