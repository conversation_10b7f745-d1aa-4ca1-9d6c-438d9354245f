package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.ExperienceTranslation;

import java.util.Objects;

public final class LocalizedExperiencePrep {
    private final Experience experience;
    private final ExperienceTranslation translation;

    public LocalizedExperiencePrep(
            Experience experience,
            ExperienceTranslation translation) {
        this.experience = experience;
        this.translation = translation;
    }

    public Experience experience() {
        return experience;
    }

    public ExperienceTranslation translation() {
        return translation;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (LocalizedExperiencePrep) obj;
        return Objects.equals(this.experience, that.experience) &&
                Objects.equals(this.translation, that.translation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(experience, translation);
    }

    @Override
    public String toString() {
        return "LocalizedExperiencePrep[" +
                "experience=" + experience + ", " +
                "translation=" + translation + ']';
    }

}
