package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.dto.SponsorDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@Setter
@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TodoListDTO {
    private Long id;
    private String name;
    private String thumbnailUrl;
    private String thumbnailBlurHash;
    private SponsorDTO sponsor;
    private Integer count;
    private TodoListType type;
    private TodoListStatus status;

    @Override
    public String toString() {
        return "TodoListDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", thumbnailBlurHash='" + thumbnailBlurHash + '\'' +
                ", sponsor=" + sponsor +
                ", count=" + count +
                ", type=" + type +
                ", status=" + status +
                '}';
    }
}
