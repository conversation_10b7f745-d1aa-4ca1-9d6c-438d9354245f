package com.arrivinginhighheels.visited.backend.features.emails;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.SneakyThrows;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.services.sesv2.SesV2Client;
import software.amazon.awssdk.services.sesv2.model.EmailContent;
import software.amazon.awssdk.services.sesv2.model.RawMessage;
import software.amazon.awssdk.services.sesv2.model.SendEmailRequest;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.EMAIL_URL;

@Service
public class EmailService {

    private static final String DEFAULT_EMAIL_ADDRESS = "<EMAIL>";

    private static final String VISITED_APP_API_SUBDOMAIN = "a";
    private static final String VISITED_APP_DOMAIN = "visitedapp.com";
    private static final String VISITED_DOT_APP_DOMAIN = "visited.app";

    private static final String ARRIVING_IN_HIGH_HEELS_DOMAIN_API_SUBDOMAIN = "visitedapi";
    private static final String ARRIVING_IN_HIGH_HEELS_DOMAIN = "arrivinginhighheels.com";

    private final UserRepository userRepository;
    private final TaskScheduler executor;
    private final TrackedLinkService trackedLinkService;
    private final SesV2Client secondarySesClient;
    private final YamlConfig config;
    private final MailerooSmtpAccountRepository mailerooSmtpAccountRepository;

    public EmailService(
            UserRepository userRepository,
            TaskScheduler executor,
            TrackedLinkService trackedLinkService,
            @Qualifier("secondarySesClient") SesV2Client secondarySesClient,
            JavaMailSender mailSender,
            YamlConfig config,
            MailerooSmtpAccountRepository mailerooSmtpAccountRepository) {
        this.userRepository = userRepository;
        this.executor = executor;
        this.trackedLinkService = trackedLinkService;
        this.secondarySesClient = secondarySesClient;
        this.config = config;
        this.mailerooSmtpAccountRepository = mailerooSmtpAccountRepository;
    }

    @SneakyThrows
    public Map<String, String> sendSimpleMessage(
            Optional<String> maybeFriendlyName,
            Optional<String> maybeFromAddress,
            String recipientEmail,
            String subject,
            String bodyText,
            boolean addUnsubscribeHeader
            ) {


        var friendlyName = maybeFriendlyName.orElse("Visited App");
        var sender = maybeFromAddress.orElse(DEFAULT_EMAIL_ADDRESS);

        if (!(sender.contains(ARRIVING_IN_HIGH_HEELS_DOMAIN) || sender.contains(VISITED_APP_DOMAIN) || sender.contains(VISITED_DOT_APP_DOMAIN))) {
            throw new RuntimeException("Invalid email address: " + sender);
        }

        final var unsubscribeUrl = createUnsubscribeUrl(recipientEmail, sender);

        if (sender.contains(VISITED_DOT_APP_DOMAIN) || sender.contains(VISITED_APP_DOMAIN)) {
            sendViaMaileroo(friendlyName, recipientEmail, subject, bodyText, addUnsubscribeHeader, sender, unsubscribeUrl);
            return Map.of("success", "true");
        }


        return sendViaSES(recipientEmail, subject, bodyText, addUnsubscribeHeader, friendlyName, sender, unsubscribeUrl);
    }

    private Map<String, String> sendViaSES(String recipientEmail, String subject, String bodyText, boolean addUnsubscribeHeader, String friendlyName, String sender, String unsubscribeUrl) {
        var rawEmailContent = createRawEmailContent(
                friendlyName,
                recipientEmail,
                subject,
                bodyText,
                addUnsubscribeHeader,
                sender,
                unsubscribeUrl
        );

        var content = EmailContent.builder()
                .raw(RawMessage.builder()
                        .data(SdkBytes.fromByteArray(rawEmailContent.getBytes(StandardCharsets.UTF_8)))
                        .build())
                .build();

        var requestBuilder = SendEmailRequest.builder()
                .content(content);

        var sendRequest = requestBuilder.build();


        var response = secondarySesClient.sendEmail(sendRequest);
        return Map.of("id", response.messageId());
    }

    private String createRawEmailContent(
            String friendlyName,
            String recipientEmail,
            String subject,
            String bodyText,
            boolean addUnsubscribeHeader,
            String sender,
            String unsubscribeUrl
    ) {
        bodyText =  bodyText
            .replaceAll("%%email_address%%", recipientEmail)
            .replaceAll("%%unsubscribe_link%%", unsubscribeUrl);

        var messageId = "<" + UUID.randomUUID() + "@" + sender.split("@")[1] + ">";


        var rawEmailContent = "From: " + "\"" + friendlyName + "\" <" + sender + "> \r\n" +
                "Return-Path: <" + sender + "> \r\n" +
                "To: " + recipientEmail + "\r\n" +
                "Message-ID: " + messageId + "\r\n" +
                "Subject: " + subject + "\r\n";

        if (addUnsubscribeHeader) {
            rawEmailContent = rawEmailContent +
                    "List-Unsubscribe: <mailto:" + sender + "?subject=unsubscribe>\r\n, <" + unsubscribeUrl + ">\r\n";
        }

        rawEmailContent = rawEmailContent +
                "MIME-Version: 1.0\r\n" +
                "Content-Type: multipart/alternative; boundary=\"boundary-string\"\r\n" +
                "\r\n" +
                "--boundary-string\r\n" +
                "Content-Type: text/html; charset=UTF-8\r\n" +
                "Content-Transfer-Encoding: 7bit\r\n" +
                "\r\n" +
                bodyText + "\r\n" +
                "\r\n" +
                "--boundary-string--";
        return rawEmailContent;
    }

    @SneakyThrows
    @Async
    public void sendViaMaileroo(
            String friendlyName,
            String recipientEmail,
            String subject,
            String bodyText,
            boolean addUnsubscribeHeader,
            String sender,
            String unsubscribeUrl
    ) {
        var htmlText =  bodyText
                .replaceAll("%%email_address%%", recipientEmail)
                .replaceAll("%%unsubscribe_link%%", unsubscribeUrl);

        var account = mailerooSmtpAccountRepository.findByAddress(sender);
        if (account == null) {
            throw new RuntimeException("No maileroo account found for " + sender);
        }

        var javaMailSender = createJavaMailSender(sender, account.getPassword());
        var message = javaMailSender.createMimeMessage();

        if (addUnsubscribeHeader) {
            configureListUnsubscribeHeader(sender, unsubscribeUrl, message);
        }

        var messageId = "<" + UUID.randomUUID() + "@" + sender.split("@")[1] + ">";
        message.setHeader("Message-ID", messageId);


        var helper = new MimeMessageHelper(message, true, "UTF-8");
        helper.setFrom(sender, friendlyName);
        helper.setTo(recipientEmail);
        helper.setSubject(subject);
        helper.setText(htmlText, true);
        javaMailSender.send(message);
    }

    private static void configureListUnsubscribeHeader(String sender, String unsubscribeUrl, MimeMessage message) throws MessagingException {
        String unsubscribeHost;
        if (sender.contains(VISITED_APP_DOMAIN)) {
            unsubscribeHost = VISITED_APP_DOMAIN;
        } else if (sender.contains(VISITED_DOT_APP_DOMAIN)) {
            unsubscribeHost = VISITED_DOT_APP_DOMAIN;
        } else {
            throw new RuntimeException("Unsupported email address: " + sender);
        }

        message.addHeader(
                "List-Unsubscribe-Post",
                "List-Unsubscribe=One-Click");

        message.addHeader(
                "List-Unsubscribe",
                "<" + unsubscribeUrl + ">, <mailto:unsubscribe@" + unsubscribeHost + "?subject=unsubscribe>");
    }

    private JavaMailSender createJavaMailSender(String username, String password) {
        final var mailSender = new JavaMailSenderImpl();
        mailSender.setHost(config.getEmailHost());
        mailSender.setPort(config.getEmailPort());
        mailSender.setUsername(username);
        mailSender.setPassword(password);

        final var properties = mailSender.getJavaMailProperties();
        properties.put("mail.transport.protocol", "smtp");
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.starttls.enable", "true");
        properties.put("mail.debug", "true");

        return mailSender;
    }

    private String createUnsubscribeUrl(String recipientEmail, String sender) {
        final var builder = new StringBuilder();
        builder.append("https://");

        if (sender.contains(ARRIVING_IN_HIGH_HEELS_DOMAIN)) {
            builder.append(ARRIVING_IN_HIGH_HEELS_DOMAIN_API_SUBDOMAIN);
            builder.append(".");
            builder.append(ARRIVING_IN_HIGH_HEELS_DOMAIN);
        } else if (sender.contains(VISITED_DOT_APP_DOMAIN)) {
            builder.append("api");
            builder.append(".");
            builder.append(VISITED_DOT_APP_DOMAIN);
        } else {
            builder.append(VISITED_APP_API_SUBDOMAIN);
            builder.append(".");
            builder.append(VISITED_APP_DOMAIN);
        }

        builder.append(EMAIL_URL);
        builder.append("/unsubscribe?email=");
        builder.append(recipientEmail);

        return builder.toString();
    }

    @Async
    public void sendBulkMailingList(SendBulkDTO request) {
        if (request.shouldSendImmediately()) {
            runBulkEmailProcess(request);
            return;
        }

        final var scheduledTime = request.scheduledDateTime().orElseThrow();
        var isoFormat = DateTimeFormatter.ISO_DATE_TIME;
        var localDateTime = LocalDateTime.parse(scheduledTime, isoFormat);
        var startInstant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
        executor.schedule(() -> runBulkEmailProcess(request), startInstant);
    }

    private void runBulkEmailProcess(SendBulkDTO request) {
        var executorService = Executors.newFixedThreadPool(1);

        executorService.submit(() -> {
            final var mailingList = request.customList();

            for (var destination : mailingList) {
                try {
                    sendSimpleMessage(
                            Optional.of(request.friendlyName()),
                            Optional.of(request.fromAddress()),
                            destination,
                            request.subject(),
                            request.htmlTemplate(),
                            true);
                } catch (Exception e) {
                    Logger.getGlobal().log(Level.SEVERE, e.toString());
                }
            }
        });
        executorService.shutdown();
    }

    public Optional<String> createTrackingLink(String url, Optional<String> title) {
        var existingLink = trackedLinkService.findLink(url);
        if (existingLink.isPresent()) {
            return existingLink.map(TrackedLink::getHash);
        }

        var newUrl = createNewTrackedLink(url, title);
        return newUrl.map(TrackedLink::getHash);
    }

    private Optional<TrackedLink> createNewTrackedLink(String url, Optional<String> title) {
        var actualTitle =  title.orElseGet(() -> fetchLinkTitle(url));
        return trackedLinkService.createNewTrackedLink(url, actualTitle);
    }


    @SneakyThrows
    private String fetchLinkTitle(String link) {
        try (var client = HttpClients.createDefault()) {
            var httpget = new HttpGet(link);
            System.out.println("Executing request " + httpget.getRequestLine());
            var handler = new ResponseHandler<String>() {
                @Override
                public String handleResponse(HttpResponse response) throws ClientProtocolException, IOException {
                    int status = response.getStatusLine().getStatusCode();
                    if (status >= 200 && status < 300) {
                        HttpEntity entity = response.getEntity();
                        return entity != null ? EntityUtils.toString(entity) : null;
                    } else {
                        throw new ClientProtocolException("Unexpected response status: " + status);
                    }
                }
            };

            String response = client.execute(httpget, handler);
            final var doc = Jsoup.parse(response);
            return Objects.requireNonNull(doc.getElementsByTag("title").first()).text();

        } finally {
            ;
        }
    }

    public boolean unsubscribeUser(String email) {
        return setUnsubscribe(email, true);
    }

    public boolean resubscribeUser(String email) {
        return setUnsubscribe(email, false);
    }


    private boolean setUnsubscribe(String email, boolean unsubscribe) {
        if (email.isEmpty()) {
            return false;
        }

        var maybeUser = userRepository.findByUsername(email);
        if (maybeUser.isEmpty()) {
            return false;
        }

        var user = maybeUser.get();
        user.setUnsubscribed(unsubscribe);
        userRepository.save(user);
        return true;
    }
}


