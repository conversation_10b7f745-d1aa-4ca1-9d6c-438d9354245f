package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.City;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.lang.Nullable;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

@Entity
@Getter
@Setter
@Table(name = "todo_list_items")
public class TodoListItem {
    @Id
    @SequenceGenerator(name = "list_items_id_seq", sequenceName = "list_items_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "list_items_id_seq")
    private Long id;

    @NotNull
    @Column(length = 128, nullable = false)
    private String name;

    @OneToMany
    @JoinTable(name = "todo_lists_xref", inverseJoinColumns = @JoinColumn(name = "todo_list_id"), joinColumns = @JoinColumn(name = "todo_list_item_id"))
    @ToString.Exclude
    private Set<TodoList> lists;

    @ManyToOne(fetch = FetchType.LAZY)
    @Nullable
    @ToString.Exclude
    private GeoArea geoArea;

    @OneToOne(fetch = FetchType.LAZY)
    @Nullable
    private City city;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    @Column(name = "long", precision = 19, scale = 10)
    private BigDecimal longitude;

    @Column(name = "lat", precision = 19, scale = 10)
    private BigDecimal latitude;

    public TodoListItem(
            Long id,
            String name,
            @Nullable GeoArea geoArea,
            @Nullable City city,
            Date lastModificationTime,
            BigDecimal longitude,
            BigDecimal latitude) {
        this.id = id;
        this.name = name;
        this.geoArea = geoArea;
        this.city = city;
        this.lastModificationTime = lastModificationTime;
        this.longitude = longitude;
        this.latitude = latitude;
    }

    public TodoListItem() {
    }

    @Nullable
    public GeoArea getGeoArea() {
        return geoArea;
    }

    public void setGeoArea(@Nullable GeoArea geoArea) {
        this.geoArea = geoArea;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoListItem that = (TodoListItem) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(geoArea, that.geoArea)
                && Objects.equals(lastModificationTime, that.lastModificationTime)
                && Objects.equals(longitude, that.longitude) && Objects.equals(latitude, that.latitude);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, geoArea, lastModificationTime, longitude, latitude);
    }

    @Override
    public String toString() {
        return "TodoListItem{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", geoArea=" + geoArea +
                ", lastModificationTime=" + lastModificationTime +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                '}';
    }
}
