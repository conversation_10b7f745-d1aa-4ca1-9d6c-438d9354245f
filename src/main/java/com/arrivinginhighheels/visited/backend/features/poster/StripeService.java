package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.Currency;
import com.arrivinginhighheels.visited.backend.dto.PaymentIntentDto;
import com.arrivinginhighheels.visited.backend.dto.PosterRequest;
import com.arrivinginhighheels.visited.backend.model.GeoAreaCurrency;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.utils.HttpRequestUtils;
import com.google.gson.JsonSyntaxException;
import com.stripe.Stripe;
import com.stripe.exception.SignatureVerificationException;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.net.ApiResource;
import com.stripe.net.Webhook;
import com.stripe.param.*;
import com.stripe.param.PaymentIntentCreateParams.Shipping;
import io.sentry.Sentry;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class StripeService {
    private static final long EUROPE_ID = 4;

    public StripeService(
            YamlConfig config,
            GeoAreaCurrencyRepository currencyRepository,
            GeoAreaRepository areaRepository,
            PrintingService printingService,
            CountryLookupService countryLookupService,
            HttpRequestUtils httpRequestUtils) {
        this.config = config;
        this.httpRequestUtils = httpRequestUtils;
        Stripe.apiKey = config.getStripeSecretKey();

        this.currencyRepository = currencyRepository;
        this.areaRepository = areaRepository;
        this.printingService = printingService;
        this.countryLookupService = countryLookupService;

    }

    private final GeoAreaCurrencyRepository currencyRepository;
    private final GeoAreaRepository areaRepository;
    private final YamlConfig config;
    private final PrintingService printingService;
    private final CountryLookupService countryLookupService;
    private final HttpRequestUtils httpRequestUtils;

    @SneakyThrows
    public PaymentIntentDto buyPoster(User user, PosterRequest request) {
        final var username = user.getUsername();
        final var customer = getOrCreateCustomer(username);

        final var payment = request.getPayment();
        final var ephemeralKeys = EphemeralKey.create(
                EphemeralKeyCreateParams.builder()
                        .setStripeVersion(payment.getClientSdkVersion())
                        .setCustomer(customer.getId())
                        .build()

        );

        final var paymentIntent = buildPaymentIntent(request, customer);

        printingService.submitOrder(user, paymentIntent.getId(), request.getPoster());

        final var dto = new PaymentIntentDto();
        dto.setCustomerId(customer.getId());
        dto.setPaymentIntent(paymentIntent.getClientSecret());
        dto.setEphemeralKey(ephemeralKeys.getSecret());

        return dto;
    }

    private PaymentIntent buildPaymentIntent(
            PosterRequest request,
            Customer customer
    ) throws StripeException {
        var payment = request.getPayment();
        var price = Price.retrieve(payment.getPriceId());

        var currency = Currency.valueOf(price.getCurrency().toLowerCase());


        var shippingRate = fetchAllShippingRates()
                .stream()
                .filter((rate) -> rate.getId().equals(payment.getShippingId()))
                .findFirst();

        if (shippingRate.isEmpty()) {
            throw new RuntimeException("Invalid Shipping Id " + payment.getShippingId());
        }

        var amount = price.getUnitAmount();

        var couponId = payment.getCouponId();
        Optional<Coupon> maybeCoupon = Optional.empty();
        if (couponId != null) {
            var coupon = Coupon.retrieve(couponId);
            if (coupon.getValid()) {
                maybeCoupon = Optional.of(coupon);
                final var discount = buildDiscount(price, coupon);
                amount -= discount.getUnitAmount();
            }
        }

        amount += shippingRate.get().getFixedAmount().getAmount();

        if (currency == Currency.cad) {
            amount += calculateCanadianTax(amount);
        }

        final var paymentParameters = PaymentIntentCreateParams
                .builder()
                .setCustomer(customer.getId())
                .setAmount(amount)
                .setCurrency(currency.name())
                .setAutomaticPaymentMethods(
                        PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                                .setEnabled(true)
                                .build())
                .setShipping(buildShippingAddress(request));

        maybeCoupon.ifPresent(coupon ->
                paymentParameters.putMetadata("coupon_applied", coupon.getId()));

        return PaymentIntent.create(paymentParameters.build());
    }

    @SneakyThrows
    public ProductPurchasingInformation getProductInformation(HttpServletRequest request) {
        var product = fetchProduct();
        var prices = Price
                .list(PriceListParams
                        .builder()
                        .setProduct(product.getId())
                        .build())
                .getData();


        var dto = new ProductPurchasingInformation();
        dto.setId(product.getId());
        dto.setName(product.getName());
        dto.setImageUrl(product.getImages().stream().findFirst().orElseThrow());

        var ipAddress = httpRequestUtils.findIpAddress(request);
        var currency = findCurrencyWithIpAddress(ipAddress);

        var preferredPrice = findPrice(prices, currency);
        dto.setPrice(buildPriceDto(preferredPrice));

        if (currency != Currency.cad) {
            var usd = findPrice(prices, Currency.usd);
            dto.setUsdAlternativePrice(buildPriceDto(usd));

            var euro = findPrice(prices, Currency.eur);
            dto.setEurAlternativePrice(buildPriceDto(euro));
        }

        var shippingRates = fetchAllShippingRates();

        var shipping = findShippingRate(shippingRates, currency);
        dto.setShippingOptions(buildShippingDtos(shipping));

        if (currency != Currency.cad) {
            var usdShipping = findShippingRate(shippingRates, Currency.usd);
            dto.setUsdAlternativeShippingOptions(buildShippingDtos(usdShipping));
            var euroShipping = findShippingRate(shippingRates, Currency.eur);
            dto.setEurAlternativePriceShippingOptions(buildShippingDtos(euroShipping));
        }

        if (currency == Currency.cad) {
            addCanadianTax(dto, preferredPrice);
        }

        return dto;
    }

    private List<ShippingRate> fetchAllShippingRates() throws StripeException {
        return ShippingRate.list(new ShippingRateListParams
                .Builder()
                .setLimit(20L)
                .setActive(true)
                .build()
            ).getData();
    }

    private void addCanadianTax(ProductPurchasingInformation dto, Price preferredPrice) throws StripeException {
        var percentage = canadianTaxPercentage();
        var baseTax = preferredPrice.getUnitAmountDecimal().movePointLeft(2).multiply(percentage);
        dto.getPrice().setTax(baseTax);
        for (var shippingDto : dto.getShippingOptions()) {
            var shippingDecimal = shippingDto.getUnitAmountDecimal();
            shippingDto.setTax(shippingDecimal.multiply(percentage));
        }
    }

    private long calculateCanadianTax(long subtotal) throws StripeException {
        var decimal = formattedRawPrice(new BigDecimal(subtotal), Currency.cad);
        var percentage = canadianTaxPercentage();
        var total = decimal.multiply(percentage);
        return total.movePointRight(2).longValue();
    }

    private BigDecimal canadianTaxPercentage() throws StripeException {
        var taxRate = TaxRate.retrieve(config.getStripeCanadianTaxId());
        return formattedRawPrice(taxRate.getPercentage(), Currency.cad);
    }

    private BigDecimal formattedRawPrice(BigDecimal price, Currency currency) {
        if (currency == Currency.jpy || currency == Currency.krw) {
            return price;
        }

        return price.movePointLeft(2);
    }

    private Price findPrice(List<Price> prices, Currency currency) {
        return prices
                .stream()
                .filter((e) -> e.getCurrency().equalsIgnoreCase(currency.name()))
                .findFirst()
                .orElseThrow();
    }

    private List<ShippingRateDto> buildShippingDtos(List<ShippingRate> shippingRates) {
        return shippingRates
                .stream()
                .map((e) -> {
                    var dto = new ShippingRateDto();
                    dto.setId(e.getId());
                    var fixedAmount = e.getFixedAmount();

                    var unitAmount = fixedAmount.getAmount();
                    var currency = fixedAmount.getCurrency();
                    dto.setCurrencyCode(currency);
                    dto.setUnitAmount(unitAmount);
                    dto.setUnitAmountDecimal(formattedRawPrice(new BigDecimal(unitAmount), Currency.valueOf(currency)));

                    var international = e.getMetadata().getOrDefault("international", "false");
                    dto.setInternationalShipping(international.equals("true"));
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private PriceDto buildPriceDto(Price price) {
        var dto = new PriceDto();
        dto.setId(price.getId());
        dto.setCurrencyCode(price.getCurrency());
        dto.setUnitAmount(price.getUnitAmount());
        dto.setUnitAmountDecimal(formattedRawPrice(price.getUnitAmountDecimal(), Currency.valueOf(price.getCurrency())));
        return dto;
    }

    private List<ShippingRate> findShippingRate(List<ShippingRate> shippingRates, Currency currency) {
        return shippingRates
                .stream()
                .filter((e) -> e.getFixedAmount().getCurrency().equalsIgnoreCase(currency.name()) && e.getActive())
                .collect(Collectors.toList());
    }

    private Product fetchProduct() throws StripeException {
        return Product.list(ProductListParams
                        .builder()
                        .setLimit(1L)
                        .build()).getData()
                .stream()
                .findFirst()
                .orElseThrow();
    }

    @SneakyThrows
    public CouponDto validateCoupon(CouponRequest couponRequest)  {
        final var promoCodeRequest = PromotionCodeListParams
                .builder()
                .setCode(couponRequest.couponCode())
                .build();

        final var codes = PromotionCode.list(promoCodeRequest).getData();
        if (codes.isEmpty()) {
            throw new RuntimeException("Invalid or expired coupon code.");
        }

        final var promo = codes.get(0);
        final var coupon = promo.getCoupon();

        if (!coupon.getValid()) {
            throw new RuntimeException("Invalid or expired coupon code.");
        }

        final var price = Price.retrieve(couponRequest.priceId());
        var dto = buildDiscount(price, coupon);

        return CouponDto.builder()
                .id(coupon.getId())
                .name(coupon.getName())
                .code(couponRequest.couponCode())
                .amountOff(coupon.getAmountOff())
                .percentOff(coupon.getPercentOff())
                .discount(dto)
                .build();
    }

    private PriceDto buildDiscount(Price price, Coupon coupon) throws StripeException {
        var dto = new PriceDto();
        dto.setId(price.getId());
        dto.setCurrencyCode(price.getCurrency());


        BigDecimal discount = BigDecimal.ZERO;
        if (coupon.getPercentOff() != null) {
            discount = price
                    .getUnitAmountDecimal()
                    .multiply(coupon.getPercentOff().movePointLeft(2));
        } else if (coupon.getAmountOff() != null) {
            discount = BigDecimal.valueOf(coupon.getAmountOff());
        }

        final var currency = Currency.valueOf(price.getCurrency().toLowerCase());
        dto.setUnitAmount(discount.longValue());
        dto.setUnitAmountDecimal( formattedRawPrice(discount, currency));

        if (currency == Currency.cad) {
            var percentage = canadianTaxPercentage();
            final var discountedTax = discount.multiply(percentage);
            dto.setTax(formattedRawPrice(discountedTax, currency));
        }
        return dto;
    }

    private Shipping buildShippingAddress(PosterRequest request) {
        var builder = Shipping.builder();
        var addressDto = request.getPoster().getShippingAddress();

        builder.setName(addressDto.getFullName());

        var address = Shipping.Address
                .builder()
                .setCity(addressDto.getCity())
                .setCountry(addressDto.getCountryCode())
                .setLine1(addressDto.getStreetLine1())
                .setLine2(addressDto.getStreetLine2())
                .setPostalCode(addressDto.getPostalCode())
                .setState(addressDto.getState())
                .build();

        builder.setAddress(address);
        return builder.build();
    }


    private Customer getOrCreateCustomer(String username) throws StripeException {
        var maybeCustomer = Customer
                .list(CustomerListParams
                        .builder()
                        .setEmail(username)
                        .build())
                .getData()
                .stream()
                .findFirst();

        return maybeCustomer.orElseGet(() -> {
            try {
                return Customer
                        .create(CustomerCreateParams
                                .builder()
                                .setEmail(username)
                                .build());
            } catch (StripeException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public Map<String, String> getCurrencyDebug(HttpServletRequest request) {
        var ipAddress = httpRequestUtils.findIpAddress(request);
        String countryCode;
        try {
            countryCode = countryLookupService.findCountry(ipAddress);
            var maybeCurrency = findGeoAreaCurrencyWithCountryCode(countryCode);
            if (maybeCurrency.isPresent()) {
                var currency = maybeCurrency.get();
                return Map.of(
                        "status","success",
                        "ip", ipAddress,
                        "countryCode", countryCode,
                        "country", currency.getGeoArea().getName(),
                        "currency", currency.getCurrency().name());
            } else {
                return Map.of(
                        "status","failed",
                        "ip", ipAddress,
                        "countryCode", countryCode,
                        "country", "not found",
                        "error", "cannot find a GeoArea with an isoCode of " + countryCode);
            }
        } catch (Exception e) {
            return Map.of(
                    "status","failed",
                    "ip", ipAddress,
                    "countryCode", "Not Found",
                    "error", e.getLocalizedMessage());

        }
    }

    private Currency findCurrencyWithIpAddress(String ipAddress) {
        try {
            var countryCode = countryLookupService.findCountry(ipAddress);
            return findCurrencyWithCountryCode(countryCode).orElse(Currency.usd);
        } catch (Exception e) {
            Sentry.captureException(e);
            return Currency.usd;
        }
    }

    private Optional<GeoAreaCurrency> findGeoAreaCurrencyWithCountryCode(String countryCode) {
        var maybeArea = areaRepository.findFirstByIsoKeyContainingIgnoreCaseAndParentIsNull(countryCode);

        if (maybeArea.isEmpty()) {
            return Optional.empty();
        }

        var area = maybeArea.get();
        return currencyRepository.findByGeoArea(area);
    }

    private Optional<Currency> findCurrencyWithCountryCode(String countryCode) {
        var maybeArea = areaRepository.findFirstByIsoKeyContainingIgnoreCaseAndParentIsNull(countryCode);

        if (maybeArea.isEmpty()) {
            return Optional.empty();
        }

        var area = maybeArea.get();
        var areaCurrency =  currencyRepository.findByGeoArea(area);

        if (areaCurrency.isEmpty()) {
            // Try to find the currency by region
            var region = area.getContinents();
            if (region == null || region.isEmpty()) {
                return Optional.empty();
            }

            var defaultRegion = region.get(0);

            // If the country is in Europe, assume Euro as the default
            if (defaultRegion.getId() == EUROPE_ID) {
                return Optional.of(Currency.eur);
            }

            return Optional.empty();
        }

        return Optional.ofNullable(areaCurrency.get().getCurrency());
    }

    public ResponseEntity processWebhook(String stripeSignature, String payload) {
        Event event;
        try {
            event = ApiResource.GSON.fromJson(payload, Event.class);
            //Webhook.constructEvent(data, stripeSignature, config.getStripeWebhook());
        } catch (JsonSyntaxException e) {
            return ResponseEntity.status(400).body(Map.of("error", e.getLocalizedMessage()));
        }

        // Only verify the event if you have an endpoint secret defined.
        // Otherwise, use the basic event deserialized with GSON.
        try {
            event = Webhook.constructEvent(payload, stripeSignature, config.getStripeWebhook());
        } catch (SignatureVerificationException e) {
            // Invalid signature
            System.out.println("⚠️  Webhook error while validating signature.");
            return ResponseEntity.status(400).body(Map.of("error", e.getLocalizedMessage()));
        }


        final var stripeObject = getStripeObject(event);

        return switch (event.getType()) {
            case "charge.succeeded" -> ResponseEntity.ok().build();
            case "payment_intent.succeeded" -> processSuccessfulPayment(stripeObject);
            case "payment_intent.created" -> ResponseEntity.ok().build();
            case "payment_intent.payment_failed", "payment_intent.failed" -> updatePaymentStatus(stripeObject, PaymentStatus.failed);
            case "payment_intent.rejected" -> updatePaymentStatus(stripeObject, PaymentStatus.rejected);
            case "payment_intent.requires_action" -> updatePaymentStatus(stripeObject, PaymentStatus.requiresAction);
            case "payment_intent.canceled" -> updatePaymentStatus(stripeObject, PaymentStatus.cancelled);
            default -> throw new RuntimeException("unsupported stripe webhook event " + event.getType());
        };
    }

    private static StripeObject getStripeObject(Event event) {
        var dataObjectDeserializer = event.getDataObjectDeserializer();
        StripeObject stripeObject;
        if (dataObjectDeserializer.getObject().isPresent()) {
            stripeObject = dataObjectDeserializer.getObject().get();
        } else {
            throw new RuntimeException("Cannot deserialize Stripe Object");
            // Deserialization failed, probably due to an API version mismatch.
            // Refer to the Javadoc documentation on `EventDataObjectDeserializer` for
            // instructions on how to handle this case, or return an error here.
        }
        return stripeObject;
    }

    private ResponseEntity updatePaymentStatus(StripeObject stripeObject, PaymentStatus status) {
        var paymentIntent = (PaymentIntent) stripeObject;
        printingService.updateOrderStatus(paymentIntent.getId(), status);
        return ResponseEntity.ok().build();
    }

    private ResponseEntity processSuccessfulPayment(StripeObject stripeObject) {
        var paymentIntent = (PaymentIntent)stripeObject;
        var chargeId = paymentIntent.getLatestCharge();
        printingService.confirmPayment(paymentIntent.getId(), chargeId);
        return ResponseEntity.ok().build();
    }
}
