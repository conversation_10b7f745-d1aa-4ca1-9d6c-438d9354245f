package com.arrivinginhighheels.visited.backend.features.inspirations;

import java.util.List;

public class InspirationPageDTO {
    private int page;
    private int max;
    private List<InspirationDTO> inspirations;

    public InspirationPageDTO(int page, int max, List<InspirationDTO> inspirations) {
        this.page = page;
        this.max = max;
        this.inspirations = inspirations;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getMax() {
        return max;
    }

    public void setMax(int max) {
        this.max = max;
    }

    public List<InspirationDTO> getInspirations() {
        return inspirations;
    }

    public void setInspirations(List<InspirationDTO> inspirations) {
        this.inspirations = inspirations;
    }
}
