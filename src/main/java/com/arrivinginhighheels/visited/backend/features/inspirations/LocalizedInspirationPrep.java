package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.model.GeoAreaTranslation;
import com.arrivinginhighheels.visited.backend.model.Inspiration;
import com.arrivinginhighheels.visited.backend.model.InspirationTranslation;

import java.util.Objects;

public final class LocalizedInspirationPrep {
    private final Inspiration inspiration;
    private final InspirationTranslation translation;
    private final GeoAreaTranslation areaTranslation;

    public LocalizedInspirationPrep(
            Inspiration inspiration,
            InspirationTranslation translation,
            GeoAreaTranslation areaTranslation) {
        this.inspiration = inspiration;
        this.translation = translation;
        this.areaTranslation = areaTranslation;
    }

    public Inspiration inspiration() {
        return inspiration;
    }

    public InspirationTranslation translation() {
        return translation;
    }

    public GeoAreaTranslation areaTranslation() {
        return areaTranslation;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (LocalizedInspirationPrep) obj;
        return Objects.equals(this.inspiration, that.inspiration) &&
                Objects.equals(this.translation, that.translation) &&
                Objects.equals(this.areaTranslation, that.areaTranslation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(inspiration, translation, areaTranslation);
    }

    @Override
    public String toString() {
        return "LocalizedInspirationPrep[" +
                "inspiration=" + inspiration + ", " +
                "translation=" + translation + ", " +
                "areaTranslation=" + areaTranslation + ']';
    }

}
