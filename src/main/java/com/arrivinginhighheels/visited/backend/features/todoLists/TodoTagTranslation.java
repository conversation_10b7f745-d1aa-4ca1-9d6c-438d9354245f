package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Setter
@Getter
@Entity
@Table(name = "todo_list_tags_translations")
public class TodoTagTranslation {
    @Id
    private Long id;

    @ManyToOne(optional = false)
    TodoTag tag;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public TodoTagTranslation() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoTagTranslation that = (TodoTagTranslation) o;
        return Objects.equals(id, that.id) && Objects.equals(tag, that.tag)
                && Objects.equals(supportedLanguage, that.supportedLanguage) && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, tag, supportedLanguage, name);
    }

    @Override
    public String toString() {
        return "TodoTagTranslation{" +
                "id=" + id +
                ", tag=" + tag +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
