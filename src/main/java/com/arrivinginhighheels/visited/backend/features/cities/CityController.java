package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Date;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.CITIES_URL;

@RestController
@RequestMapping(path = CITIES_URL)
public class CityController {

    private final ResponseHelper responseHelper;
    private final CityService cityService;
    private final LoggedInUserUtil userUtil;

    public CityController(
            ResponseHelper responseHelper,
            CityService cityService,
            LoggedInUserUtil userUtil) {
        this.responseHelper = responseHelper;
        this.cityService = cityService;
        this.userUtil = userUtil;
    }

    @GetMapping(path = "/areas/{areaIsoCode}", produces = "application/json")
    public ResponseEntity<List<CityDto>> getCity(final HttpServletRequest request,
            @PathVariable final String areaIsoCode) {
        final Date lastModificationTime = cityService.getMaxModificationTimeOfCitiesByIsoCode(areaIsoCode);
        return responseHelper.wrap(
                request,
                lastModificationTime,
                ResponseHelper.standardCacheLength,
                (r) -> cityService.fetchCitiesForIsoCode(areaIsoCode));
    }

    @GetMapping(path = "/search", produces = "application/json")
    public ResponseEntity<List<CityDto>> searchCity(final HttpServletRequest request,
            @RequestParam final String query) {
        final var results = cityService.searchForCity(query);
        return responseHelper.standardCacheableResponse(results);
    }

    @GetMapping(path = "/id/{cityId}", produces = "application/json")
    public ResponseEntity<CityDto> getCity(@PathVariable final Long cityId) {
        var city = cityService.fetchCityDtoById(cityId);
        return responseHelper.standardCacheableResponse(city);
    }

    @GetMapping(path = "/live", produces = "application/json")
    public ResponseEntity<CityDto> getLivedCity(final HttpServletRequest request) {
        final User user = userUtil.getLoggedInUser(request);
        var city = cityService.fetchLivedCity(user);
        if (city.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        return responseHelper.doNotCacheResponse(city.get());
    }

    @GetMapping(path = "/select", produces = "application/json")
    public ResponseEntity<UserCitiesDTO> getCitySelections(final HttpServletRequest request) {
        final User user = userUtil.getLoggedInUser(request);
        var cities = cityService.getCitiesForUser(user);
        return responseHelper.doNotCacheResponse(cities);
    }

    @PostMapping(path = "/select", produces = "application/json")
    public UserCitiesUpdatedDTO selectCity(
            final HttpServletRequest request,
            @Valid @RequestBody final UserCitySelectionDTO selection) {
        final User user = userUtil.getLoggedInUser(request);
        return cityService.handleSelection(selection, user);
    }

    @PostMapping(path = "/select/batch", produces = "application/json")
    public UserCitiesUpdatedDTO getLivedCity(
            final HttpServletRequest request,
            @Valid @RequestBody final UserCitiesBatchSelectDTO selections) {
        final User user = userUtil.getLoggedInUser(request);
        return cityService.handleBatchSelection(user, selections);
    }

}
