package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface ExperienceRepository extends JpaRepository<Experience, Long> {
    @Query( "select ega.experience " +
            "  from ExperienceGeoArea ega " +
            " where ega.experience.id = ?1 AND ega.geoArea.isoKey = ?2")
    Experience findByExperienceIdAndAreaIsoCode(Long experienceId, String areaIsoCode);

    @Query( "select ega.geoArea.isoKey " +
            "  from ExperienceGeoArea ega " +
            " where ega.experience.id = ?1")
    List<String> findAllByExperienceId(Long experienceId);

    @Query( "select ega.experience " +
            "  from ExperienceGeoArea ega " +
            " where ega.geoArea.isoKey = ?1")
    List<Experience> getAllExperiencesRelatedToAnAreaByTheAreasIsoCode(String areaIsoCode);

    @Query( "select count(ega) " +
            "  from ExperienceGeoArea ega " +
            " where ega.geoArea = ?1")
    Integer countExperiencesRelatedToAnArea(GeoArea area);

    @Query( "select max(ega.lastModificationTime) " +
            "     from ExperienceGeoArea ega " +
            "    where ega.geoArea.isoKey = ?1")
    Date getMaxModificationTimeOfExperienceGeoAreaRelationByTheGeoAreasIsoCode(String areaIsoCode);

    @Query( "select max(e.lastModificationTime) from Experience e")
    Date getMaxModificationTime();

    @Query( "select max(ega.lastModificationTime) " +
            "     from ExperienceGeoArea ega " +
            "    where ega.experience.id = ?1")
    Date getMaxModificationTimeOfExperienceById(Long experienceId);
}
