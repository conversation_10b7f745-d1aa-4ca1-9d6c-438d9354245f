package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.features.emails.EmailService;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchase;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.SessionRepository;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import com.arrivinginhighheels.visited.backend.security.JwtTokenUtil;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponse;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * Service responsible for the login behavior of the system
 */
@Service
public class LoginService {

    private final Random random = new Random();

    private final AuthenticationManager authenticationManager;

    private final JwtTokenUtil jwtTokenUtil;

    private final UserDetailsService userDetailsService;

    private final UserBuilder userBuilder;

    private final SessionRepository sessionRepository;

    private final SelectionService selectionService;

    private final UserPurchasesRepository userPurchasesRepository;

    private final UserRepository userRepository;

    private final EmailService emailService;

    public LoginService(
            AuthenticationManager authenticationManager,
            JwtTokenUtil jwtTokenUtil,
            UserDetailsService userDetailsService,
            UserBuilder userBuilder,
            SessionRepository sessionRepository,
            SelectionService selectionService,
            UserPurchasesRepository userPurchasesRepository,
            UserRepository userRepository, EmailService emailService) {
        this.authenticationManager = authenticationManager;
        this.jwtTokenUtil = jwtTokenUtil;
        this.userDetailsService = userDetailsService;
        this.userBuilder = userBuilder;
        this.sessionRepository = sessionRepository;
        this.selectionService = selectionService;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userRepository = userRepository;
        this.emailService = emailService;
    }

    public boolean emailAvailable(String email) {
        return userRepository.findByUsername(email).isEmpty();
    }

    @Deprecated
    public TokenResponseWithStats oldLogin(
            final AuthRequest authRequest,
            final boolean fetchSelections
    ) {
        final var result = startNewSession(authRequest);

        final var purchases = userPurchasesRepository
                .findAllByUser((User) result.userDetails())
                .stream()
                .map(UserPurchase::getProductId)
                .toList();

        final var user = userBuilder.createDTOFromUser((User) result.userDetails());

        if (!fetchSelections) {
            return new TokenResponseWithStats(
                    result.token(),
                    user,
                    null,
                    purchases
            );
        }

        final var selections = selectionService.getUserSelections((User) result.userDetails());
        return new TokenResponseWithStats(
                result.token(),
                user,
                selections,
                purchases
        );
    }

    public TokenResponse login(AuthRequest request) {
        final var result = startNewSession(request);
        final var user = userBuilder.createDTOFromUser((User) result.userDetails());
        return new TokenResponse(result.token(), user);
    }

    private TokenAndUserDetails startNewSession(AuthRequest authRequest) {
        // Perform the security
        final var caseInsensitiveEmail = authRequest.getEmail().toLowerCase();

        var accountDoesNotExist = emailAvailable(caseInsensitiveEmail);
        if (accountDoesNotExist) {
            throw new UserNotFoundByUsernameException(caseInsensitiveEmail);

        }

        var password = authRequest.getPassword();
        if (password == null) {
            password = caseInsensitiveEmail;
        }

        final var authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(caseInsensitiveEmail, password)
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);

        // Reload password post-security so we can generate token
        final var userDetails = userDetailsService.loadUserByUsername(caseInsensitiveEmail);

        final var token = jwtTokenUtil.generateToken(userDetails);

        // Saves session information
        final Session session = new Session((User) userDetails, token, authRequest.getPlatform(), LocalDateTime.now());
        sessionRepository.save(session);
        return new TokenAndUserDetails(userDetails, token);
    }

    public void toggleUnsubscribed(User user) {
        user.setUnsubscribed(!user.getUnsubscribed());
        userRepository.save(user);
    }

    private record TokenAndUserDetails(UserDetails userDetails, String token) {
    }
}
