package com.arrivinginhighheels.visited.backend.features.poster;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
public class PriceDto {
    private String id;
    private String currencyCode;
    private Long unitAmount;
    private BigDecimal unitAmountDecimal;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal tax;
}
