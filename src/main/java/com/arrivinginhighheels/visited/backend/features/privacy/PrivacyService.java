package com.arrivinginhighheels.visited.backend.features.privacy;

import com.arrivinginhighheels.visited.backend.dto.PrivacyAgreementDTO;
import com.arrivinginhighheels.visited.backend.dto.PrivacyDTO;
import com.arrivinginhighheels.visited.backend.model.Selection;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.SelectionRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Service for the CASL and GDPR functionality.
 */
@Service
public class PrivacyService {

    public static final String[] PRIVACY_ISO_KEYS = {"CA"};

    private final PrivacyAgreementRepository privacyAgreementRepository;

    private final SelectionRepository selectionRepository;

    public PrivacyService(PrivacyAgreementRepository privacyAgreementRepository, SelectionRepository selectionRepository) {
        this.privacyAgreementRepository = privacyAgreementRepository;
        this.selectionRepository = selectionRepository;
    }

    /**
     * Check if the user has CASL information stored in the Database. If not, checks if CASL agreement is required
     * for the user by checking if the user has lived or lives in Canada (saving the information in the DB for quick
     * access in future requests in case the user has lived or lives in Canada).
     *
     * @param user - the user
     * @return CASL info
     */
    @Transactional
    public PrivacyDTO getPrivacyStatusForTheUser(User user) {

        //1. search in the DB for an existing privacy agreement already informed.
        PrivacyAgreement privacyAgreement = privacyAgreementRepository.findByUser(user);

        if (privacyAgreement != null) {
            //1.1 if there's an already informed privacy agreement, just returns it.
            return new PrivacyDTO(privacyAgreement);
        }

//        //2. checks if the user lived/lives in any CASL or GDPR geoarea
//        if (hasUserLivedOrIsLivingInPrivacyRequiredCountry(user)) {
            //2.1 user has lived/lives in privacy agreement required area

            //2.1.1 saves the information in the database for quick future access
            privacyAgreement = new PrivacyAgreement();
            privacyAgreement.setUser(user);
            privacyAgreement.setRequired(true);
            privacyAgreement.setOptin(TriStateValue.NA);
            privacyAgreement.setTerms(TriStateValue.NA);

            privacyAgreementRepository.save(privacyAgreement);

            //2.1.2 returns the information requiring CASL for this user
            return new PrivacyDTO(privacyAgreement);

//        } else {
//            //2.2 user hasn't lived and doesn't live in Canada, just returns info stating CASL is not required
//            return new PrivacyDTO(false, TriStateValue.NA, TriStateValue.NA);
//        }
    }

    /**
     * Saves (inserts or updates) the agreement info for CASL for the user
     *
     * @param user
     * @param privacyAgreementDTO
     * @return
     */
    @Transactional
    public PrivacyDTO setPrivacyStatusForTheUser(User user, PrivacyAgreementDTO privacyAgreementDTO) {

        PrivacyAgreement privacyAgreement = privacyAgreementRepository.findByUser(user);

        if (privacyAgreement == null) {
            privacyAgreement = new PrivacyAgreement();
            privacyAgreement.setUser(user);
        }

        privacyAgreement.setRequired(false);
        privacyAgreement.setOptin(privacyAgreementDTO.getOptIn());
        privacyAgreement.setTerms(privacyAgreementDTO.getTerms());
        privacyAgreement.setTimestamp(new Date());

        privacyAgreementRepository.save(privacyAgreement);

        return new PrivacyDTO(privacyAgreement);
    }

    @Transactional
    public void deletePrivacyAgreementForUser(User user) {
        privacyAgreementRepository.deleteByUser(user);
    }

    private boolean hasUserLivedOrIsLivingInPrivacyRequiredCountry(User user) {
        // Get all the areas that user has selected 'Lived'
        List<Selection> livedSelections = selectionRepository.findAllByUserAndType(user, SelectionType.LIVED);

        if (livedSelections == null) {
            return false;
        }

        if (livedSelections.isEmpty()) {
            return false;
        }

        List<String> privacyIsoCodes = Arrays.asList(PRIVACY_ISO_KEYS);

        // Check if any of the selections are on the GDPR or CASL list
        for (Selection selection : livedSelections) {
            final String iso = selection.getGeoArea().getIsoKey();
            if (privacyIsoCodes.contains(iso)) {
                return true;
            }
        }

        return false;
    }

    public void deletePrivacyAgreementForUserId(User user) {
        privacyAgreementRepository.deleteByUser(user);
    }
}
