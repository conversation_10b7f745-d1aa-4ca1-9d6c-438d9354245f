package com.arrivinginhighheels.visited.backend.features.emails;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface TrackedLinkRepository extends JpaRepository<TrackedLink, Long> {
    @Query("select distinct link from TrackedLink link where link.url = ?1")
    Optional<TrackedLink> findByUrl(String url);
    Optional<TrackedLink> findByHash(String hash);
}
