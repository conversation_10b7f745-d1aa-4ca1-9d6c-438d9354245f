package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.SelectionType;

public class ExperienceAreaSelectionDTO {
    private Long experienceId;
    private SelectionType type;

    public ExperienceAreaSelectionDTO() { }

    public ExperienceAreaSelectionDTO(Long experienceId, SelectionType type) {
        this.experienceId = experienceId;
        this.type = type;
    }

    public Long getExperienceId() {
        return experienceId;
    }

    public void setExperienceId(Long experienceId) {
        this.experienceId = experienceId;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(SelectionType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "ExperienceAreaSelectionDTO{" +
                "experienceId=" + experienceId +
                ", type=" + type +
                '}';
    }
}
