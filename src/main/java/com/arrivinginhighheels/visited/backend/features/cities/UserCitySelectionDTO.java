package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.SelectionType;

public class UserCitySelectionDTO {
    private Long cityId;
    private SelectionType type;

    public UserCitySelectionDTO() {
    }

    public UserCitySelectionDTO(final Long cityId, final SelectionType type) {
        this.cityId = cityId;
        this.type = type;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(final Long cityId) {
        this.cityId = cityId;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(final SelectionType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "UserPlaceSelectionDTO{" +
                "placeId=" + cityId +
                ", type=" + type +
                '}';
    }
}
