package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface TodoListItemTranslationRepository extends JpaRepository<TodoListItemTranslation, Long> {
    TodoListItemTranslation findByListItemAndSupportedLanguage(TodoListItem listItem, SupportedLanguage language);

    List<TodoListItemTranslation> findBySupportedLanguageAndListItemIdIn(SupportedLanguage supportedLanguage, List<Long> listItemIds);
}
