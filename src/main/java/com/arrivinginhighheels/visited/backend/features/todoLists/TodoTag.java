package com.arrivinginhighheels.visited.backend.features.todoLists;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Objects;

@Entity()
@Table(name = "todo_list_tags")
public class TodoTag {
    @Id
    private Long id;

    private String name;

    public TodoTag() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoTag todoTag = (TodoTag) o;
        return Objects.equals(id, todoTag.id) && Objects.equals(name, todoTag.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }

    @Override
    public String toString() {
        return "TodoTag{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
