package com.arrivinginhighheels.visited.backend.features.itineraries;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.AreaDetails;

import java.util.Objects;

public final class ItinerarySummary {
    private final GeoArea area;
    private final AreaDetails details;
    private final int amount;

    ItinerarySummary(GeoArea area, AreaDetails details, int amount) {
        this.area = area;
        this.details = details;
        this.amount = amount;
    }

    public GeoArea area() {
        return area;
    }

    public AreaDetails details() {
        return details;
    }

    public int amount() {
        return amount;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (ItinerarySummary) obj;
        return Objects.equals(this.area, that.area) &&
                Objects.equals(this.details, that.details) &&
                Objects.equals(this.amount, that.amount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(area, details, amount);
    }

    @Override
    public String toString() {
        return "ItinerarySummary[" +
                "area=" + area + ", " +
                "details=" + details + ", " +
                "amount=" + amount + ']';
    }
}
