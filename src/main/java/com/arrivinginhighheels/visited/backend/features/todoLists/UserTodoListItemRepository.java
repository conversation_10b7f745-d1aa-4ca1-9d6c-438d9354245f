package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserTodoListItemRepository extends JpaRepository<UserTodoListItem, Long> {

    void deleteByUser(User user);

    List<UserTodoListItem> findAllByUserAndList(User user, TodoList list);

    Optional<UserTodoListItem> findByUserAndListItemAndList(User user, TodoListItem item, TodoList list);

    @Query(value = """
            SELECT list_id, COUNT(list_id)
            FROM user_todo_list_items
            WHERE user_id = ?1 AND selection_type = 'BEEN'
            GROUP BY list_id""",
            nativeQuery = true)
    List<Object[]> getSelectionSummaryByUser(Long userId);

    @Query(value = """
            SELECT list_item_id, COUNT(list_item_id) as count
            FROM user_todo_list_items
            WHERE list_id = ?1 AND selection_type = 'BEEN'
            GROUP BY list_item_id 
            ORDER BY count DESC""", nativeQuery = true)
    List<Object[]> findListItemsByPopularityForList(Long list);

    @Query(value = """
            SELECT list_id, COUNT(DISTINCT user_id) as user_count
            FROM user_todo_list_items
            GROUP BY list_id
            ORDER BY user_count DESC""",
           nativeQuery = true)
    List<Object[]> findListsByPopularity();

    @Query("""
    SELECT new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoListItem(xref.listItem, xref.todoList, translation, xref.thumbnail, xref.popularity)
    FROM UserTodoListItem u
    INNER join TodoListXref xref on xref.todoList = u.list and xref.listItem = u.listItem
    INNER JOIN TodoListItemTranslation translation ON translation.listItem = u.listItem
    WHERE u.user = ?1 AND u.listItem.geoArea = ?2 AND u.type = 'WANT' AND translation.supportedLanguage = ?3
    """)
    List<LocalizedTodoListItem> findUserSelectedLocalizedWantByArea(User user, GeoArea area, SupportedLanguage language);

    @Query("""
    SELECT xref
    FROM UserTodoListItem u
    INNER join TodoListXref xref on xref.todoList = u.list and xref.listItem = u.listItem
    WHERE u.user = ?1 AND u.listItem.geoArea = ?2 AND u.type = 'WANT'
    """)
    List<TodoListXref> findUserSelectedWantByArea(User user, GeoArea area);

    @Query("""
    SELECT new com.arrivinginhighheels.visited.backend.features.todoLists.UserListSelectionSummary(
      u.listItem.geoArea.id,
      count(u.listItem.geoArea.id))
    FROM UserTodoListItem u
    WHERE u.user = ?1 AND u.type = 'BEEN'
    GROUP BY u.listItem.geoArea.id
    """)
    List<UserListSelectionSummary> findAreaSummaryByUser(User user);

    List<UserTodoListItem> findAllByUserAndListItem_GeoArea(User user, GeoArea area);
}
