package com.arrivinginhighheels.visited.backend.features.experiences;

import lombok.Getter;

//@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
public class ExperienceDTO {
    private Long id;
    private String name;
    private String etag;
    private String iconUrl;
    private String file;
    private String dealsEtag;

    public ExperienceDTO() { }


    public ExperienceDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public ExperienceDTO setName(String name) {
        this.name = name;
        return this;
    }

    public ExperienceDTO setEtag(String etag) {
        this.etag = etag;
        return this;
    }

    public ExperienceDTO setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
        return this;
    }

    public ExperienceDTO setFile(String file) {
        this.file = file;
        return this;
    }

    public ExperienceDTO setDealsEtag(String dealsEtag) {
        this.dealsEtag = dealsEtag;
        return this;
    }

    @Override
    public String toString() {
        return "ExperienceDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", etag='" + etag + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", file='" + file + '\'' +
                ", dealsEtag='" + dealsEtag + '\'' +
                '}';
    }
}
