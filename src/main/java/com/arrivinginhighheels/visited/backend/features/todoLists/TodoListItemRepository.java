package com.arrivinginhighheels.visited.backend.features.todoLists;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import jakarta.transaction.Transactional;
import java.util.Date;

public interface TodoListItemRepository extends JpaRepository<TodoListItem, Long> {

  @Query("select max(l.lastModificationTime) from TodoListItem l")
  Date getMaxModificationTime();

  @Transactional
  @Modifying
  @Query(value = """
      UPDATE todo_lists_xref
      SET ordinal = ?3
      WHERE todo_list_id = ?1 AND todo_list_item_id = ?2
      """, nativeQuery = true)
  void updateListItemOrder(Long listId, Long listItemId, int order);

  @Modifying
  @Transactional
  @Query(value = """
      create sequence if not exists list_popularity_id;
      do
      $$
      declare
          f record;
      begin
          for f in select id, name from todo_lists
          loop

      PERFORM SETVAL('list_popularity_id', 1);

      update todo_lists_xref
      set ordinal = (select count(todo_list_id)
                     from todo_lists_xref
                     where todo_list_id = f.id)
      where todo_list_id = f.id;

      update todo_lists_xref
      set ordinal = list_popularity.ordinal
      from (select
      	nextval('list_popularity_id') as ordinal,
      	todo_list_items.name,
      	user_todo_list_items.list_item_id as id,
      	count(user_todo_list_items.list_item_id) as count
      from
          user_todo_list_items, todo_list_items
      where
         user_todo_list_items.list_id  = f.id AND
         todo_list_items.id = user_todo_list_items.list_item_id
      group by
        user_todo_list_items.list_item_id, todo_list_items.name
      order by "count" DESC) as list_popularity
      where list_popularity.id = todo_lists_xref.todo_list_item_id and todo_list_id = f.id;

      end loop;
      end;
      $$;

      DO $$
      DECLARE results RECORD;
      BEGIN FOR results IN
        select
        	distinct list_id, list_item_id, count(*)
        from
        	user_todo_list_items
        group by
        	list_id, list_item_id
      LOOP
        UPDATE
        	todo_lists_xref
        SET
        	popularity = results.count
        WHERE
       	todo_list_id = results.list_id AND
       	todo_list_item_id = results.list_item_id;
        END LOOP;
      END; $$

      """, nativeQuery = true)
  void sortByPopularity();
}
