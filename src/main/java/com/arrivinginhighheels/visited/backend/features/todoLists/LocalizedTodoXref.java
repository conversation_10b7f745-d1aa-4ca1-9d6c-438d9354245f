package com.arrivinginhighheels.visited.backend.features.todoLists;

import java.util.Objects;

public final class LocalizedTodoXref {
    private final TodoListXref xref;
    private final TodoListItemTranslation translation;

    public LocalizedTodoXref(TodoListXref xref, TodoListItemTranslation translation) {
        this.xref = xref;
        this.translation = translation;
    }

    public TodoListXref xref() {
        return xref;
    }

    public TodoListItemTranslation translation() {
        return translation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LocalizedTodoXref that)) return false;
        return Objects.equals(xref, that.xref) && Objects.equals(translation, that.translation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(xref, translation);
    }

    @Override
    public String toString() {
        return "LocalizedTodoXref{" +
                "xref=" + xref +
                ", translation=" + translation +
                '}';
    }
}
