package com.arrivinginhighheels.visited.backend.features.itineraries;

import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.ITINERARIES_URL;

@RestController
@RequestMapping(path = ITINERARIES_URL)
public class ItineraryController {
        private final ItineraryService itineraryService;

        private final LoggedInUserUtil userUtil;
        private final ResponseHelper responseHelper;

        public ItineraryController(ItineraryService itineraryService, LoggedInUserUtil userUtil,
                        ResponseHelper responseHelper) {
                this.itineraryService = itineraryService;
                this.userUtil = userUtil;
                this.responseHelper = responseHelper;
        }

        @GetMapping(path = "/summary")
        public ResponseEntity<List<ItinerarySummaryDTO>> getSummary(
                        final HttpServletRequest request,
                        @RequestParam(required = false) Double resolution) {
                var user = userUtil.getLoggedInUser(request);
                var actualResolution = resolution == null ? 1.0 : resolution;

                return responseHelper.doNotCacheResponse(
                                itineraryService.getItinerarySummaries(
                                                user,
                                                actualResolution,
                                                false));
        }

        @GetMapping(path = "areas/{iso}")
        public ItineraryDTO getByArea(
                        final HttpServletRequest request,
                        @PathVariable String iso,
                        @RequestParam(required = false) Double resolution) {
                var user = userUtil.getLoggedInUser(request);
                var actualResolution = resolution == null ? 1.0 : resolution;
                return itineraryService.getByArea(user, iso, actualResolution);
        }

        @GetMapping(path = "/summary/v2")
        public ResponseEntity<List<ItinerarySummaryDTO>> getSummaryV2(
                        final HttpServletRequest request,
                        @RequestParam(required = false) Double resolution) {
                var user = userUtil.getLoggedInUser(request);
                var actualResolution = resolution == null ? 1.0 : resolution;

                return responseHelper.doNotCacheResponse(
                                itineraryService.getItinerarySummaries(
                                                user,
                                                actualResolution,
                                                true));
        }

        @GetMapping(path = "areas/{iso}/v2")
        public ResponseEntity<ItineraryV2dto> getByAreaV2(
                        final HttpServletRequest request,
                        @PathVariable String iso,
                        @RequestParam(required = false) Double resolution) {
                var user = userUtil.getLoggedInUser(request);
                var actualResolution = resolution == null ? 1.0 : resolution;
                return responseHelper.doNotCacheResponse(
                                itineraryService.getAreaDetailsV2(user, iso, actualResolution));
        }

        @GetMapping(path = "areas/{iso}/notes")
        public ResponseEntity<Optional<UserItineraryDto>> getByArea(
                        final HttpServletRequest request,
                        @PathVariable String iso) {
                final var user = userUtil.getLoggedInUser(request);
                final var notes = itineraryService.getUserItinerary(user, iso);
                return responseHelper.doNotCacheResponse(notes);
        }

        @PostMapping(path = "areas/{iso}/notes")
        public boolean getByArea(
                        final HttpServletRequest request,
                        @PathVariable String iso,
                        @RequestBody @Valid UserItineraryDto itineraryDto) {
                final var user = userUtil.getLoggedInUser(request);
                return itineraryService.saveUserItinerary(user, iso, itineraryDto);
        }
}
