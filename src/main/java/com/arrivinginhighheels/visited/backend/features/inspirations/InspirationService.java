package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.AreaSimpleDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import com.arrivinginhighheels.visited.backend.service.translations.AreaTranslationService;
import com.arrivinginhighheels.visited.backend.service.translations.InspirationTranslationService;
import com.arrivinginhighheels.visited.backend.utils.AdaptiveImageUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import jakarta.ws.rs.ForbiddenException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InspirationService {
    private static final int PAGE_SIZE = 50;

    private final InspirationRepository inspirationRepository;
    private final AreaTranslationService areaTranslationService;
    private final InspirationTranslationService inspirationTranslationService;
    private final UserInspirationsRepository userInspirationsRepository;
    private final SelectionService selectionService;
    private final UserPurchasesRepository userPurchasesRepository;
    private final AdaptiveImageUtils imageUtils;
    private final GeoAreaRepository areaRepository;
    private final YamlConfig yamlConfig;

    public InspirationService(
            InspirationRepository inspirationRepository,
            AreaTranslationService areaTranslationService,
            InspirationTranslationService inspirationTranslationService,
            UserInspirationsRepository userInspirationsRepository,
            SelectionService selectionService,
            UserPurchasesRepository userPurchasesRepository,
            AdaptiveImageUtils imageUtils,
            GeoAreaRepository areaRepository,
            YamlConfig yamlConfig
    ) {
        this.inspirationRepository = inspirationRepository;
        this.areaTranslationService = areaTranslationService;
        this.inspirationTranslationService = inspirationTranslationService;
        this.userInspirationsRepository = userInspirationsRepository;
        this.selectionService = selectionService;
        this.userPurchasesRepository = userPurchasesRepository;
        this.imageUtils = imageUtils;
        this.areaRepository = areaRepository;
        this.yamlConfig = yamlConfig;
    }

    public InspirationDTO getInspirationById(final User user, final Long id, final double resolution) {
        final Optional<Inspiration> maybeInspiration = inspirationRepository.findById(id);

        if (maybeInspiration.isEmpty()) {
            return null;
        }

        final Inspiration inspiration = maybeInspiration.get();

        if (checkIfUserIsForbiddenToAccessInspiration(user, inspiration.getId())) {
            return null;
        }

        final String inspirationTranslation = inspirationTranslationService.getTranslation(inspiration);
        final String areaTranslation = areaTranslationService.getAreaName(inspiration.getGeoArea());

        return new InspirationDTO(
                inspiration.getId(),
                inspirationTranslation,
                buildAreaDto(inspiration.getGeoArea(), areaTranslation),
                imageUtils.getResolution(inspiration.getImage(), resolution),
                inspiration.getImage().getBlurHash()
        );
    }

    public List<InspirationDTO> getNextSetOfInspirations(final User user, final Integer page, final double resolution) {
        return doGetInspirations(user, resolution, page, (purchase, request) -> {
            var inspirations =  request != null
                    ? inspirationRepository.getNextInspirations(user, request)
                    : inspirationRepository.getAllUnselectedInspirations(user);

            if (!purchase) {
                inspirations.removeIf(i -> i.getId() > PAGE_SIZE);
            }
            return inspirations;
        });

    }

    public List<InspirationDTO> getNextSetOfInspirationsForArea(final User user, final String isoCode, final Integer page, final double resolution) {
        var area = areaRepository.findByIsoKey(isoCode).orElseThrow(() -> new GeoAreaNotFoundException(isoCode));
        return doGetInspirations(
                user,
                resolution,
                page,
                (purchased, pageable) -> inspirationRepository.getNextInspirationForUserAndArea(user, area, pageable));
    }

    @FunctionalInterface
    private interface InspirationGetter {
        List<Inspiration> get(Boolean purchase, Pageable request);
    }

    private List<InspirationDTO> doGetInspirations(
            final User user,
            final double resolution,
            final Integer page,
            final InspirationGetter getter) {

        final var purchase = hasUserPurchasedInspirations(user);

        Pageable request;
        if (!purchase) {
            request = PageRequest.of(0, PAGE_SIZE);
        } else if (page != null) {
            request = PageRequest.of(page, PAGE_SIZE);
        } else {
            request = null;
        }


        final var inspirations = getter.get(purchase, request);

        final var areas = inspirations.stream().map(Inspiration::getGeoArea).collect(Collectors.toSet());
        final var areaTranslations = areaTranslationService.getAllTranslationsForAreas(areas);
        final var inspirationTranslations = inspirationTranslationService.getAllTranslationsForInspirations(inspirations);

        return inspirations
                .stream()
                .map((Inspiration i) -> buildInspirationDto(i, resolution, inspirationTranslations, areaTranslations))
                .collect(Collectors.toList());
    }

    public Map<Long, InspirationSelectionType> getAllSelectionsForUser(final User user) {
        final List<UserInspiration> selections = userInspirationsRepository.findAllByUser(user);
        final Map<Long, InspirationSelectionType> selectionsDto = new HashMap<>();
        for (final UserInspiration selection : selections) {
            selectionsDto.put(selection.getInspiration().getId(), selection.getType());
        }
        return selectionsDto;
    }

    public SelectionsDTO handleSelection(final User user, final UserInspirationSelectionDTO selectionDTO) {
        final Optional<Inspiration> inspirationBox = inspirationRepository.findById(selectionDTO.getInspirationId());

        if (inspirationBox.isEmpty()) {
            throw new RuntimeException("Cannot find inspiration with id " + selectionDTO.getInspirationId());
        }

        final Inspiration inspiration = inspirationBox.get();

        UserInspiration selection = userInspirationsRepository.findByUserAndInspiration(user, inspiration);
        if (selection == null) {
            selection = new UserInspiration();
            selection.setUser(user);
            selection.setInspiration(inspiration);
        }

        final InspirationSelectionType selectionType = selectionDTO.getType();

        selection.setType(selectionType);
        selection.setLastModificationTime(new Date());

        userInspirationsRepository.save(selection);

        final GeoArea area = inspiration.getGeoArea();
        final Selection existing = selectionService.getSelectionsForArea(user, area);
        final SelectionType existingType = existing == null ? SelectionType.CLEAR : existing.getType();

        if (existingType.getWeight() >= selectionType.getWeight()) {
            return null;
        }

        return selectionService.handleSelection(user, inspiration.getGeoArea().getIsoKey(), selectionType.toGeoSelectionType());
    }

    private boolean checkIfUserIsForbiddenToAccessInspiration(final User user, final long id) {
        if (id <= PAGE_SIZE) {
            return false;
        }

        if (!hasUserPurchasedInspirations(user)) {
            throw new ForbiddenException("You must purchase the Inspirations feature or Visited Unlimited to access this content");
        }

        return false;
    }

    private boolean hasUserPurchasedInspirations(final User user) {
        final var purchased = userPurchasesRepository.countByUserAndProductIdIn(user, Arrays.asList(
                yamlConfig.getUnlockInspirationsBundleId(),
                yamlConfig.getProLifetimeBundleId(),
                yamlConfig.getProSubscriptionAnnualBundleId(),
                yamlConfig.getProSubscriptionMonthlyBundleId()
        ));
        return purchased > 0;
    }

    private InspirationDTO buildInspirationDto(final Inspiration i, final double resolution, final Map<Inspiration, String> inspirationTranslations, final Map<GeoArea, String> areaTranslations) {
        final String translation = inspirationTranslations.containsKey(i)
                ? inspirationTranslations.get(i)
                : i.getName();

        return new InspirationDTO(
                i.getId(),
                translation,
                buildAreaDto(i.getGeoArea(), areaTranslations),
                imageUtils.getResolution(i.getImage(), resolution),
                i.getImage().getBlurHash()
        );
    }

    private AreaSimpleDTO buildAreaDto(final GeoArea area, final Map<GeoArea, String> areaTranslations) {
        final String name = areaTranslations.containsKey(area) ? areaTranslations.get(area) : area.getName();
        return buildAreaDto(area, name);
    }

    private AreaSimpleDTO buildAreaDto(final GeoArea area, final String translation) {
        return new AreaSimpleDTO(
                area.getId(),
                area.getIsoKey(),
                translation
        );
    }

    public Date getMaxModificationTimeOfInspiration(final Long inspiration) {
        return inspirationRepository.getMaxModificationTimeOfInspirationById(inspiration);
    }
}
