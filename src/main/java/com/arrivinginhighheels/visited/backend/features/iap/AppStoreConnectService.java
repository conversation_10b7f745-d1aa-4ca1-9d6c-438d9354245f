package com.arrivinginhighheels.visited.backend.features.iap;

import com.apple.itunes.storekit.client.APIError;
import com.apple.itunes.storekit.client.APIException;
import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.client.GetTransactionHistoryVersion;
import com.apple.itunes.storekit.migration.ReceiptUtility;
import com.apple.itunes.storekit.model.*;
import com.apple.itunes.storekit.model.TransactionHistoryRequest.ProductType;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.apple.itunes.storekit.verification.VerificationException;
import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.sentry.Sentry;
import lombok.SneakyThrows;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import java.io.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

import static java.nio.charset.StandardCharsets.UTF_8;

@Service
public class AppStoreConnectService {

    private final AppStoreServerAPIClient client;
    private final YamlConfig config;
    private final ResourceLoader resourceLoader;
    private final Environment environment;
    private final YamlConfig yamlConfig;

    @SneakyThrows
    public AppStoreConnectService(
            YamlConfig config,
            ResourceLoader resourceLoader,
            YamlConfig yamlConfig) {

        this.config = config;
        this.resourceLoader = resourceLoader;
        environment = config.isSandbox() ? Environment.SANDBOX : Environment.PRODUCTION;

        final var encodedKey = readResource("classpath:appStoreConnect/SubscriptionKey_4GUV723F56.p8");

        client = new AppStoreServerAPIClient(
                encodedKey,
                config.getAppStoreConnectKeyId(),
                config.getAppStoreConnectIssuerId(),
                config.getAppStoreConnectBundleId(),
                environment
        );
        this.yamlConfig = yamlConfig;
    }

    public AppStoreNotification checkAppStoreConnection() throws APIException, IOException, InterruptedException, VerificationException {
        final var test = client.requestTestNotification();
        Thread.sleep(5000L);
        final var checkResponse = client.getTestNotificationStatus(test.getTestNotificationToken());

        final var signedPayload = checkResponse.getSignedPayload();
        return decodeServerNotification(signedPayload);
    }

    private long milliseconds(LocalDateTime localDateTime) {
        var zoneDateTime = localDateTime.atZone(ZoneId.of("America/Los_Angeles"));
        return zoneDateTime.toInstant().toEpochMilli();
    }

    public void investigateNotificationHistory() throws APIException, IOException, VerificationException {
        var startMilliSeconds = milliseconds(LocalDateTime.of(2025, 1, 1, 0, 0));
        var endMilliSeconds = milliseconds(LocalDateTime.of(2025, 4, 1, 0, 0));
        var historyRequest = new NotificationHistoryRequest()
                .startDate(startMilliSeconds)
                .endDate(endMilliSeconds)
                .onlyFailures(true)
                .notificationType(NotificationTypeV2.TEST);
        var result = client.getNotificationHistory(null, historyRequest);
        var history = result.getNotificationHistory();

        final var signedDataVerifier = buildSignedDataVerifier();

        for (NotificationHistoryResponseItem item : history) {
            final var decoded = signedDataVerifier.verifyAndDecodeNotification(item.getSignedPayload());
            System.out.println(decoded);
        }

        System.out.println(history);
    }

    public AppStoreNotification decodeServerNotification(String payload) throws IOException, VerificationException {
        final var signedDataVerifier = buildSignedDataVerifier();
        final var decoded = signedDataVerifier.verifyAndDecodeNotification(payload);

        final var id = decoded.getNotificationUUID();
        final var type = decoded.getNotificationType();

        final var purchase = getPurchaseInformationFromServerNotification(decoded, type);
        return new AppStoreNotification(id, type, decoded.getSubtype(), purchase);
    }

    private Optional<ValidatedPurchase> getPurchaseInformationFromServerNotification(ResponseBodyV2DecodedPayload decoded, NotificationTypeV2 type) {
        if (type == NotificationTypeV2.TEST) {
            return Optional.empty();
        }

        final var data = decoded.getData();

        if (data == null) {
            return Optional.empty();
        }

        final var signedData = data.getSignedTransactionInfo();
        if (signedData == null) {
            return Optional.empty();
        }

        try {
            final var verifier = buildSignedDataVerifier();
            final var transaction = verifier.verifyAndDecodeTransaction(signedData);

            final var transactionId = transaction.getOriginalTransactionId();
            final var expirationDate = extractExpirationDate(transaction);
            var productId = transaction.getProductId();

            if (type.equals(NotificationTypeV2.DID_CHANGE_RENEWAL_PREF)) {
                // Check if the product id has changed.
                // The notification will only tell you the original product id.
                var mostRecent = findMostRecentTransaction(transactionId, getProductType(productId));
                productId = mostRecent.getProductId();
            }

            return Optional.of(new ValidatedPurchase(
                    productId,
                    transactionId,
                    UserPurchaseStatus.ACTIVE,
                    userPurchaseProductTypeFromAppleProductType(getProductType(productId)),
                    expirationDate, true)
            );

        } catch (Exception e) {
            return Optional.empty();
        }
    }

    private SignedDataVerifier buildSignedDataVerifier() throws IOException {
        final var rootCAs = Set.of(
                getInputStream("classpath:appStoreConnect/AppleComputerRootCertificate.cer"),
                getInputStream("classpath:appStoreConnect/AppleIncRootCertificate.cer"),
                getInputStream("classpath:appStoreConnect/AppleRootCA-G2.cer"),
                getInputStream("classpath:appStoreConnect/AppleRootCA-G3.cer")
        );

        final boolean onlineChecks = true;

        return new SignedDataVerifier(
                rootCAs,
                config.getAppStoreConnectBundleId(),
                config.getAppStoreAppleId(),
                environment,
                onlineChecks);
    }

    private InputStream getInputStream(String resourceName) throws IOException {
        return resourceLoader.getResource(resourceName).getInputStream();
    }

    private String readResource(String resourceName) {
        final var resource = resourceLoader.getResource(resourceName);
        try (Reader reader = new InputStreamReader(resource.getInputStream(), UTF_8)) {
            return FileCopyUtils.copyToString(reader);
        } catch (IOException e) {
        throw new UncheckedIOException(e);
        }
    }

    public Optional<ValidatedPurchase> validateAppReceiptAndExtractTransactionId(
            String productId,
            String appReceipt,
            String localVerificationData) throws IOException, APIException, VerificationException {

        String transactionId = extractTransactionId(appReceipt, localVerificationData);

        if (transactionId == null) {
            Sentry.captureException(new RuntimeException(
                    "Failed to extract transaction id from app receipt " + productId));
            return Optional.empty();
        }

        return verifyTransactionWithApple(productId, transactionId);
    }

    private String extractTransactionId(String appReceipt, String localVerificationData) {
        String id = null;

        if (isProbablyBase64(appReceipt)) {
            id = tryExtractFromBase64Receipt(appReceipt);
        }

        if (id == null && isJwsFormat(appReceipt)) {
            id = tryExtractFromJws(appReceipt);
        }

        if (id == null && localVerificationData != null) {
            id = tryExtractFromLocalVerificationData(localVerificationData);
        }

        return id;
    }

    private String tryExtractFromBase64Receipt(String appReceipt) {
        try {
            var receiptUtility = new ReceiptUtility();
            return receiptUtility.extractTransactionIdFromAppReceipt(appReceipt);
        } catch (Exception e) {
            Sentry.captureException(e);
            return null;
        }
    }

    private String tryExtractFromJws(String appReceipt) {
        try {
            SignedDataVerifier verifier = buildSignedDataVerifier();
            var transaction = verifier.verifyAndDecodeTransaction(appReceipt);
            return transaction.getTransactionId();
        } catch (Exception e) {
            Sentry.captureException(e);
            return null;
        }
    }

    private String tryExtractFromLocalVerificationData(String json) {
        try {
            var node = new ObjectMapper().readTree(json);
            return node.has("transactionId") ? node.get("transactionId").asText() : null;
        } catch (Exception e) {
            Sentry.captureException(e);
            return null;
        }
    }

    private boolean isProbablyBase64(String str) {
        // quick check: no dots, only base64 chars
        return str != null && str.length() > 100 && !str.contains(".") &&
                str.matches("^[A-Za-z0-9+/=\\r\\n]+$");
    }

    private boolean isJwsFormat(String str) {
        // JWS strings have three parts separated by dots
        return str != null && str.split("\\.").length == 3;
    }

    private Optional<ValidatedPurchase> verifyTransactionWithApple(String productId, String transactionId)
            throws APIException, VerificationException, IOException {

        final var productType = getProductType(productId);

        try {
            return Optional.of(validateTransaction(transactionId, productType));
        } catch (APIException e) {
            Sentry.captureException(e);

            if (e.getApiError().equals(APIError.TRANSACTION_ID_NOT_FOUND)) {
                return Optional.of(new ValidatedPurchase(
                        productId,
                        transactionId,
                        UserPurchaseStatus.ACTIVE,
                        userPurchaseProductTypeFromAppleProductType(productType),
                        Optional.empty(),
                        false
                ));
            }

            throw e;
        }
    }


    private ProductType getProductType(String productId) {
        return isSubscription(productId)
                ? ProductType.AUTO_RENEWABLE
                : ProductType.NON_CONSUMABLE;
    }

    private boolean isSubscription(final String bundleId) {
        return bundleId.equals(yamlConfig.getProSubscriptionAnnualBundleId())
                || bundleId.equals(yamlConfig.getProSubscriptionMonthlyBundleId());
    }

    private ValidatedPurchase validateTransaction(String transactionId, ProductType productType) throws APIException, IOException, VerificationException {
        var decoded = findMostRecentTransaction(transactionId, productType);

        // Check if the purchase is active
        var expirationDate = extractExpirationDate(decoded);
        var isRevoked = decoded.getRevocationReason() != null;
        var status = isRevoked ? UserPurchaseStatus.REFUNDED : UserPurchaseStatus.ACTIVE;
        var type = userPurchaseProductTypeFromAppleProductType(productType);

        return new ValidatedPurchase(decoded.getProductId(), transactionId, status, type, expirationDate, true);
    }

    private JWSTransactionDecodedPayload findMostRecentTransaction(String transactionId, ProductType productType) throws APIException, IOException, VerificationException {
        final var transactions = new LinkedList<String>();

        var request = new TransactionHistoryRequest().productTypes(List.of(productType));
        HistoryResponse response = null;
        do {
            String revision = response != null ? response.getRevision() : null;
            response = client.getTransactionHistory(transactionId, revision, request, GetTransactionHistoryVersion.V2);
            transactions.addAll(response.getSignedTransactions());
        } while (response.getHasMore());

        var verifier = buildSignedDataVerifier();

        var all = transactions.stream().map(transaction -> {
            try {
                return verifier.verifyAndDecodeTransaction(transaction);
            } catch (VerificationException e) {
                Sentry.captureException(e);
                throw new RuntimeException(e);
            }
        }).toList();

        return Collections.max(all, new TransactionComparator());
    }

    private UserPurchaseProductType userPurchaseProductTypeFromAppleProductType(ProductType productType) {
        switch (productType) {
            case AUTO_RENEWABLE -> {
                return UserPurchaseProductType.AUTO_RENEWABLE;
            }
            case NON_RENEWABLE -> {
                return UserPurchaseProductType.NON_RENEWABLE;
            }
            case CONSUMABLE -> {
                return UserPurchaseProductType.CONSUMABLE;
            }
            case NON_CONSUMABLE -> {
                return UserPurchaseProductType.NON_CONSUMABLE;
            }
            default -> throw new IllegalStateException("Unexpected value: " + productType);
        }
    }

    private Optional<LocalDateTime> extractExpirationDate(final JWSTransactionDecodedPayload payload) {
        var expiredDate = payload.getExpiresDate();
        if (expiredDate == null) {
            return Optional.empty();
        }

        var instant = Instant.ofEpochMilli(expiredDate);
        var date = LocalDateTime.ofInstant(instant, ZoneOffset.UTC);
        return Optional.of(date);
    }


    static class TransactionComparator implements Comparator<JWSTransactionDecodedPayload> {
        @Override
        public int compare(JWSTransactionDecodedPayload a, JWSTransactionDecodedPayload b) {
            var aId = Long.parseLong(a.getTransactionId());
            var bId = Long.parseLong(b.getTransactionId());
            return Long.compare(aId, bId);
        }
    }
}


