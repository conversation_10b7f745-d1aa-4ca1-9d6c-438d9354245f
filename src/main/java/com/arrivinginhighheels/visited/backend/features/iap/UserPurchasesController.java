package com.arrivinginhighheels.visited.backend.features.iap;

import com.apple.itunes.storekit.client.APIException;
import com.apple.itunes.storekit.verification.VerificationException;
import com.arrivinginhighheels.visited.backend.dto.IAPReceiptDTO;
import com.arrivinginhighheels.visited.backend.dto.IAPPurchaseValidationDTO;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import lombok.SneakyThrows;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.io.IOException;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.RECEIPT_URL;

@RestController
@RequestMapping(path = RECEIPT_URL)
public class UserPurchasesController {

    private final UserPurchasesService purchaseService;
    private final LoggedInUserUtil loggedInUserUtil;
    private final ResponseHelper responseHelper;
    private final UserPurchasesService userPurchasesService;

    public UserPurchasesController(
            UserPurchasesService receiptService,
            LoggedInUserUtil loggedInUserUtil,
            ResponseHelper responseHelper, UserPurchasesService userPurchasesService) {
        this.purchaseService = receiptService;
        this.loggedInUserUtil = loggedInUserUtil;
        this.responseHelper = responseHelper;
        this.userPurchasesService = userPurchasesService;
    }

    @GetMapping(path = "/purchases")
    public ResponseEntity<List<String>> getCurrentPurchases(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        final var purchases = purchaseService.getPurchases(user);
        return responseHelper.doNotCacheResponse(purchases);
    }

    @GetMapping("/checkStoreConnection")
    public String checkStoreConnection(
            HttpServletRequest request) throws APIException, VerificationException, IOException, InterruptedException {
        final var platform = loggedInUserUtil.getCurrentOperatingSystem(request);
        return purchaseService.checkStoreConnection(platform);
    }

    @PostMapping(path = "/apple")
    public IAPPurchaseValidationDTO validateAppleIAPReceipt(
            @Valid @RequestBody IAPReceiptDTO receipt,
            HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        final var platform = loggedInUserUtil.getCurrentOperatingSystem(request);
        return purchaseService.validate(
                receipt,
                platform,
                user);
    }

    @PostMapping(path = "notification")
    public ResponseEntity<Void> handleAppStoreServerNotification(
            @RequestBody AppleNotificationDto notificationDto) {
        try {
            final var signedPayload = notificationDto.signedPayload();
            userPurchasesService.handleAppStoreNotification(signedPayload);
            return ResponseEntity.ok().build();
        } catch (VerificationException | IOException e) {
            return ResponseEntity.status(HttpStatus.NOT_ACCEPTABLE).build();
        }
    }

    @PostMapping(path = "/google_webhook")
    public ResponseEntity<Void> handleGooglePlayNotification(@RequestBody GoogleNotification notification) {
        final var message = notification.message().decodeDeveloperNotification();
        userPurchasesService.handleGooglePlayNotification(message);
        return ResponseEntity.ok().build();
    }

    @GetMapping(path = "notification/history")
    @SneakyThrows
    public ResponseEntity<Void> handleAppStoreServerNotification() {
        userPurchasesService.getNotificationHistory();
        return ResponseEntity.ok().build();
    }
}
