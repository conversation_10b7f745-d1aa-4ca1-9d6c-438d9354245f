package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.model.Inspiration;
import com.arrivinginhighheels.visited.backend.model.InspirationTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface InspirationTranslationRepository extends JpaRepository<InspirationTranslation, Long> {
    InspirationTranslation findByInspirationAndSupportedLanguage(Inspiration inspiration, SupportedLanguage supportedLanguage);

    List<InspirationTranslation> findBySupportedLanguageAndInspirationIdIn(SupportedLanguage supportedLanguage, List<Long> inspirationIds);
}
