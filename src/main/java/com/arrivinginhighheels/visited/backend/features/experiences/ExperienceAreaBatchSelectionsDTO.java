package com.arrivinginhighheels.visited.backend.features.experiences;

import java.util.ArrayList;
import java.util.List;

public class ExperienceAreaBatchSelectionsDTO {
    private List<ExperienceBatchSelectionDTO> batch = new ArrayList<>();

    public ExperienceAreaBatchSelectionsDTO(List<ExperienceBatchSelectionDTO> batch) {
        this.batch = batch;
    }

    public ExperienceAreaBatchSelectionsDTO() {
    }

    public List<ExperienceBatchSelectionDTO> getBatch() {
        return batch;
    }

    public void setBatch(List<ExperienceBatchSelectionDTO> batch) {
        this.batch = batch;
    }
}

