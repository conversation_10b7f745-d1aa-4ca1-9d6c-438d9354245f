package com.arrivinginhighheels.visited.backend.features.emails;

import lombok.Builder;
import lombok.Getter;
import org.json.JSONObject;
import org.springframework.http.HttpMethod;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Getter
@Builder
public class JsonRequest {
    /**
     * Initial capacity of the StringBuilder.
     */
    private static final int INIT_CAPACITY = 2048;

    private String host;
    private String path;
    private Map<String, String> headers;
    private JSONObject body;
    private HttpMethod method;
    JSONObject send() throws IOException {
        HttpURLConnection connection = null;
        try {
            final var url = new URL(host + "/" + path);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(method.name());
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("Accept", "application/json");

            if (headers != null) {
                headers.forEach(connection::setRequestProperty);
            }

            connection.setDoOutput(true);

            if (method == HttpMethod.POST || method == HttpMethod.PUT && body != null) {
                final var jsonInput = body.toString();
                try (var outputStream = connection.getOutputStream()) {
                    byte[] input = jsonInput.getBytes(StandardCharsets.UTF_8);
                    outputStream.write(input, 0, input.length);
                }
            }


            final var response = readHttpURLConnection(connection);
            return new JSONObject(response);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    static String readHttpURLConnection(HttpURLConnection conn) throws IOException {
            BufferedReader br = null;
        try {
            final var responseCode = conn.getResponseCode();
            if (100 <= responseCode && responseCode <= 399) {
                br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            } else {
                br = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
            }

            final StringBuilder builder = new StringBuilder(INIT_CAPACITY);
            for (String line; (line = br.readLine()) != null; ) {
                builder.append(line);
            }
            return builder.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (br != null) {
                br.close();
            }
        }
    }
}
