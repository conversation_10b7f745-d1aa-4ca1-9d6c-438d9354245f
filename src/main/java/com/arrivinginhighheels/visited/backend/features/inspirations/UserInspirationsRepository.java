package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserInspirationsRepository extends JpaRepository<UserInspiration, Long> {
    void deleteByUser(User user);

    List<UserInspiration> findAllByUser(User user);

    UserInspiration findByUserAndInspiration(User user, Inspiration inspiration);

    List<UserInspiration> findAllByUserAndType(User user, InspirationSelectionType selectionType);

    long countByUser(User user);

    @Query(value = """
      SELECT new com.arrivinginhighheels.visited.backend.features.inspirations.LocalizedInspirationPrep(u.inspiration, t, area)\s
      FROM UserInspiration u\s
      INNER JOIN InspirationTranslation t ON u.inspiration.id = t.inspiration.id
      INNER JOIN GeoAreaTranslation area ON u.inspiration.geoArea = area.geoArea
      WHERE (u.inspiration.geoArea = ?2 OR u.inspiration.geoArea.parent = ?2)
        AND u.user = ?1
        AND u.type = 'WANT'
        AND t.supportedLanguage = ?3
        AND area.supportedLanguage = ?3
    """)
    List<LocalizedInspirationPrep> findWantInspirationsByUserAreaAndLanguage(User user, GeoArea area, SupportedLanguage lang);
    @Query(value = """
      SELECT u.inspiration
      FROM UserInspiration u
      WHERE (u.inspiration.geoArea = ?2 or u.inspiration.geoArea.parent = ?2)
        AND u.user = ?1
        AND u.type = 'WANT'
    """)
    List<Inspiration> findWantInspirationsByUserAndArea(User user, GeoArea area);
}
