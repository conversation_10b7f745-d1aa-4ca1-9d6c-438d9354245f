package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.dto.MapType;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.vladmihalcea.hibernate.type.array.StringArrayType;
import com.vladmihalcea.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.lang.Nullable;

import java.util.Date;
import java.util.Map;

@Entity
@Table(name = "printed_map_orders")
@Getter @Setter @NoArgsConstructor

public class PrintingOrder {

    @SequenceGenerator(name = "printing_order_id_seq", sequenceName = "printing_order_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "printing_order_id_seq")
    private @Id Long id;

    @ManyToOne
    @JoinColumn(unique = true)
    private User user;

    @Enumerated(EnumType.STRING)
    private MapType mapType;

    @Type(StringArrayType.class)
    @Enumerated(EnumType.STRING)
    @Column(columnDefinition = "character varying(16)[]")
    private SelectionType[] selections;

    @Type(JsonType.class)
    private Map<String, Object> mapData;

    @Type(JsonType.class)
    private Map<String, Object> disputedTerritories;

    @Type(JsonType.class)
    private Map<String, Object> shippingAddress;

    @Type(JsonType.class)
    private Map<String, Object> palette;

    @ManyToOne
    @JoinColumn(name = "language_id")
    private SupportedLanguage language;

    private String paymentIntentId;

    @Nullable
    private String chargeId;

    private Date orderReceived;

    private Date submittedToPrinter;

    @CreationTimestamp
    private Date createdAt;

    @UpdateTimestamp
    private Date lastModified;

    @Enumerated(EnumType.STRING)
    private PaymentStatus status;


}
