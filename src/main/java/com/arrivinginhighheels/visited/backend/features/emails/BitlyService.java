package com.arrivinginhighheels.visited.backend.features.emails;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import org.json.JSONObject;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class BitlyService {
    private final YamlConfig config;

    private static final String BITLY_HOST = "https://api-ssl.bitly.com/v4";

    final
    TrackedLinkRepository repository;

    public BitlyService(YamlConfig config, TrackedLinkRepository repository) {
        this.config = config;
        this.repository = repository;
    }

    public Optional<TrackedLink> findLink(String url) {
        Logger.getGlobal().log(Level.INFO, "Looking for tracking link: " + url);
        try  {
            var results = repository.findByUrl(url);
            if (results.isEmpty()) {
                Logger.getGlobal().log(Level.INFO, "Cannot find tracking link: " + url);
                return results;
            }

            return results;
        } catch (Exception e) {
            Logger.getGlobal().log(Level.INFO, "Cannot find tracking link: " + url);
            return Optional.empty();
        }
    }

    public Optional<TrackedLink> createBitlyLink(String url, String title)  {
        final var logger = Logger.getGlobal();
        logger.info("Creating BITLY Link for:" + url);

        try {
            final var response = new JsonRequest.JsonRequestBuilder()
                    .method(HttpMethod.POST)
                    .host(BITLY_HOST)
                    .path("bitlinks")
                    .headers(Map.of("Authorization", "Bearer " + config.getBitlyApiKey()))
                    .body(new JSONObject(Map.of(
                            "long_url", url,
                            "title", title,
                            "domain", "bit.ly",
                            "group_guid", config.getBitlyGroupGuid()

                    )))
                    .build()
                    .send();

            final var bitlyUrl = response.getString("link");

            final var link = new TrackedLink();
            link.setUrl(url);
            link.setTitle(title);
            link.setTrackedUrl(bitlyUrl);
            repository.save(link);

            logger.info("Bitly Success: " + url + " -> " + bitlyUrl);
            return Optional.of(link);

        } catch (IOException e) {
            logger.severe("Bitly Failed: " + url + " " + e.getMessage());
            logger.severe(Arrays.toString(e.getStackTrace()));
            return Optional.empty();
        }
    }
}

