//package com.arrivinginhighheels.visited.backend.features.emails;
//
//import lombok.extern.slf4j.Slf4j;
//import org.json.JSONObject;
//import org.springframework.cloud.aws.messaging.config.annotation.NotificationMessage;
//import org.springframework.cloud.aws.messaging.config.annotation.NotificationSubject;
//import org.springframework.cloud.aws.messaging.endpoint.NotificationStatus;
//import org.springframework.cloud.aws.messaging.endpoint.annotation.NotificationMessageMapping;
//import org.springframework.cloud.aws.messaging.endpoint.annotation.NotificationSubscriptionMapping;
//import org.springframework.cloud.aws.messaging.endpoint.annotation.NotificationUnsubscribeConfirmationMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SNS_URL;
//
//@RestController
//@Slf4j
//@RequestMapping(SNS_URL)
//public class EmailFailureSNSListener {
//    private final EmailService emailService;
//
//    public EmailFailureSNSListener(EmailService emailService) {
//        this.emailService = emailService;
//    }
//
//    @NotificationMessageMapping
//    public void receiveNotification(@NotificationMessage String message,
//                                    @NotificationSubject String subject) {
//
//        log.info("receiveNotification -> Message :{}, Subject:{}",message,subject);
//
//        final var json = new JSONObject(message);
//        final var type = json.getString("notificationType");
//
//        if (!(type.equals("Bounce") || type.equals("Complaint"))) {
//            return;
//        }
//
//        if (!json.has("mail")) {
//            return;
//        }
//
//        final var mail = json.getJSONObject("mail");
//
//        if (!mail.has("destination")) {
//            return;
//        }
//
//        final var emails = mail.getJSONArray("destination");
//        for (int i = 0; i < emails.length(); i++) {
//          final var email = emails.getString(i);
//          emailService.unsubscribeUser(email);
//        }
//
//    }
//
//    @NotificationUnsubscribeConfirmationMapping
//    public void confirmUnsubscribeMessage(NotificationStatus notificationStatus) {
//        log.info("confirmUnsubscribeMessage -> Request received");
//        notificationStatus.confirmSubscription();
//        log.info("confirmUnsubscribeMessage -> UnSubscribe confirmed");
//    }
//
//    @NotificationSubscriptionMapping
//    public void confirmSubscriptionMessage(NotificationStatus notificationStatus) {
//        log.info("confirmSubscriptionMessage -> Request received");
//        notificationStatus.confirmSubscription();
//        log.info("confirmSubscriptionMessage -> Subscribe confirmed");
//    }
//}
