package com.arrivinginhighheels.visited.backend.features.cities;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record UserCitiesDTO(CityDto live, List<CityDto> been, List<CityDto> want) {
    public UserCitiesDTO(final CityDto live, final List<CityDto> been, final List<CityDto> want) {
        this.live = live;
        this.been = been.isEmpty() ? null : been;
        this.want = want.isEmpty() ? null : want;
    }
}
