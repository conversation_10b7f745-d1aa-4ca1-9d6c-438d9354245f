package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "todo_list_popularities")
public class TodoListPopularity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "list_id")
    private TodoList todoList;

    @ManyToOne
    @JoinColumn(name = "list_item_id")
    private TodoListItem todoListItem;

    @ManyToOne
    @JoinColumn(name = "adaptive_image_id")
    private AdaptiveImage thumbnail;

    private Integer count;
}
