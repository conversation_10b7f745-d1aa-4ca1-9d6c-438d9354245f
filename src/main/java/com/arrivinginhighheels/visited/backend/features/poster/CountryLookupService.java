package com.arrivinginhighheels.visited.backend.features.poster;

import com.maxmind.geoip2.DatabaseReader;
import lombok.SneakyThrows;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.net.Inet4Address;

@Service
public class CountryLookupService {

    private final DatabaseReader dbReader;

    @SneakyThrows
    CountryLookupService(ResourceLoader resourceLoader) {
        var resource = resourceLoader.getResource("classpath:GeoLite2-Country.mmdb");
        var stream = resource.getInputStream();
        dbReader = new DatabaseReader.Builder(stream).build();
    }

    @SneakyThrows
    String findCountry(String ipAddress) {
        var inetAddress = Inet4Address.getByName(ipAddress);
        var response = dbReader.country(inetAddress);
        return response.getCountry().getIsoCode();
    }
}
