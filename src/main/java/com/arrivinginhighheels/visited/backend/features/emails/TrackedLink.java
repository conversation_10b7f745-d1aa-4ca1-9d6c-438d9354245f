package com.arrivinginhighheels.visited.backend.features.emails;

import lombok.Getter;

import jakarta.persistence.*;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@Entity
@Table(name = "tracked_links")
public class TrackedLink {
    @Id
    @SequenceGenerator(name = "tracked_links_id_seq", sequenceName = "tracked_links_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tracked_links_id_seq")
    private Long id;

    @Column(name = "url")
    private String url;

    @Column(name = "title")
    private String title;

    @Deprecated(forRemoval = true)
    @Column(name = "tracked_url")
    private String trackedUrl;

    @Column(name = "hash")
    private String hash;

    public TrackedLink() {
    }


    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TrackedLink that = (TrackedLink) o;
        return Objects.equals(id, that.id) && Objects.equals(url, that.url) && Objects.equals(title, that.title)
                && Objects.equals(trackedUrl, that.trackedUrl);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getId());
        result = 31 * result + Objects.hashCode(getUrl());
        result = 31 * result + Objects.hashCode(getTitle());
        result = 31 * result + Objects.hashCode(getTrackedUrl());
        result = 31 * result + Objects.hashCode(getHash());
        return result;
    }

    @Override
    public String toString() {
        return "TrackedLink{" +
                "id=" + id +
                ", url='" + url + '\'' +
                ", title='" + title + '\'' +
                ", trackedUrl='" + trackedUrl + '\'' +
                ", hash='" + hash + '\'' +
                '}';
    }
}
