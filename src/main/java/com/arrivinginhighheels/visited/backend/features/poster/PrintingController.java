package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.dto.PrintedOrderDto;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.PRINTING_URL;

@RestController
@RequestMapping(path = PRINTING_URL)
public class PrintingController {

    private final PrintingService printingService;

    public PrintingController(PrintingService printingService) {
        this.printingService = printingService;
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @PostMapping(path = "/{orderId}/submit", produces = "application/json")
    public ResponseEntity<Object> submitOrderToPrinter(
            @PathVariable Long orderId) {

        printingService.confirmOrderHasBeenSentToPrinter(orderId);
        return ResponseEntity.ok("{\"success\": true, \"message\": \"Order Submitted to Printer\"}");
    }

    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @GetMapping(path = "/orders", produces = "application/json")
    public List<PrintedOrderDto> getPendingOrders() {
        return printingService.getPendingOrders();
    }
}
