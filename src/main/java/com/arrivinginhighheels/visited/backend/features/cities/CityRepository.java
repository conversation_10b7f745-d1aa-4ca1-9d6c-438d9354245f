package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.City;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface CityRepository extends JpaRepository<City, Long> {
    List<City> findAllByGeoAreaLevelOne(GeoArea area);

    List<City> findAllByIdIn(List<Long> cityIds);

    @Query("select max(c.lastModificationTime) from City c where c.geoAreaLevelOne = ?1")
    Date getMaxModificationTimeOfCitiesByArea(GeoArea geaArea);

    @Query(value = """
        select * from cities city 
        where ?1 % ANY(STRING_TO_ARRAY(city.name, ' '))
        order by name <-> ?1 ASC
        limit 20
    """, nativeQuery = true)
    List<City> fuzzySearchByName(String title);

    @Query(value = """
        select
            city.id,
            translation.name,
            city.lat,
            city.long,
            area.iso_key,
            city.level_two_geo_area_id
        from
            cities city
        INNER join city_translations translation on city.id = translation.city_id
        INNER join geoareas area on city.level_one_geo_area_id = area.id
        where
            translation.supported_language_id = ?2 AND
            ?1 % ANY(STRING_TO_ARRAY(translation.name, ' '))
        order by
            translation.name <-> ?1 ASC
        limit
            20
            """, nativeQuery = true)
    List<Object[]> fuzzySearchByNameAndLanguage(String title, Long langId);
}

