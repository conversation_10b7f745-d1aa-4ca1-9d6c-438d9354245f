package com.arrivinginhighheels.visited.backend.features.poster;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter @Setter @NoArgsConstructor
public class ProductPurchasingInformation {
    private String id;
    private String name;
    private String imageUrl;
    private PriceDto price;
    private PriceDto usdAlternativePrice;
    private PriceDto eurAlternativePrice;

    private List<ShippingRateDto> shippingOptions;
    private List<ShippingRateDto> usdAlternativeShippingOptions;
    private List<ShippingRateDto> eurAlternativePriceShippingOptions;
}

