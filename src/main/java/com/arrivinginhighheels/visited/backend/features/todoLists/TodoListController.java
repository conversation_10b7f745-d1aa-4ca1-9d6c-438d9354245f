package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.OSType;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Locale;
import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.LISTS_URL;

@RestController
@RequestMapping(path = LISTS_URL)
public class TodoListController {

    private final TodoListService listService;

    private final LoggedInUserUtil loggedInUserUtil;

    private final ResponseHelper responseHelper;

    public TodoListController(TodoListService listService, LoggedInUserUtil loggedInUserUtil, ResponseHelper responseHelper) {
        this.listService = listService;
        this.loggedInUserUtil = loggedInUserUtil;
        this.responseHelper = responseHelper;
    }

    @GetMapping(produces = "application/json")
    public ResponseEntity<List<TodoListDTO>> getAllLists(
            HttpServletRequest request,
            @RequestHeader(value = "x-app-platform") String platform,
            @RequestHeader(value = "x-app-density", required = false) Double resolution,
            @RequestHeader(value = "x-app-version") String version
    ) {
        final var os = parseOperatingSystem(platform);
        final double actualResolution = resolution == null ? 1.0 : resolution;
        final var lastModified = listService.getLastModifiedTimeForLists();

        return responseHelper.wrap(
                request,
                lastModified,
                ResponseHelper.standardCacheLength,
                (r) ->  listService.getAll(actualResolution, os, version));
    }

    @GetMapping(path = "areas")
    public ResponseEntity<List<TodoListDTO>> getAllListsByCountry(
            @RequestHeader(value = "x-app-density", required = false) Double resolution
    ) {
        final double actualResolution = resolution == null ? 1.0 : resolution;
        return responseHelper.standardCacheableResponse(listService.getAllByCountry(actualResolution));
    }

    @GetMapping(path = "areas/{isoCode}")
    public ResponseEntity<List<TodoListItemDTO>> getAreaListDetails(
            @PathVariable String isoCode,
            @RequestParam Optional<TodoListType> filter,
            @RequestHeader(value = "x-app-density", required = false) Double resolution
    ) {
        final double actualResolution = resolution == null ? 1.0 : resolution;
        return responseHelper.standardCacheableResponse(listService.getAreaTodoListItems(isoCode, actualResolution, filter));
    }

    @GetMapping(path = "selections/v2/areas/{isoCode}", produces = "application/json")
    public ResponseEntity<List<TodoAreaListSelectionDTO>> getAreaListSelections(
            HttpServletRequest request,
            @PathVariable String isoCode) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var selections = listService.getAreaListSelections(isoCode, user);
        return responseHelper.doNotCacheResponse(selections);
    }

    @GetMapping(path = "tags")
    public ResponseEntity<List<TodoListTagDto>> getListTags() {
        var tags = listService.getTags();
        return responseHelper.standardCacheableResponse(tags);
    }

    @GetMapping(path = "{listId}",  produces = "application/json")
    public ResponseEntity<TodoListDetailsDto> getListDetails(
            HttpServletRequest request,
            @PathVariable Long listId,
            @RequestParam(required = false) Double resolution,
            @RequestHeader(value = "x-app-platform") String platform
    ) {
        final var os = parseOperatingSystem(platform);
        final var actualResolution = resolution == null ? 1.0 : resolution;
        final var date = listService.getListLastModifiedTime(listId);
        return responseHelper.wrap(
                request,
                date,
                ResponseHelper.standardCacheLength,
                (r) -> listService.getDetails(listId, actualResolution, os));
    }

    @GetMapping(path = "selections/{listId}", produces = "application/json")
    @Deprecated
    public ResponseEntity<List<Long>> getSelections(HttpServletRequest request, @PathVariable Long listId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var  dto = listService.getSelectionsForList(listId, user);
        var been = dto.getSelections().get(SelectionType.BEEN);
        return responseHelper.doNotCacheResponse(been);
    }

    @GetMapping(path = "selections/v2/{listId}", produces = "application/json")
    public ResponseEntity<TodoListSelectionDTO> getSelectionsWithSelectionType(HttpServletRequest request, @PathVariable Long listId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var selections = listService.getSelectionsForList(listId, user);
        return responseHelper.doNotCacheResponse(selections);
    }

    @GetMapping(path = "selections/summary", produces = "application/json")
    public ResponseEntity<List<TodoListSummaryDto>> getSelectionSummary(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var summary =  listService.getSelectionSummary(user);
        return responseHelper.doNotCacheResponse(summary);
    }

    @GetMapping(path = "selections/areas/summaries", produces = "application/json")
    public ResponseEntity<List<TodoListSummaryDto>> getAreaSelectionSummary(HttpServletRequest request) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var summary =  listService.getAreaSelectionSummary(user);
        return responseHelper.doNotCacheResponse(summary);
    }

    @GetMapping(path = "{listId}/select", produces = "application/json")
    @Deprecated
    public ResponseEntity<List<Long>> getListSelections(HttpServletRequest request, @PathVariable Long listId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        var selections =  listService.getListSelections(user, listId);
        return responseHelper.doNotCacheResponse(selections);
    }

    @PostMapping(path = "{listId}/select/{listItemId}", produces = "application/json")
    public List<Long> makeListSelection(
            HttpServletRequest request,
            @PathVariable Long listId,
            @PathVariable Long listItemId,
            @RequestParam(defaultValue = "BEEN") SelectionType type) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        return listService.selectListItem(user, listId, listItemId, type);
    }

    @DeleteMapping(path = "{listId}/select/{listItemId}", produces = "application/json")
    public List<Long> deleteListSelection(
            HttpServletRequest request,
            @PathVariable Long listId,
            @PathVariable Long listItemId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        return listService.unselectListItem(user, listId, listItemId);
    }

    @RequestMapping(path = "select/{listItemId}", method = RequestMethod.POST, produces = "application/json")
    @Deprecated
    public Boolean selectItem(HttpServletRequest request, @PathVariable Long listItemId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        return listService.selectItemById(listItemId, user);
    }

    @RequestMapping(path = "select/{listItemId}", method = RequestMethod.DELETE, produces = "application/json")
    @Deprecated
    public Boolean unselectItem(HttpServletRequest request, @PathVariable Long listItemId) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        return listService.unselectItemById(listItemId, user);
    }

    @GetMapping(path = "top1000")
    public ResponseEntity<List<TopOneThousandListDto>> getTopOneThousandList(
            HttpServletRequest request,
            @RequestHeader(value = "x-app-density", required = false) Double resolution
    ) {
        final var user = loggedInUserUtil.getLoggedInUser(request);
        final double actualResolution = resolution == null ? 1.0 : resolution;
        final var topItems = listService.getTopOneThousandList(user, actualResolution);
        return responseHelper.doNotCacheResponse(topItems);
    }

    @GetMapping(path = "sort")
    public Boolean updateOrder() {
        listService.updateListAndItemOrderByPopularity();
        return true;
    }

    private OSType parseOperatingSystem(String operatingSystem) {
        if (operatingSystem == null) {
            return OSType.WEB;
        }

        final var os = operatingSystem.toLowerCase(Locale.ROOT);

        if (os.equals("ios")) {
            return OSType.iOS;
        } else if (os.equals("android")) {
            return OSType.Android;
        } else {
            return OSType.WEB;
        }
    }
}
