package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.AddressDto;
import com.arrivinginhighheels.visited.backend.dto.PaletteDto;
import com.arrivinginhighheels.visited.backend.dto.PrintedOrderDto;
import com.arrivinginhighheels.visited.backend.dto.PrintingRequest;
import com.arrivinginhighheels.visited.backend.features.emails.EmailService;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.service.SelectionService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;

import jakarta.ws.rs.NotFoundException;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PrintingService {

    private final SelectionService selectionService;

    private final PrintingRepository repository;

    private final EmailService emailService;

    private final YamlConfig config;

    private final PrintingTranslationService translationService;

    public PrintingService(SelectionService selectionService, PrintingRepository repository, EmailService emailService, YamlConfig config, PrintingTranslationService translationService) {
        this.selectionService = selectionService;
        this.repository = repository;
        this.emailService = emailService;
        this.config = config;
        this.translationService = translationService;
    }

    public Boolean submitOrder(
            User user,
            String paymentIntentId,
            PrintingRequest printingRequest) {
        final var printingWorkOrder = new PrintingOrder();

        printingWorkOrder.setUser(user);
        printingWorkOrder.setMapType(printingRequest.getMapType());

        var language = translationService.getCurrentLanguage();
        printingWorkOrder.setLanguage(language);

        var providedSelections = printingRequest.getSelections();
        if (providedSelections.isPresent()) {
            printingWorkOrder.setMapData(providedSelections.get());
        } else {
            final var selections = selectionService.getUserSelections(user);
            printingWorkOrder.setMapData(Map.of(
                    "LIVE", selections.getLivedAreas(),
                    "BEEN", selections.getBeenAreas(),
                    "WANT", selections.getWantAreas(),
                    "LIVED", selections.getPastLivedAreas()
            ));
        }

        final var selectionsTypesList = printingRequest.getSelectionTypes();
        final var selectionsTypesArray = new SelectionType[selectionsTypesList.size()];
        for (int i = 0; i < selectionsTypesList.size(); i++) selectionsTypesArray[i] = selectionsTypesList.get(i);

        printingWorkOrder.setSelections(selectionsTypesArray);
        printingWorkOrder.setDisputedTerritories(printingRequest.getDisputedTerritoryPreferences());
        printingWorkOrder.setSubmittedToPrinter(null);
        printingWorkOrder.setChargeId(null);
        printingWorkOrder.setPaymentIntentId(paymentIntentId);
        printingWorkOrder.setStatus(PaymentStatus.pending);

        final var mapper = new ObjectMapper();
        var address = printingRequest.getShippingAddress();
        var shippingAddressJson = (Map<String, Object>) mapper.convertValue(address, Map.class);
        printingWorkOrder.setShippingAddress(shippingAddressJson);

        final var paletteDto = printingRequest.getPalette();
        final var paletteJson = (Map<String, Object>)mapper.convertValue(paletteDto, Map.class);
        printingWorkOrder.setPalette(paletteJson);

        repository.save(printingWorkOrder);

        return true;
    }

    public void confirmPayment(String intentId, String chargeId) {
        var maybeOrder = repository.findFirstByPaymentIntentId(intentId);
        if (maybeOrder.isEmpty()) {
            return;
        }

        var order = maybeOrder.get();
        order.setChargeId(chargeId);
        order.setStatus(PaymentStatus.succeeded);
        repository.save(order);

        emailService.sendSimpleMessage(
                Optional.empty(),
                Optional.empty(),
                "<EMAIL>",
                "New Printing Order " + order.getId() + " Submitted",
                "A new order has been submitted. \n\nPlease log into the visited admin console to fulfill it.",
                false);
    }

    public void updateOrderStatus(String intentId, PaymentStatus status) {
        var maybeOrder = repository.findFirstByPaymentIntentId(intentId);
        if (maybeOrder.isEmpty()) {
            return;
        }

        var order = maybeOrder.get();
        order.setStatus(status);
        repository.save(order);

    }

    public void confirmOrderHasBeenSentToPrinter(Long orderId) {
        final var maybeOrder = repository.findById(orderId);
        final var order = maybeOrder.orElseThrow(NotFoundException::new);

        if (order.getSubmittedToPrinter() != null) {
            throw new RuntimeException("Order " + orderId + " has already been sent to the printer");
        }

        order.setSubmittedToPrinter(new Date(System.currentTimeMillis()));

        repository.save(order);

        emailService.sendSimpleMessage(
                Optional.empty(),
                Optional.empty(),
                config.getEmailAddress(),
                "Order " + orderId + " flagged as complete",
                "This order has been marked as complete.  Please confirm that the image and shipping address has been sent to Pikto. \n\n" + order.getShippingAddress().toString(),
                false);
    }

    public List<PrintedOrderDto> getPendingOrders() {
        final var orders = repository.findAllBySubmittedToPrinterIsNullAndChargeIdIsNotNull();
        final var mapper = new ObjectMapper();
        return orders.stream().map(e -> {
            try {
                final var dto = new PrintedOrderDto();
                dto.setOrderId(e.getId());
                dto.setUser(e.getUser().getUsername());
                dto.setType(e.getMapType());
                dto.setUseSelections(Arrays.stream(e.getSelections()).toList());
                dto.setData(e.getMapData());
                dto.setDisputedTerritories(e.getDisputedTerritories());
                dto.setLanguageId(e.getLanguage().getId());

                final var paletteJson = e.getPalette();
                final var palette = mapper.convertValue(paletteJson, PaletteDto.class);
                dto.setPalette(palette);

                final var shippingJson = e.getShippingAddress();
                final var shipping = mapper.convertValue(shippingJson, AddressDto.class);
                dto.setShippingAddress(shipping);

                return dto;
            } catch (Exception exception) {
                throw exception;
            }
        }).collect(Collectors.toList());
    }
}
