package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.UserPreferredExperience;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface UserExperienceRepository extends JpaRepository<UserPreferredExperience, Long> {
    @Transactional
    void deleteByUser(User user);

    List<UserPreferredExperience> findAllByUser(User user);
    UserPreferredExperience findByUserAndExperience(User user, Experience experience);

}
