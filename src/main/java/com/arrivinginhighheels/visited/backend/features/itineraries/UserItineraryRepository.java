package com.arrivinginhighheels.visited.backend.features.itineraries;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.UserItinerary;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface UserItineraryRepository extends JpaRepository<UserItinerary, Long> {

    Optional<UserItinerary> findByUserAndGeoArea(User user, GeoArea geoArea);

    void deleteAllByUser(User user);
}
