package com.arrivinginhighheels.visited.backend.features.iap;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.Getter;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.features.iap.GoogleNotification.OneTimeProductNotificationType.ONE_TIME_PRODUCT_CANCELED;

@Service
public class PlayStoreClient {
    private final AndroidPublisher publisher;
    private final YamlConfig yamlConfig;

    public PlayStoreClient(ResourceLoader resourceLoader, YamlConfig yamlConfig) throws Exception {
        this.yamlConfig = yamlConfig;

        final var resource = resourceLoader.getResource(yamlConfig.getGooglePlayKeyPath());
        try (InputStream stream = new FileInputStream(resource.getFile())) {

            final var credentials = GoogleCredentials.fromStream(stream)
                    .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));

            publisher = new AndroidPublisher.Builder(
                    GoogleNetHttpTransport.newTrustedTransport(),
                    GsonFactory.getDefaultInstance(),
                    new HttpCredentialsAdapter(credentials))
                .setApplicationName(yamlConfig.getGooglePlayApplicationName())
                .build();
            }
    }

    public ValidatedPurchase verifyPurchase(String productId, String purchaseId, String receiptData) throws Exception {
        if (productId.contains("subscription")) {
            return validateSubscriptionPurchase(productId, receiptData);
        } else {
            return validateNonConsumablePurchase(productId, receiptData);
        }
    }

    private ValidatedPurchase validateSubscriptionPurchase(String productId, String receiptData) throws IOException {
        final var type = UserPurchaseProductType.AUTO_RENEWABLE;
        final var subscription = publisher.purchases()
            .subscriptions()
            .get(yamlConfig.getGooglePlayPackageName(), productId, receiptData)
                .execute();

        final var orderId = subscription.getOrderId();
        final var expirationDate = LocalDateTime.ofInstant(Instant.ofEpochMilli(subscription.getExpiryTimeMillis()), ZoneOffset.UTC);
        final var  autoRenew = subscription.getAutoRenewing();

        UserPurchaseStatus status;
        if (expirationDate.isBefore(LocalDateTime.now())) {
            status = UserPurchaseStatus.EXPIRED;
        } else {
            final var cancelledReason = subscription.getCancelReason();
            if (cancelledReason == null) {
                status = UserPurchaseStatus.ACTIVE;
            } else {
                final var reason = SubscriptionCancelReason.values()[cancelledReason];
                switch (reason) {
                    case USER_CANCELED, SYSTEM_CANCELED, DEVELOPER_CANCELED -> status = UserPurchaseStatus.REFUNDED;
                    default -> status = UserPurchaseStatus.ACTIVE;
                }
            }
        }

        return new ValidatedPurchase(
                productId,
                orderId,
                status,
                type,
                Optional.of(expirationDate),
                autoRenew
        );
    }

    private ValidatedPurchase validateNonConsumablePurchase(String productId, String receiptData) throws IOException {
        final var type = UserPurchaseProductType.NON_CONSUMABLE;
        final var purchase = publisher.purchases()
                .products()
                .get(yamlConfig.getGooglePlayPackageName(), productId, receiptData)
                .execute();

        final var orderId = purchase.getOrderId();
        final var state = PurchaseState.values()[purchase.getPurchaseState()];
        final var status = switch (state) {
            case PURCHASED, PENDING -> UserPurchaseStatus.ACTIVE;
            case CANCELED -> UserPurchaseStatus.REFUNDED;
        };

        return new ValidatedPurchase(
                productId,
                orderId,
                status,
                type,
                Optional.empty(),
                false
        );
    }

    //TODO: Do I need this.
    public Optional<ValidatedPurchase> findPurchase(GoogleNotification.OneTimeProductNotification nonConsumable) throws IOException {
        if (nonConsumable.notificationType() == ONE_TIME_PRODUCT_CANCELED) {
            final var purchase = publisher
                    .purchases()
                    .products()
                    .get(yamlConfig.getGooglePlayPackageName(), nonConsumable.sku(), nonConsumable.purchaseToken())
                    .execute();

            return Optional.of(new ValidatedPurchase(
                    nonConsumable.sku(),
                    purchase.getOrderId(),
                    UserPurchaseStatus.REFUNDED,
                    UserPurchaseProductType.NON_CONSUMABLE,
                    Optional.empty(),
                    false
            ));
        }

        return Optional.empty();
    }

    public Optional<SubscriptionPurchase> findSubscriptionPurchase(GoogleNotification.SubscriptionNotification subscription) {
        try {
            final var purchase = publisher
                    .purchases()
                    .subscriptions()
                    .get(yamlConfig.getGooglePlayPackageName(), subscription.subscriptionId(), subscription.purchaseToken())
                    .execute();

            return Optional.of(purchase);
        } catch (Exception e) {
            return Optional.empty();
        }
    }


    @Getter
    private enum PurchaseState {
        PURCHASED(0),
        CANCELED(1),
        PENDING(2);

        private final int value;

        PurchaseState(int value) {
            this.value = value;
        }

    }


    @Getter
    private enum SubscriptionCancelReason {
        USER_CANCELED(0),
        SYSTEM_CANCELED(1),
        REPLACED(2),
        DEVELOPER_CANCELED(3),
        UNKNOWN(-1);

        private final int value;

        SubscriptionCancelReason(int value) {
            this.value = value;
        }

    }
}
