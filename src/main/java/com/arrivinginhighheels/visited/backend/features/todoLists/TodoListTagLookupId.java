package com.arrivinginhighheels.visited.backend.features.todoLists;

import java.io.Serializable;
import java.util.Objects;

public class TodoListTagLookupId implements Serializable {
    private Long listId;
    private Long tagId;

    public Long getListId() {
        return listId;
    }

    public void setListId(Long listId) {
        this.listId = listId;
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TodoListTagLookupId that = (TodoListTagLookupId) o;
        return Objects.equals(listId, that.listId) && Objects.equals(tagId, that.tagId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(listId, tagId);
    }
}
