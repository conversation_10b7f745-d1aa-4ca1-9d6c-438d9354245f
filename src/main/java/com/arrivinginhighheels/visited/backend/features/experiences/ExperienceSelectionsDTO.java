package com.arrivinginhighheels.visited.backend.features.experiences;

import java.util.ArrayList;
import java.util.List;

public class ExperienceSelectionsDTO {
    private ExperienceDTO experience;
    private List<String> been;
    private List<String> want;
    private Boolean preferred;

    public ExperienceSelectionsDTO() {
        preferred = false;
        been = new ArrayList<>();
        want = new ArrayList<>();
    }

    public ExperienceSelectionsDTO setExperience(ExperienceDTO experience) {
        this.experience = experience;
        return this;
    }

    public ExperienceDTO getExperience() {
        return experience;
    }

    public List<String> getBeen() {
        return been;
    }

    public void setBeen(List<String> been) {
        this.been = been;
    }

    public List<String> getWant() {
        return want;
    }

    public void setWant(List<String> want) {
        this.want = want;
    }

    public Boolean getPreferred() {
        return preferred;
    }

    public ExperienceSelectionsDTO setPreferred(<PERSON><PERSON><PERSON> preferred) {
        this.preferred = preferred;
        return this;
    }

    @Override
    public String toString() {
        return "ExperienceSelectionsDTO{" +
                "experience=" + experience +
                ", been=" + been +
                ", want=" + want +
                ", preferred=" + preferred +
                '}';
    }
}
