package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.model.Platform;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Represents the information for the login request
 */
@Getter
@Setter
@NoArgsConstructor
public class AuthRequest implements Serializable {

    @NotNull
    @Email
    private String email;

    @Nullable
    private String password;

    @NotNull
    private Platform platform;

    public AuthRequest(String email, Platform platform) {
        this.email = email;
        this.platform = platform;
    }
}
