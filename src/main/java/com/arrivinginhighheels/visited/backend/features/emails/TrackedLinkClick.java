package com.arrivinginhighheels.visited.backend.features.emails;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

import java.net.InetAddress;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "tracked_link_clicks")
public class TrackedLinkClick {
    @Id
    @SequenceGenerator(name = "tracked_link_clicks_seq", sequenceName = "tracked_link_clicks_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tracked_link_clicks_seq")
    @Column(name = "id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "link")
    private TrackedLink link;

    @NotNull
    @Column(name = "ip_address", nullable = false)
    private InetAddress ipAddress;

    @ColumnDefault("CURRENT_TIMESTAMP")
    @Column(name = "\"timestamp\"")
    private Instant timestamp;

}
