package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TodoListPopularityRepository extends JpaRepository<TodoListPopularity, Long> {


  @Query("""
    select new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoPopularity(
        popularity, 
        popularity.todoListItem.name, 
        userItem.type
    )
    from TodoListPopularity popularity
    left join UserTodoListItem userItem 
        ON userItem.listItem = popularity.todoListItem 
       AND userItem.list = popularity.todoList 
       AND userItem.user = ?1
    order by popularity.count desc
""")
  List<LocalizedTodoPopularity> findAllByOrderByCountDesc(User user);

  @Query("""
    select new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoPopularity(
        popularity,
        translation.name, 
        userItem.type
    )
    from TodoListPopularity popularity
    inner join TodoListItemTranslation translation 
        on popularity.todoListItem = translation.listItem 
       and translation.supportedLanguage = ?2
    left join UserTodoListItem userItem 
        on userItem.listItem = popularity.todoListItem 
       and userItem.list = popularity.todoList 
       and userItem.user = ?1
    order by popularity.count desc
""")
  List<LocalizedTodoPopularity> findAllByOrderByCountDescLocalized(User user, SupportedLanguage language);
}
