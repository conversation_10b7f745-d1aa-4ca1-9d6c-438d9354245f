package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.City;
import com.arrivinginhighheels.visited.backend.model.CityTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface CityTranslationRepository extends JpaRepository<CityTranslation, Long> {
    Optional<CityTranslation> findByCityAndSupportedLanguage(City city, SupportedLanguage supportedLanguage);

    List<CityTranslation> findBySupportedLanguageAndCityIdIn(SupportedLanguage supportedLanguage, List<Long> cityIds);
}
