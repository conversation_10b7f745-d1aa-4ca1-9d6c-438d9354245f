package com.arrivinginhighheels.visited.backend.features.iap;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Base64;
import java.util.Optional;

public record GoogleNotification(
        GoogleMessage message,
        String subscription,
        String publishTime
) {
    public record GoogleMessage(
            String data,
            String messageId,
            String publishTime) {

        DeveloperNotification decodeDeveloperNotification() {
            try {
                final var decodedBytes = Base64.getDecoder().decode(data);
                final var json = new String(decodedBytes, StandardCharsets.UTF_8);

                final var objectMapper = new ObjectMapper()
                        .registerModule(new Jdk8Module())
                        .registerModule(new JavaTimeModule());
                return objectMapper.readValue(json, DeveloperNotification.class);
            } catch (Exception e) {
                throw new RuntimeException("Failed to decode DeveloperNotification", e);
            }
        }
    }

    public record DeveloperNotification(
            String version,
            String packageName,

            @JsonProperty("eventTimeMillis")
            @JsonDeserialize(using = InstantFromMillisDeserializer.class)
            Instant eventTimeMillis,

            Optional<OneTimeProductNotification> oneTimeProductNotification,
            Optional<SubscriptionNotification> subscriptionNotification,
            Optional<VoidedPurchaseNotification> voidedPurchaseNotification,
            Optional<TestNotification> testNotification

    ) {}

    public static class InstantFromMillisDeserializer extends JsonDeserializer<Instant> {
        @Override
        public Instant deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String raw = p.getText();
            return Instant.ofEpochMilli(Long.parseLong(raw));        }
    }

    public record SubscriptionNotification(
            String version,
            SubscriptionNotificationType notificationType,
            String purchaseToken,
            String subscriptionId
    ) { }

    public enum SubscriptionNotificationType {
        SUBSCRIPTION_RECOVERED(1),
        SUBSCRIPTION_RENEWED(2),
        SUBSCRIPTION_CANCELED(3),
        SUBSCRIPTION_PURCHASED(4),
        SUBSCRIPTION_ON_HOLD(5),
        SUBSCRIPTION_IN_GRACE_PERIOD(6),
        SUBSCRIPTION_RESTARTED(7),
        SUBSCRIPTION_PRICE_CHANGE_CONFIRMED(8), // Deprecated
        SUBSCRIPTION_DEFERRED(9),
        SUBSCRIPTION_PAUSED(10),
        SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED(11),
        SUBSCRIPTION_REVOKED(12),
        SUBSCRIPTION_EXPIRED(13),
        SUBSCRIPTION_PRICE_CHANGE_UPDATED(19),
        SUBSCRIPTION_PENDING_PURCHASE_CANCELED(20),

        UNKNOWN(-1); // Fallback

        private final int code;

        SubscriptionNotificationType(int code) {
            this.code = code;
        }

        @JsonValue
        public int getCode() {
            return code;
        }

        @JsonCreator
        public static SubscriptionNotificationType fromCode(int code) {
            for (SubscriptionNotificationType type : values()) {
                if (type.code == code) return type;
            }
            return UNKNOWN;
        }
    }

    public record OneTimeProductNotification(
        String version,
        OneTimeProductNotificationType notificationType,
        String purchaseToken,
        String sku
    ) {}

    public enum OneTimeProductNotificationType {
        ONE_TIME_PRODUCT_PURCHASED(1),
        ONE_TIME_PRODUCT_CANCELED(2),
        UNKNOWN(-1); // Fallback

        private final int code;

        OneTimeProductNotificationType(int code) {
            this.code = code;
        }

        @JsonValue
        public int getCode() {
            return code;
        }

        @JsonCreator
        public static OneTimeProductNotificationType fromCode(int code) {
            for (OneTimeProductNotificationType type : values()) {
                if (type.code == code) return type;
            }
            return UNKNOWN;
        }
    }

    public record VoidedPurchaseNotification(
            String purchaseToken,
            String orderId,
            VoidedPurchaseProductType productType,
            VoidedPurchaseRefundType refundType
    ) {}

    public enum VoidedPurchaseProductType {
        PRODUCT_TYPE_SUBSCRIPTION(1),
        PRODUCT_TYPE_ONE_TIME(2),
        UNKNOWN(-1); // Fallback

        private final int code;

        VoidedPurchaseProductType(int code) {
            this.code = code;
        }

        @JsonValue
        public int getCode() {
            return code;
        }

        @JsonCreator
        public static VoidedPurchaseProductType fromCode(int code) {
            for (VoidedPurchaseProductType type : values()) {
                if (type.code == code) return type;
            }
            return UNKNOWN;
        }
    }


    public enum VoidedPurchaseRefundType {
        REFUND_TYPE_FULL_REFUND(1),
        REFUND_TYPE_QUANTITY_BASED_PARTIAL_REFUND(2),
        UNKNOWN(-1); // Fallback

        private final int code;

        VoidedPurchaseRefundType(int code) {
            this.code = code;
        }

        @JsonValue
        public int getCode() {
            return code;
        }

        @JsonCreator
        public static VoidedPurchaseRefundType fromCode(int code) {
            for (VoidedPurchaseRefundType type : values()) {
                if (type.code == code) return type;
            }
            return UNKNOWN;
        }
    }

    public record TestNotification(
            String version
    ) {}
}
