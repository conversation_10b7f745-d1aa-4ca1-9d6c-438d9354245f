package com.arrivinginhighheels.visited.backend.features.experiences;

import java.util.ArrayList;
import java.util.List;

public class ExperienceBatchSelectionDTO {
    private String isoCode;
    private List<ExperienceAreaSelectionDTO> selections = new ArrayList<>();

    public ExperienceBatchSelectionDTO() { }

    public ExperienceBatchSelectionDTO(String isoCode, List<ExperienceAreaSelectionDTO> selections) {
        this.isoCode = isoCode;
        this.selections = selections;
    }

    public String getIsoCode() {
        return isoCode;
    }

    public void setIsoCode(String isoCode) {
        this.isoCode = isoCode;
    }

    public List<ExperienceAreaSelectionDTO> getSelections() {
        return selections;
    }

    public void setSelections(List<ExperienceAreaSelectionDTO> selections) {
        this.selections = selections;
    }
}
