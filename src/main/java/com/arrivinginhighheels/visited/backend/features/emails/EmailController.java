package com.arrivinginhighheels.visited.backend.features.emails;

import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Map;
import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.EMAIL_URL;

@RestController
@RequestMapping(path = EMAIL_URL)
public class EmailController {
    final EmailService service;

    public EmailController(EmailService service) {
        this.service = service;
    }

    @ResponseBody
    @GetMapping(path = "unsubscribe")
    public ResponseEntity<String> unsubscribe(@RequestParam String email) throws IOException {
        var success = service.unsubscribeUser(email);
        if (!success) {
            throw new UserNotFoundByUsernameException(email);
        }

        return ResponseEntity.ok(unsubscribeTemplate);
    }

    @ResponseBody
    @GetMapping(path = "resubscribe")
    public String resubscribe(@RequestParam String email) {
        var success = service.resubscribeUser(email);
        if (!success) {
            throw new UserNotFoundByUsernameException(email);
        }

        return "You have been resubscribed to our mailing list";
    }


    @PostMapping(path = "maileroo_webhook")
    public ResponseEntity<String> mailerooWebhook(
            @RequestHeader("x-maileroo-signature") String signature,
            @RequestBody String payload) {

        try {
//            if (!isValidWebhook(payload, signature)) {
//                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
//            }

            return ResponseEntity.ok("Accepted");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Webhook validation error");
        }
    }

    private boolean isValidWebhook(String payload, String signature) throws NoSuchAlgorithmException, InvalidKeyException {
        // Create a signature using HMAC-SHA256
        var sha256_HMAC = Mac.getInstance("HmacSHA256");
        var secret = "07fff55128322da7e5ba5576c44d2db39604f707fa4c4d624f07939d183571e8";
        var secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
        sha256_HMAC.init(secret_key);

        // Compute the HMAC signature
        var hash = sha256_HMAC.doFinal(payload.getBytes());
        var computedSignature = Base64.getEncoder().encodeToString(hash);

        // Compare the computed signature with the received signature
        return computedSignature.equals(signature);
    }

    @PostMapping(path = "test")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Map<String, String> sendTestEmail(@RequestBody SendEmailDTO request) {
        return service.sendSimpleMessage(
                Optional.of(request.friendlyName()),
                Optional.of(request.from()),
                request.to(),
                request.subject(),
                request.htmlTemplate(),
                true
        );
    }

    @PostMapping(path = "bulk")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public void sendBulkEmail(@RequestBody SendBulkDTO request) {
        service.sendBulkMailingList(request);
    }


    @PostMapping(path = "trackLink")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<String> createTrackedLink(@RequestBody CreateTrackingLinkDTO request) {
        var url = service.createTrackingLink(request.link(), request.title());
        return url
                .map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Cannot create tracking link"));
    }

private final String unsubscribeTemplate = """
        <!DOCTYPE html>
        <html>
        <head>
            <title></title>
            <meta http-equiv="Content-Type" content="text/html;charset=utf-8"/>
        </head>
                
        <body style="margin:0;padding:0;">
        <table width="100%" style="width:100%;">
            <tbody>
            <tr>
                <td>
                    <table id="header" width="100%" style="width:100%;">
                        <tbody>
                        <tr>
                            <td>
                                <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tbody>
                                    <tr>
                                        <td colspan="2">
                                            <table border="0" width="100%" cellpadding="0" cellspacing="0"
                                                   style="
                                border-bottom: solid 3px #ffd70f;
                                background-color: #0058b7;
                                padding: 10px;
                                margin-bottom: 12px;">
                                                <tbody>
                                                <tr>
                                                    <td style="text-align: left">
                                                        <img alt="Visited_logo" border="0" hspace="0"
                                                             name="Visited_logo"
                                                             src="http://travelspikeoas.blob.core.windows.net/images/f973d87e36e74d9f96944a5314a8c770.png"
                                                             height="70" title="logo" vspace="0"/>
                                                    </td>
                                                    <td style="text-align: left; padding: 0 0px">
                                                        <a href="" name="TravelDeals" target="_blank" style="
                                                font-family: tahoma, arial, Helvetica,
                                                    sans-serif;
                                                color: #ffffff;
                                                text-decoration: none;
                                                font-size: 32px;
                                                font-weight: 600;
                                            ">Visited</a>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div style="font-family: sans-serif; text-align: center; font-weight: bold; padding: 16px">
                        You have been successfully unsubscribed.
                    </div>
                    <table id="footer" width="100%" style="width:100%;">
                        <tbody>
                        <tr>
                            <td>
                                <table style="width: 100%" width="100%">
                                    <tbody>
                                    <tr>
                                        <td>
                                            <table align="center" cellpadding="15" cellspacing="0" width="100%"
                                                   style="
                                border-top: solid 3px #ffd70f;
                                background-color: #0058b7;
                                padding-top: 1px;">
                                                <tbody>
                                                <tr>
                                                    <td>
                                                        <p style="color: #bec0c2" ;p align="center">
                                                                                <span style="
                                                    font-family: Tahoma, Arial,
                                                        Helvetica, sans-serif;
                                                    font-size: 11px;
                                                ">
                                                                                    <a href="http://www.arrivinginhighheels.com/privacy-policy/"
                                                                                       name="privacy-policy"
                                                                                       style="color: #ffffff"
                                                                                       target="_blank"><strong>Privacy
                                                                                            Policy</strong></a>
                                                                                    |
                                                                                    <a href="http://www.arrivinginhighheels.com/terms-of-use/"
                                                                                       name="terms-of-use"
                                                                                       style="color: #ffffff"
                                                                                       target="_blank"><strong>Terms of
                                                                                            Use</strong></a>
                                                                                </span>
                                                        </p>
                                                        <p align="center" style="
                                                font-family: Tahoma, Arial, Helvetica,
                                                    sans-serif;
                                                font-size: 11px;
                                                color: #ffffff;
                                            ">
                                                            Follow us on Social Media :
                                                            <a href="https://www.facebook.com/VisitedTravelApp"
                                                               target="_blank">
                                                                <img height="30"
                                                                     src="http://travelspikeoas.blob.core.windows.net/images/a2bdd02816d249a7837280d4a1c7628b.png"
                                                                     style="
                                                        height: 20px;
                                                        border: none;
                                                        border-width: 10px;
                                                    "/>
                                                            </a>
                                                            <a href="https://www.instagram.com/visitedtravelapp/"
                                                               target="_blank">
                                                                <img height="30"
                                                                     src="http://travelspikeoas.blob.core.windows.net/images/d8bb72b7201f4390a38623f2d56e2ad8.png"
                                                                     style="
                                                        height: 20px;
                                                        border: none;
                                                        border-width: 10px;
                                                    "/>
                                                            </a>
                                                            <a href="https://twitter.com/VisitedTravel"
                                                               target="_blank">
                                                                <img height="30"
                                                                     src="http://travelspikeoas.blob.core.windows.net/images/ca7c33c0670a49f8b9f7b191cb8f5ce3.png"
                                                                     style="
                                                        height: 20px;
                                                        border: none;
                                                        border-width: 10px;
                                                    "/>
                                                            </a>
                                                        </p>
                                                        <p style="color: #bec0c2" ;p align="center">
                                                                                <span style="
                                                    font-family: Tahoma, Arial,
                                                        Helvetica, sans-serif;
                                                    font-size: 11px;
                                                    color: #ffffff;
                                                ">You have received this email because
                                                                                    you have used the app, Visited.
                                                                                </span>
                                                        </p>
                                                        <p align="center" style="
                                                font-family: Tahoma, Arial, Helvetica,
                                                    sans-serif;
                                                font-size: 11px;
                                                color: #ffffff;
                                            ">
                                                            Arriving in High Heels Corporation, 31
                                                            Claudview St., King City, ON, L7B0C6, Canada
                                                        </p>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        </body>
        </html>
        """;
}
