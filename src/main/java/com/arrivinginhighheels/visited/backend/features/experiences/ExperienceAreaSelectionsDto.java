package com.arrivinginhighheels.visited.backend.features.experiences;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * DTO for selecting multiple experiences areas at once
 */
@Getter
public class ExperienceAreaSelectionsDto {
    private List<ExperienceAreaSelectionDTO> selections = new ArrayList<>();

    public ExperienceAreaSelectionsDto() { }

    public ExperienceAreaSelectionsDto(List<ExperienceAreaSelectionDTO> selections) {
        this.selections = selections;
    }

    public void setSelections(List<ExperienceAreaSelectionDTO> selections) {
        this.selections = selections;
    }
}

