package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface UserCitiesRepository extends JpaRepository<UserCity, Long> {
    @Transactional
    void deleteByUser(User user);

    List<UserCity> findAllByUser(User user);

    Optional<UserCity> findByUserAndCity(User user, City city);

    List<UserCity> findAllByUserAndType(User user, SelectionType type);

    void deleteByUserAndCityIdIn(User user, List<Long> cityIds);

    List<UserCity> findAllByUserAndCityIdIn(User user, List<Long> cityIds);

    @Query("""
       select userCity.city
       from UserCity userCity
       where userCity.user = :user AND
       userCity.type = :type AND 
       userCity.city.geoAreaLevelOne = :geoArea
    """)
    List<City> findAllByUserAndTypeAndArea(User user, SelectionType type, GeoArea geoArea);

    @Query("""
       select new com.arrivinginhighheels.visited.backend.model.City(
            userCity.city.id,
            translation.name,
            userCity.city.latitude,
            userCity.city.longitude,
            userCity.city.geoAreaLevelOne,
            userCity.city.geoAreaLevelTwo
        )
       from UserCity userCity
       left outer join CityTranslation translation on userCity.city = translation.city
       where userCity.user = ?1 AND
       userCity.type = ?2 AND
       userCity.city.geoAreaLevelOne = ?3 AND
       translation.supportedLanguage = ?4
    """)
    List<City> findAllByUserAndTypeAndAreaAndLanguage(User user, SelectionType type, GeoArea geoArea, SupportedLanguage language);
}
