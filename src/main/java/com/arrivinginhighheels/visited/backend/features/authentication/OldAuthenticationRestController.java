package com.arrivinginhighheels.visited.backend.features.authentication;

import com.arrivinginhighheels.visited.backend.dto.EmailAvailableDto;
import com.arrivinginhighheels.visited.backend.dto.EmailValidatedDto;
import com.arrivinginhighheels.visited.backend.dto.UserChangePasswordRequest;
import com.arrivinginhighheels.visited.backend.dto.UsersDTO;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import com.arrivinginhighheels.visited.backend.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.*;

/**
 * REST Controller for the login logic.
 */
@RestController
public class OldAuthenticationRestController {

    private static final Logger log = LoggerFactory.getLogger(OldAuthenticationRestController.class);

    private final UserBuilder userBuilder;

    private final LoginService loginService;

    private final LoggedInUserUtil loggedInUserUtil;

    private final UserRepository userRepository;

    private final UserService userService;

    public OldAuthenticationRestController(
            UserBuilder userBuilder,
            LoginService loginService,
            LoggedInUserUtil loggedInUserUtil,
            UserRepository userRepository,
            UserService userService) {
        this.userBuilder = userBuilder;
        this.loginService = loginService;
        this.loggedInUserUtil = loggedInUserUtil;
        this.userRepository = userRepository;
        this.userService = userService;
    }

    @PostMapping(SIGNUP_URL + "/available")
    public EmailValidatedDto isEmailTaken(@RequestBody EmailAvailableDto dto) {
        var email = dto.getEmail();
        var available = loginService.emailAvailable(email);
        return new EmailValidatedDto(email, available);
    }

    @PostMapping(value = SIGNIN_URL)
    public ResponseEntity<TokenResponseWithStats> createAuthenticationToken(
            @RequestBody @Valid final AuthRequest authRequest,
            @RequestParam(name = "includeStats", defaultValue = "true", required = false) final boolean fetchSelections)
            throws AuthenticationException {

        final var tokenResponse = loginService.oldLogin(authRequest, fetchSelections);

        log.info("Login request successful for the user " + tokenResponse.user().getEmail());

        // Return the token
        return ResponseEntity.ok(tokenResponse);
    }

    @GetMapping(value = USERS_URL)
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public UsersDTO getUsers() {
        final List<User> users = userRepository.findAll();
        return new UsersDTO(users.stream()
                .map(userBuilder::createDTOFromUser)
                .collect(Collectors.toList()));
    }

    //Deletes the logged in user.  Destroys everything, including the session."
    @DeleteMapping(value = USERS_ME_URL, produces = "application/json")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public boolean deleteLoggedUser(final HttpServletRequest request) {
        final User loggedInUser = loggedInUserUtil.getLoggedInUser(request);
        userService.deleteUser(loggedInUser);
        return true;
    }

    @PostMapping(path = UPDATE_PASSWORD_URL)
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public Boolean updatePassword(final HttpServletRequest request,
            @Valid @RequestBody UserChangePasswordRequest passwordRequest) {
        final User loggedInUser = loggedInUserUtil.getLoggedInUser(request);
        userService.updatePassword(loggedInUser, passwordRequest);
        return true;
    }
}
