package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "user_todo_list_items")
public class UserTodoListItem {

    @Id
    @SequenceGenerator(name = "user_list_item_id_seq", sequenceName = "user_list_item_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_list_item_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private User user;

    @ManyToOne(optional = false)
    private TodoListItem listItem;

    @ManyToOne(optional = false)
    private TodoList list;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "selection_type", length = 10, nullable = false)
    private SelectionType type;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    public UserTodoListItem() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public TodoListItem getListItem() {
        return listItem;
    }

    public void setListItem(TodoListItem listItem) {
        this.listItem = listItem;
    }

    public TodoList getList() {
        return list;
    }

    public void setList(TodoList list) {
        this.list = list;
    }

    public SelectionType getType() {
        return type;
    }

    public void setType(SelectionType type) {
        this.type = type;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        UserTodoListItem that = (UserTodoListItem) o;
        return Objects.equals(id, that.id) && Objects.equals(user, that.user) && Objects.equals(listItem, that.listItem)
                && Objects.equals(list, that.list) && type == that.type && Objects.equals(timestamp, that.timestamp);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, user, listItem, list, type, timestamp);
    }

    @Override
    public String toString() {
        return "UserTodoListItem{" + "id=" + id + ", user=" + user + ", listItem=" + listItem + ", list=" + list
                + ", type=" + type + ", timestamp=" + timestamp + '}';
    }
}
