package com.arrivinginhighheels.visited.backend.features.books;

import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceRepository;
import com.arrivinginhighheels.visited.backend.features.todoLists.TodoListRepository;
import com.arrivinginhighheels.visited.backend.model.BookLink;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.service.translations.AreaTranslationService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
public class BookService {

    final private BookLinkRepository bookLinkRepository;

    final private GeoAreaRepository areaRepository;

    final private ExperienceRepository experienceRepository;

    final private TodoListRepository todoListRepository;

    final private AreaTranslationService translationService;

    final private Random random = new Random();

    public BookService(
            BookLinkRepository bookLinkRepository,
            GeoAreaRepository areaRepository,
            ExperienceRepository experienceRepository,
            TodoListRepository todoListRepository,
            AreaTranslationService translationService) {
        this.bookLinkRepository = bookLinkRepository;
        this.areaRepository = areaRepository;
        this.experienceRepository = experienceRepository;
        this.todoListRepository = todoListRepository;
        this.translationService = translationService;
    }

    public Optional<BookDto> fetchBooksByAreaIsoCode(String isoCode) {
        if (!translationService.isInEnglish()) {
            return Optional.empty();
        }

        final var area = areaRepository.findByIsoKey(isoCode).orElseThrow();
        final var books = bookLinkRepository.findBooksByArea(area);
        return bookLinksToDtos(books);
    }

    public Optional<BookDto> fetchBooksByTodoListId(Long listId) {
        if (!translationService.isInEnglish()) {
            return Optional.empty();
        }

        final var list = todoListRepository.findById(listId).orElseThrow();
        final var books = bookLinkRepository.findBooksByTodoList(list);
        return bookLinksToDtos(books);
    }

    public Optional<BookDto> fetchBooksByExperienceId(Long experienceId) {
        if (!translationService.isInEnglish()) {
            return Optional.empty();
        }

        final var experience = experienceRepository.findById(experienceId).orElseThrow();
        final var books = bookLinkRepository.findBooksByExperience(experience);
        return bookLinksToDtos(books);
    }

    private Optional<BookDto> bookLinksToDtos(List<BookLink> books) {
        if (books.isEmpty()) {
            return Optional.empty();
        }

        final var randomBookIndex = random.nextInt(books.size());
        final var book = books.get(randomBookIndex);
        return Optional.of(new BookDto(
            book.getName(),
            book.getUrl(),
            book.getImageUrl())
        );
    }
}
