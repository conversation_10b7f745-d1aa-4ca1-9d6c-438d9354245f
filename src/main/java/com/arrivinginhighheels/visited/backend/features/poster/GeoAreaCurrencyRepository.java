package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaCurrency;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface GeoAreaCurrencyRepository extends JpaRepository<GeoAreaCurrency, Long> {
    Optional<GeoAreaCurrency> findByGeoArea(GeoArea geoArea);
}
