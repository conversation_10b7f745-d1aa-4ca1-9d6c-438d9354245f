package com.arrivinginhighheels.visited.backend.features.iap;

import com.arrivinginhighheels.visited.backend.model.User;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Objects;

@Setter
@Getter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "user_purchases")
public class UserPurchase {
    @Id
    @SequenceGenerator(name = "user_purchases_id_seq", sequenceName = "user_purchases_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_purchases_id_seq")
    private Long id;

    @NotNull
    @ManyToOne(optional = false)
    private User user;

    @NotNull
    @Column(name = "product_id")
    private String productId;

    @NotNull
    @Column(nullable = false)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime timestamp;

    @Column(name = "transaction_id")
    private String transactionId;

    @Enumerated(EnumType.STRING)
    private StorePlatform platform;

    @Enumerated(EnumType.STRING)
    private UserPurchaseStatus status;

    @Enumerated(EnumType.STRING)
    @Column(name = "product_type")
    private UserPurchaseProductType productType;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "expiration_date")
    private LocalDateTime expirationDate;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @Column(name = "last_checked")
    private LocalDateTime lastChecked;

    @Column(name = "auto_renew")
    private Boolean autoRenew;

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserPurchase that)) return false;

        return Objects.equals(getId(), that.getId()) && Objects.equals(getUser(), that.getUser()) && Objects.equals(getProductId(), that.getProductId()) && Objects.equals(getTimestamp(), that.getTimestamp()) && Objects.equals(getTransactionId(), that.getTransactionId()) && getPlatform() == that.getPlatform() && getStatus() == that.getStatus() && getProductType() == that.getProductType() && Objects.equals(getExpirationDate(), that.getExpirationDate()) && Objects.equals(getLastChecked(), that.getLastChecked()) && Objects.equals(getAutoRenew(), that.getAutoRenew());
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(getId());
        result = 31 * result + Objects.hashCode(getUser());
        result = 31 * result + Objects.hashCode(getProductId());
        result = 31 * result + Objects.hashCode(getTimestamp());
        result = 31 * result + Objects.hashCode(getTransactionId());
        result = 31 * result + Objects.hashCode(getPlatform());
        result = 31 * result + Objects.hashCode(getStatus());
        result = 31 * result + Objects.hashCode(getProductType());
        result = 31 * result + Objects.hashCode(getExpirationDate());
        result = 31 * result + Objects.hashCode(getLastChecked());
        result = 31 * result + Objects.hashCode(getAutoRenew());
        return result;
    }

    @Override
    public String toString() {
        return "UserPurchase{" +
                "id=" + id +
                ", user=" + user +
                ", productId='" + productId + '\'' +
                ", timestamp=" + timestamp +
                ", transactionId='" + transactionId + '\'' +
                ", platform=" + platform +
                ", status=" + status +
                ", productType=" + productType +
                ", expirationDate=" + expirationDate +
                ", lastChecked=" + lastChecked +
                ", autoRenew=" + autoRenew +
                '}';
    }


}
