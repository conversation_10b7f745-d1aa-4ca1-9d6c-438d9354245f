package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationSelectionType;

public class UserInspirationSelectionDTO {
    private Long inspirationId;
    private InspirationSelectionType type;

    public UserInspirationSelectionDTO() {
    }

    public UserInspirationSelectionDTO(Long inspirationId, InspirationSelectionType type) {
        this.inspirationId = inspirationId;
        this.type = type;
    }

    public Long getInspirationId() {
        return inspirationId;
    }

    public void setInspirationId(Long inspirationId) {
        this.inspirationId = inspirationId;
    }

    public InspirationSelectionType getType() {
        return type;
    }

    public void setType(InspirationSelectionType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "UserInspirationSelectionDTO{" +
                "inspirationId=" + inspirationId +
                ", type=" + type +
                '}';
    }
}
