package com.arrivinginhighheels.visited.backend.features.itineraries;


import com.arrivinginhighheels.visited.backend.features.cities.CityDto;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceDTO;
import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationDTO;
import com.arrivinginhighheels.visited.backend.features.todoLists.TodoListItemDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record ItineraryV2dto(
        List<InspirationDTO> inspirations,
        List<ExperienceDTO> experiences,
        List<CityDto> cities,
        List<TodoListItemDTO> places,
        List<TodoListItemDTO> food
) {
}
