package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.dto.SponsorDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.features.books.BookService;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.utils.AdaptiveImageUtils;
import org.apache.maven.artifact.versioning.DefaultArtifactVersion;
import org.hibernate.Session;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.arrivinginhighheels.visited.backend.model.SelectionType.BEEN;
import static com.arrivinginhighheels.visited.backend.model.SelectionType.CLEAR;
import static java.util.stream.Collectors.groupingBy;

@Service
public class TodoListService {

    private final TodoListRepository listRepository;

    private final TodoListXrefRepository xrefRepository;

    private final TodoListItemRepository itemRepository;

    private final AdaptiveImageUtils imageUtils;

    private final TodoListTranslationService translationService;

    private final UserTodoListItemRepository userTodoListItemRepository;

    private final TodoListTagRepository tagRepository;

    private final TodoListTagLookupRepository tagLookupRepository;

    private final TodoTagTranslationRepository tagTranslationRepository;

    private final GeoAreaRepository areaRepository;

    private final BookService bookService;

    private final TodoListPopularityRepository popularityRepository;

    public TodoListService(
            TodoListItemRepository itemRepository,
            TodoListRepository listRepository,
            TodoListXrefRepository xrefRepository,
            AdaptiveImageUtils imageUtils,
            TodoListTranslationService translationService,
            UserTodoListItemRepository userTodoListItemRepository,
            TodoListTagRepository tagRepository,
            TodoListTagLookupRepository tagLookupRepository,
            TodoTagTranslationRepository tagTranslationRepository,
            GeoAreaRepository areaRepository,
            BookService bookService,
            TodoListPopularityRepository popularityRepository) {
        this.itemRepository = itemRepository;
        this.listRepository = listRepository;
        this.xrefRepository = xrefRepository;
        this.imageUtils = imageUtils;
        this.translationService = translationService;
        this.userTodoListItemRepository = userTodoListItemRepository;
        this.tagRepository = tagRepository;
        this.tagLookupRepository = tagLookupRepository;
        this.tagTranslationRepository = tagTranslationRepository;
        this.areaRepository = areaRepository;
        this.bookService = bookService;
        this.popularityRepository = popularityRepository;
    }

    public List<TodoListDTO> getAll(final double resolution, final OSType os, String version) {
        final var clientSemanticVersion = new DefaultArtifactVersion(version);

        return listRepository.findAllByOrderByOrdinalAsc().stream().filter(e -> {
            final var minVersion = new DefaultArtifactVersion(e.getMinVersion());
            return clientSemanticVersion.compareTo(minVersion) >= 0;
        }).sorted((a, b) -> {
            final Integer hasSponsorA = a.getSponsor() != null ? 1 : 0;
            final Integer hasSponsorB = b.getSponsor() != null ? 1 : 0;

            return hasSponsorB.compareTo(hasSponsorA);
        }).map(e -> buildTodoListDto(e, resolution, os)).collect(Collectors.toList());
    }

    public Date getListLastModifiedTime(Long listId) {
        return findListById(listId).getLastModificationTime();
    }

    public TodoListDetailsDto getDetails(
            final Long listId,
            final double resolution,
            final OSType os) {
        final var list = findListById(listId);
        final var listDto = buildTodoListDto(list, resolution, os);

        final var bookLink = bookService.fetchBooksByTodoListId(listId);

        final var rawItems = xrefRepository.findAllByTodoListOrderByOrdinalAsc(list);
        final var language = translationService.getCurrentLanguage();

        if (language.isEnglish()) {
            final var items = rawItems
                    .stream()
                    .filter(Objects::nonNull)
                    .map(xref -> buildTodoListItemDto(xref, resolution))
                    .toList();
            return new TodoListDetailsDto(listDto, items, bookLink);
        }

        final var items = rawItems.stream()
                .filter(Objects::nonNull)
                .map(xref -> {
                    final var translation = translationService.getListItemName(xref.getListItem());
                    return buildTodoListItemDto(xref, translation, resolution);
                })
                .toList();
        return new TodoListDetailsDto(listDto, items, bookLink);
    }

    private TodoListDTO buildTodoListDto(
            final TodoList list,
            final double resolution,
            final OSType os) {
        final var url = imageUtils.getResolution(list.getImage(), resolution);
        final var name = translationService.getListName(list);
        var dto = TodoListDTO.builder()
                .id(list.getId())
                .name(name)
                .thumbnailUrl(url)
                .thumbnailBlurHash(list.getImage().getBlurHash())
                .count(list.getCount())
                .type(list.getType());

        final var sponsor = list.getSponsor();
        if (sponsor != null) {
            final var sponsorDto = buildSponsorDTO(os, sponsor);
            dto = dto.sponsor(sponsorDto);
        }

        if (list.getStatus() != null) {
            dto = dto.status(list.getStatus());
        }

        return dto.build();
    }

    private SponsorDTO buildSponsorDTO(OSType os, Sponsor sponsor) {
        String url;
        if (os == OSType.iOS) {
            url = sponsor.getUrlIos();
        } else if (os == OSType.Android) {
            url = sponsor.getUrlAndroid();
        } else {
            url = sponsor.getUrl();
        }

        return new SponsorDTO(sponsor.getName(), url, sponsor.getPromotionalText());
    }

    public TodoListItemDTO buildTodoListItemDto(
            TodoListXref xref,
            final double resolution) {
        return buildTodoListItemDto(
                xref.getListItem(),
                xref.getListItem().getName(),
                xref.getThumbnail(),
                resolution,
                xref.getPopularity());
    }

    private TodoListItemDTO buildTodoListItemDto(
            TodoListXref item,
            String translation,
            double resolution) {
        return buildTodoListItemDto(
                item.getListItem(),
                translation,
                item.getThumbnail(),
                resolution,
                item.getPopularity());
    }

    public TodoListItemDTO buildTodoListItemDto(
            LocalizedTodoListItem localizedItem,
            final double resolution) {
        return buildTodoListItemDto(
                localizedItem.getItem(),
                localizedItem.getTranslation().getName(),
                localizedItem.getThumbnail(),
                resolution,
                localizedItem.getPopularity());
    }

    private TodoListItemDTO buildTodoListItemDto(
            TodoListItem listItem,
            String name,
            AdaptiveImage thumbnail,
            final double resolution,
            Integer popularity) {
        String url = null;
        String blurHash = null;

        if (thumbnail != null) {
            url = imageUtils.getResolution(thumbnail, resolution);
            blurHash = thumbnail.getBlurHash();
        }

        final var coordinate = buildCoordinateDto(listItem);

        String iso = null;
        final var geoArea = listItem.getGeoArea();
        if (geoArea != null) {
            iso = geoArea.getIsoKey();
        }

        var dto = new TodoListItemDTO();
        dto.setId(listItem.getId());
        dto.setName(name);
        dto.setIsoCode(iso);
        dto.setImageUrl(url);
        dto.setImageBlurHash(blurHash);
        dto.setCoordinate(coordinate);
        dto.setPopularity(popularity);

        final var city = listItem.getCity();
        if (city != null) {
            dto.setCityId(city.getId());
        }

        return dto;
    }

    @Deprecated
    public boolean selectItemById(final Long id, User user) {
        final var item = findItemById(id);
        final var list = item.getLists().stream().findFirst().orElseThrow();
        final var existingSelection = userTodoListItemRepository.findByUserAndListItemAndList(user, item, list);

        // Item already selected
        if (existingSelection.isPresent()) {
            return true;
        }

        final var selection = new UserTodoListItem();
        selection.setUser(user);
        selection.setListItem(item);
        selection.setTimestamp(LocalDateTime.now());
        selection.setList(list);
        selection.setType(BEEN);
        userTodoListItemRepository.save(selection);
        return true;
    }

    @Deprecated
    public boolean unselectItemById(final Long id, User user) {
        final var item = findItemById(id);
        final var list = item.getLists().stream().findFirst().orElseThrow();

        final var existingSelection = userTodoListItemRepository.findByUserAndListItemAndList(user, item, list);

        // Item isn't selected; nothing to do.
        if (existingSelection.isEmpty()) {
            return false;
        }

        userTodoListItemRepository.delete(existingSelection.get());
        return true;
    }

    public TodoListSelectionDTO getSelectionsForList(final Long listId, User user) {
        final var list = findListById(listId);
        final var selections = userTodoListItemRepository.findAllByUserAndList(user, list);

        return buildListItemSelectionDto(selections);
    }

    private TodoListSelectionDTO buildListItemSelectionDto(List<UserTodoListItem> selections) {
        final var dto = new TodoListSelectionDTO();
        final var dtoIds = dto.getSelections();
        for (var selection : selections) {
            final var type = selection.getType();
            final var ids = dtoIds.getOrDefault(type, new ArrayList<>());
            ids.add(selection.getListItem().getId());
            dtoIds.put(type, ids);
        }
        return dto;
    }

    private TodoList findListById(final Long listId) {
        final var list = listRepository.findById(listId);
        if (list.isEmpty()) {
            throw new RuntimeException("List " + listId + " not found");
        }
        return list.get();
    }

    private TodoListItem findItemById(final Long id) {
        final var item = itemRepository.findById(id);
        if (item.isEmpty()) {
            throw new RuntimeException("Unknown Todo List Item - " + id);
        }
        return item.get();
    }

    public List<TodoListSummaryDto> getSelectionSummary(User user) {
        final var selections = userTodoListItemRepository.getSelectionSummaryByUser(user.getId());
        return selections.stream().map((e) -> new TodoListSummaryDto(longValue(e, 0), longValue(e, 1))).toList();
    }

    public List<TodoListSummaryDto> getAreaSelectionSummary(User user) {
        return userTodoListItemRepository
                .findAreaSummaryByUser(user)
                .stream()
                .filter((e) -> e != null && e.getCount() != null && e.getListId() != null)
                .map((e) -> new TodoListSummaryDto(e.getListId(), e.getCount()))
                .toList();
    }

    private long longValue(Object[] result, int index) {
        var value = result[index];
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof BigInteger) {
            return ((BigInteger) value).longValue();
        } else if (value instanceof Double) {
            return ((Double) value).longValue();
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else {
            return 0;
        }
    }

    public List<Long> getListSelections(User user, Long listId) {
        final var list = listRepository.findById(listId);
        if (list.isEmpty()) {
            throw new RuntimeException("Unknown List - " + listId);
        }
        final var selection = userTodoListItemRepository.findAllByUserAndList(user, list.get());

        return selection.stream().map(e -> e.getListItem().getId()).collect(Collectors.toList());
    }

    public List<Long> selectListItem(User user, Long listId, Long listItemId, SelectionType type) {
        if (type == CLEAR) {
            return unselectListItem(user, listId, listItemId);
        }

        final var listAndItem = getListAndItem(listId, listItemId);

        final var list = listAndItem.list;
        final var item = listAndItem.item;

        final var existingSelection = userTodoListItemRepository.findByUserAndListItemAndList(user, item, list);

        final var selection = existingSelection.orElseGet(UserTodoListItem::new);
        selection.setUser(user);
        selection.setListItem(item);
        selection.setList(list);
        selection.setType(type);
        selection.setTimestamp(LocalDateTime.now());
        userTodoListItemRepository.save(selection);

        return getListSelections(user, listId);
    }

    public List<Long> unselectListItem(User user, Long listId, Long listItemId) {
        final var listAndItem = getListAndItem(listId, listItemId);

        final var list = listAndItem.list;
        final var item = listAndItem.item;

        final var existingSelection = userTodoListItemRepository.findByUserAndListItemAndList(user, item, list);

        // Not Select, nothing to do
        if (existingSelection.isEmpty()) {
            return getListSelections(user, listId);
        }

        final var selection = existingSelection.get();
        userTodoListItemRepository.delete(selection);
        return getListSelections(user, listId);
    }

    private ListAndItem getListAndItem(Long listId, Long listItemId) {
        final var maybeItem = itemRepository.findById(listItemId);
        if (maybeItem.isEmpty()) {
            throw new RuntimeException("Unknown List Item - " + listItemId);
        }

        final var maybeList = listRepository.findById(listId);
        if (maybeList.isEmpty()) {
            throw new RuntimeException("Unknown List - " + listId);
        }

        final var item = maybeItem.get();
        final var list = maybeList.get();

        if (!item.getLists().contains(list)) {
            throw new RuntimeException("List " + listId + " has no list item " + listItemId);
        }

        return new ListAndItem(list, item);
    }

    public List<TodoListTagDto> getTags() {
        var allTags = tagRepository.findAll();
        var allLookups = tagLookupRepository.findAll();
        var currentLanguage = translationService.getCurrentLanguage();
        var isEnglish = currentLanguage.isEnglish();

        List<TodoTagTranslation> translations = null;
        if (!isEnglish) {
            translations = tagTranslationRepository.findAllBySupportedLanguage(currentLanguage);
        }

        var dtos = new ArrayList<TodoListTagDto>();

        for (var tag : allTags) {
            var ids = allLookups
                    .stream()
                    .filter((e) -> Objects.equals(e.getTagId(), tag.getId()))
                    .map(TodoListTagLookup::getListId)
                    .toList();

            var name = isEnglish ? tag.getName()
                    : translations
                            .stream()
                            .filter((e) -> e.getTag().equals(tag))
                            .map(TodoTagTranslation::getName)
                            .findFirst()
                            .orElse(tag.getName());

            dtos.add(new TodoListTagDto(tag.getId(), name, ids));
        }

        return dtos;
    }

    public Date getLastModifiedTimeForLists() {
        return listRepository.getMaxModificationTime();
    }

    public List<TodoListDTO> getAllByCountry(double actualResolution) {
        var language = translationService.getCurrentLanguage();

        var lists = language.isEnglish()
                ? listRepository.getCountryTodoLists()
                : listRepository.getLocalizedCountryTodoLists(language);


        return lists.stream().map((e) ->  TodoListDTO.builder()
                .id(e.getArea().getId())
                .name(e.getTranslation() != null ? e.getTranslation().getName() : e.getArea().getName())
                .thumbnailUrl(imageUtils.getResolution(e.getImage(), actualResolution))
                .thumbnailBlurHash(e.getImage().getBlurHash())
                .count(e.getItemCount().intValue())
                .type(TodoListType.IGNORE)
                .build()).toList();
    }

    public List<TodoListItemDTO> getAreaTodoListItems(String isoCode, double resolution,
            Optional<TodoListType> filter) {
        var area = areaRepository.findByIsoKey(isoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(isoCode));
        var language = translationService.getCurrentLanguage();

        List<TodoListItemDTO> dtos;

        if (language.isEnglish()) {
            var response = filter.isPresent()
                    ? xrefRepository.findAllByListItem_GeoAreaAndTodoList_TypeOrderByPopularityDesc(area, filter.get())
                    : xrefRepository.findAllByListItem_GeoAreaOrderByPopularityDesc(area);
            dtos = response
                    .stream()
                    .map((e) -> {
                        var dto = buildTodoListItemDto(e, resolution);
                        dto.setListIds(Collections.singletonList(e.getTodoList().getId()));
                        return dto;
                    })
                    .collect(Collectors.toList());

        } else {
            var response = filter.isPresent()
                    ? xrefRepository.findAllByGeoAreaAndTypeAndLanguage(area, filter.get(), language)
                    : xrefRepository.findAllByGeoAreaAndLanguage(area, language);
            dtos = response
                    .stream()
                    .map((e) -> {
                        var dto = buildTodoListItemDto(e.xref(), resolution);
                        dto.setName(e.translation().getName());
                        dto.setListIds(Collections.singletonList(e.xref().getTodoList().getId()));
                        return dto;
                    })
                    .collect(Collectors.toList());
        }

        return dtos;
    }

    private List<Double> buildCoordinateDto(TodoListItem item) {
        return Arrays.asList(
                item.getLongitude().doubleValue(),
                item.getLatitude().doubleValue());
    }

    public List<TodoAreaListSelectionDTO> getAreaListSelections(String isoCode, User user) {
        var area = areaRepository.findByIsoKey(isoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(isoCode));

        var selections = userTodoListItemRepository
                .findAllByUserAndListItem_GeoArea(user, area);

        return selections
                .stream()
                .collect(groupingBy(UserTodoListItem::getList))
                .entrySet()
                .stream()
                .map((e) -> {
                    final var dto = new TodoAreaListSelectionDTO();
                    dto.setListId(e.getKey().getId());

                    var listSelections = buildListItemSelectionDto(e.getValue());
                    dto.setSelections(listSelections.getSelections());
                    return dto;
                }).toList();
    }

    public List<TopOneThousandListDto> getTopOneThousandList(User user, double resolution) {
        final var language = translationService.getCurrentLanguage();
        final var popularities = language.isEnglish()
                ? popularityRepository.findAllByOrderByCountDesc(user)
                :  popularityRepository.findAllByOrderByCountDescLocalized(user, language);

        return popularities.stream().map((e) -> {
            final var popularity = e.getPopularity();
            final var dto = buildTodoListItemDto(
                    popularity.getTodoListItem(),
                    e.getLocalizedName(),
                    popularity.getThumbnail(),
                    resolution,
                    popularity.getCount()
            );
            dto.setListIds(Collections.singletonList(e.getPopularity().getTodoList().getId()));
            return new TopOneThousandListDto(dto, e.selection);
        }).toList();
    }

    private record ListAndItem(TodoList list, TodoListItem item) {
    }

    @PersistenceContext
    private EntityManager entityManager;

    @Scheduled(cron = "0 0 1 * * MON")
    public void scheduleWeeklyListSorting() {
        if (entityManager == null) {
            return;
        }

        final var session = entityManager.unwrap(Session.class);
        if (session == null) {
            return;
        }

        final var transaction = session.beginTransaction();
        updateListAndItemOrderByPopularity();
        transaction.commit();
    }

    public void updateListAndItemOrderByPopularity() {
        itemRepository.sortByPopularity();
        listRepository.sortByPopularity();
    }
}
