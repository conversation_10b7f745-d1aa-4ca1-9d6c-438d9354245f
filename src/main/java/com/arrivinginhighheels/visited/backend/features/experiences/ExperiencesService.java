package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.service.AbstractSelectionService;
import com.arrivinginhighheels.visited.backend.service.DealsService;
import com.arrivinginhighheels.visited.backend.service.translations.ExperiencesTranslationService;
import com.arrivinginhighheels.visited.backend.utils.DateTools;
import com.arrivinginhighheels.visited.backend.utils.ETagHashUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

import static java.util.stream.Collectors.toList;

@Service
public class ExperiencesService extends AbstractSelectionService {

    private final ExperienceRepository experienceRepository;

    private final ExperiencesTranslationService experiencesTranslationService;

    private final UserExperienceAreaRepository userExperienceAreaRepository;

    private final UserExperienceRepository userExperienceRepository;

    private final DealsService dealsService;

    private final DateTools dateTools;

    public ExperiencesService(ExperienceRepository experienceRepository, ExperiencesTranslationService experiencesTranslationService, UserExperienceAreaRepository userExperienceAreaRepository, UserExperienceRepository userExperienceRepository, DealsService dealsService, DateTools dateTools) {
        this.experienceRepository = experienceRepository;
        this.experiencesTranslationService = experiencesTranslationService;
        this.userExperienceAreaRepository = userExperienceAreaRepository;
        this.userExperienceRepository = userExperienceRepository;
        this.dealsService = dealsService;
        this.dateTools = dateTools;
    }

    public List<ExperienceDTO> getAllExperiences() {
        return experienceRepository.findAll()
                .stream()
                .map((e) -> buildExperienceDTO(e)
                        .setEtag(generateEtagForExperience(e)))
                .collect(toList());
    }

    public List<ExperienceDTO> getPreferredExperiences(User user) {
        return userExperienceRepository.findAllByUser(user)
                .stream()
                .map((e) -> buildExperienceDTO(e.getExperience()))
                .collect(toList());
    }

    public List<String> getAreasByExperienceId(Long id) {
        return experienceRepository.findAllByExperienceId(id);
    }

    public List<ExperienceSelectionsDTO> getSelections(User user) {
        List<Experience> preferred = userExperienceRepository
                .findAllByUser(user)
                .stream()
                .map(UserPreferredExperience::getExperience)
                .toList();

        List<UserExperienceArea> areas = userExperienceAreaRepository.findAllByUser(user);

        Map<Experience, ExperienceSelectionsDTO> summaries = new HashMap<>();

        for (UserExperienceArea selection : areas) {
            Experience experience = selection.getExperience();
            ExperienceSelectionsDTO summary = summaries.get(experience);
            if (summary == null) {
                summary = new ExperienceSelectionsDTO()
                        .setExperience(buildExperienceDTO(experience))
                        .setPreferred(preferred.contains(experience));
            }


            SelectionType type = selection.getType();
            String iso = selection.getGeoArea().getIsoKey();
            if (type == SelectionType.BEEN) {
                summary.getBeen().add(iso);
            } else {
                summary.getWant().add(iso);
            }

            summaries.put(experience, summary);
        }

        return new ArrayList<>(summaries.values());
    }

    public Boolean handleBatchSelections(User user, ExperienceAreaBatchSelectionsDTO batchSelections) {
        for (ExperienceBatchSelectionDTO areaSelections : batchSelections.getBatch()) {
            String areaIsoCode = areaSelections.getIsoCode();
            List<ExperienceAreaSelectionDTO> selections = areaSelections.getSelections();
            if (!handleAreaSelection(user, areaIsoCode, selections)) {
                return false;
            }
        }

        return true;
    }

    public Boolean handleAreaSelection(User user, String areaIsoCode, List<ExperienceAreaSelectionDTO> selections) {
        final GeoArea area = getGeoAreaVerifyingIfItExists(areaIsoCode);
        selections.forEach((s) -> processSelection(user, area, s));
        userExperienceAreaRepository.flush();
        return true;
    }

    public Boolean handlePreferredExperienceSelections(User user, ExperienceAreaSelectionsDto selections) {
        selections.getSelections().forEach((s) -> processPreferredExperience(user, s));
        userExperienceRepository.flush();
        return true;
    }

    public ExperienceByAreaDTO getUserExperienceSelectionsByAreaIsoCode(User user, String areaIsoCode) {
        final GeoArea area = getGeoAreaVerifyingIfItExists(areaIsoCode);
        final List<UserExperienceArea> selections = userExperienceAreaRepository.findAllByUserAndGeoArea(user, area);

        List<ExperienceDTO> been = new ArrayList<>();
        List<ExperienceDTO> want = new ArrayList<>();
        for (UserExperienceArea selection : selections) {
            Experience experience = selection.getExperience();
            ExperienceDTO dto = buildExperienceDTO(experience);

            if (selection.getType() == SelectionType.BEEN) {
                been.add(dto);
            } else {
                want.add(dto);
            }
        }

        return new ExperienceByAreaDTO(been, want);
    }

    public ExperienceByTypeDTO getExperienceSelectionsByExperienceId(User user, Long experienceId) {
        final Experience experience = findExperienceById(experienceId);
        final List<UserExperienceArea> selections = userExperienceAreaRepository.findAllByUserAndExperience(user, experience);

        List<String> been = new ArrayList<>();
        List<String> want = new ArrayList<>();
        for (UserExperienceArea selection : selections) {
            GeoArea geoArea = selection.getGeoArea();
            String isoCode = geoArea.getIsoKey();

            if (selection.getType() == SelectionType.BEEN) {
                been.add(isoCode);
            } else {
                want.add(isoCode);
            }
        }

        return new ExperienceByTypeDTO(been, want);
    }

    public ExperienceDTO buildExperienceDTO(Experience experience) {
        final ExperienceDTO dto = new ExperienceDTO()
                .setId(experience.getId())
                .setName(experiencesTranslationService.getExperienceName(experience))
                .setIconUrl(experience.getIconUrl())
                .setEtag(generateEtagForExperience(experience))
                .setFile(experience.getFile());

        final Date dealsMaxModifiedTime = dealsService.getMaxModificationTimeOfDealsByExperienceId(experience.getId());
        if (dealsMaxModifiedTime != null) {
            dto.setDealsEtag(ETagHashUtils.buildHashedValueForTheETagFromADate(dealsMaxModifiedTime));
        }

        return dto;
    }

    private void processSelection(User user, GeoArea area, ExperienceAreaSelectionDTO selection) {
        final Experience experience = findExperienceById(selection.getExperienceId());
        selectTheExperience(user, area, experience, selection.getType());
    }

    private void processPreferredExperience(User user, ExperienceAreaSelectionDTO s) {
        final Experience experience = findExperienceById(s.getExperienceId());
        SelectionType type = s.getType();
        if (type == SelectionType.LIVED || type == SelectionType.WANT) {
            throw new RuntimeException("only BEEN or CLEAR selection types are support for preferred experiences");
        }

        UserPreferredExperience preferredExperience = userExperienceRepository.findByUserAndExperience(user, experience);

        if (preferredExperience == null && type == SelectionType.BEEN) {
            preferredExperience = new UserPreferredExperience();
            preferredExperience.setExperience(experience);
            preferredExperience.setUser(user);
            preferredExperience.setTimestamp(LocalDateTime.now());
            userExperienceRepository.save(preferredExperience);
        } else if (preferredExperience != null && type == SelectionType.CLEAR) {
            userExperienceRepository.delete(preferredExperience);
        }
    }

    private Experience findExperienceById(Long id) {
        final Optional<Experience> experience = experienceRepository.findById(id);
        if (experience.isEmpty()) {
            throw new ExperienceNotFoundException(id);
        }
        return experience.get();
    }

    private void selectTheExperience(User user, GeoArea area, Experience experience, SelectionType type) {
        if (type == SelectionType.LIVED) {
            throw new RuntimeException("LIVED selections not supported for experiences");
        }

        // Verify that this experience is allowed to be selected
        Experience validated = experienceRepository.findByExperienceIdAndAreaIsoCode(experience.getId(), area.getIsoKey());
        if (validated == null) {
            throw new RuntimeException(experience.getName() + " not available for selection in " + area.getName());
        }

        UserExperienceArea selection = userExperienceAreaRepository.findByUserAndExperienceAndGeoArea(user, experience, area);

        if (selection == null && type != SelectionType.CLEAR) {
            selection = new UserExperienceArea(user, experience, area, type);
        }

        // Nothing more to do here
        if (selection == null) {
            return;
        }

        selection.setTimestamp(LocalDateTime.now());
        selection.setType(type);
        saveSelection(selection);
    }

    private void saveSelection(UserExperienceArea selection) {
        // Delete this selection its set to clear
        if (SelectionType.CLEAR.equals(selection.getType())) {
            userExperienceAreaRepository.delete(selection);
            return;
        }

        userExperienceAreaRepository.save(selection);
    }

    public Date getMaxModificationTimeOfExperiences() {
        Date maxDate = experienceRepository.getMaxModificationTime();
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    public Date getMaxModificationTimeOfAnExperienceById(Long id) {
        Date maxDate = experienceRepository.getMaxModificationTimeOfExperienceById(id);
        if (maxDate == null) {
            return dateTools.defaultLastModificationTime();
        }
        return maxDate;
    }

    private String generateEtagForExperience(Experience e) {
        final Date date = getMaxModificationTimeOfAnExperienceById(e.getId());
        return ETagHashUtils.buildHashedValueForTheETagFromADate(date);
    }
}
