package com.arrivinginhighheels.visited.backend.features.cities;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include;

@Getter
@JsonInclude(Include.NON_NULL)
public class CityDto {
    private Long id;
    private String name;
    private List<Double> coordinate;
    private String levelOneIso;
    private String levelTwoIso;

    public CityDto() { }

    public CityDto setId(final Long id) {
        this.id = id;
        return this;
    }

    public CityDto setName(final String name) {
        this.name = name;
        return this;
    }

    public CityDto setCoordinate(final double longitude, final double latitude) {
       this.coordinate = Arrays.asList(longitude, latitude);
       return this;
    }

    public CityDto setLevelOneIso(String levelOneIso) {
        this.levelOneIso = levelOneIso;
        return this;
    }

    public CityDto setLevelTwoIso(String levelTwoIso) {
        this.levelTwoIso = levelTwoIso;
        return this;
    }
}
