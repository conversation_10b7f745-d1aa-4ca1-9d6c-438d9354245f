package com.arrivinginhighheels.visited.backend.features.emails;

import java.util.List;
import java.util.Optional;

public record SendBulkDTO(
        String friendlyName,
        String fromAddress,
        String subject,
        String htmlTemplate,
        List<String> customList,
        Optional<String> scheduledDateTime) {

    boolean shouldSendImmediately() {
        return scheduledDateTime().isEmpty();
    }
}
