package com.arrivinginhighheels.visited.backend.features.iap;

import com.arrivinginhighheels.visited.backend.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface UserPurchasesRepository extends JpaRepository<UserPurchase, Long> {
    void deleteByUser(User user);
    List<UserPurchase> findAllByUser(User user);
    List<UserPurchase> findAllByUserAndStatus(User user, UserPurchaseStatus status);
    UserPurchase findByUserAndProductId(User user, String productId);
    Long countByUserAndProductIdIn(User user, List<String> productIds);

    Optional<UserPurchase> findByTransactionId(String transactionId);

    @Query("""
        SELECT up FROM UserPurchase up
        WHERE up.expirationDate IS NOT NULL
          AND up.expirationDate < CURRENT_TIMESTAMP
          AND up.status = 'ACTIVE'
          AND up.autoRenew = false
    """)
    List<UserPurchase> findExpiredPurchases();
}
