package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.dto.AreaSimpleDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class InspirationDTO {
    private Long id;
    private String name;
    private AreaSimpleDTO geoArea;
    private String imageUrl;
    private String imageBlurHash;
}
