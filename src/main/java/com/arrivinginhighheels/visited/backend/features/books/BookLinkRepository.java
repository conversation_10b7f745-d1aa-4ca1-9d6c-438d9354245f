package com.arrivinginhighheels.visited.backend.features.books;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.features.todoLists.TodoList;
import com.arrivinginhighheels.visited.backend.model.BookLink;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface BookLinkRepository extends JpaRepository<BookLink, Long> {

    @Query("select links.book from AreaBookLink links where links.area = ?1")
    List<BookLink> findBooksByArea(GeoArea area);

    @Query("select links.book from TodoListBookLink links where links.todoList = ?1")
    List<BookLink> findBooksByTodoList(TodoList list);

    @Query("select links.book from ExperienceBookLink links where links.experience = ?1")
    List<BookLink> findBooksByExperience(Experience experience);
}
