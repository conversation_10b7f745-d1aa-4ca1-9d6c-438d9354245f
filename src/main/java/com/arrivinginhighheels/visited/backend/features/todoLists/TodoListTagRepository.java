package com.arrivinginhighheels.visited.backend.features.todoLists;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TodoListTagRepository extends JpaRepository<TodoTag, Long> {

    @Query(value = """
        select
    	    tag.name, count(xref.tag_id) as count
        from user_todo_list_items as item
        INNER join todo_list_tags_xref as xref on xref.list_id = item.list_id
        INNER join todo_list_tags as tag on xref.tag_id = tag.id
        where 
            item.user_id = ?1 and 
            item.selection_type = 'BEEN' AND
            tag.exclude_from_travel_type = false
        group by tag.name
        order by count DESC;
    """, nativeQuery = true)
    List<Object[]> fetchTravellerType(Long userId);

    @Query(value = """
        select
    	    translation.name, count(xref.tag_id) as count
        from user_todo_list_items as item
        INNER join todo_list_tags_xref as xref on xref.list_id = item.list_id
        INNER join todo_list_tags_translations as translation on xref.tag_id = translation.tag_id
        INNER join todo_list_tags as tag on xref.tag_id = tag.id
        where
    	    item.user_id = ?1 and
    	    item.selection_type = 'BEEN' and
    	    tag.exclude_from_travel_type = false and
    	    translation.supported_language_id = ?2
        group by translation.name
        order by count DESC;
    """, nativeQuery = true)
    List<Object[]> fetchLocalizedTravellerType(Long userId, Long languageId);
}
