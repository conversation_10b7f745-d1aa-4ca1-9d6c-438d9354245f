package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.Experience;
import com.arrivinginhighheels.visited.backend.model.ExperienceTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ExperienceTranslationRepository extends JpaRepository<ExperienceTranslation, Long> {
    ExperienceTranslation findByExperienceAndSupportedLanguage(Experience e, SupportedLanguage lang);
}
