package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface TodoTagTranslationRepository extends JpaRepository<TodoTagTranslation, Long> {

    List<TodoTagTranslation> findAllBySupportedLanguage(SupportedLanguage language);

    TodoTagTranslation findByTagAndSupportedLanguage(TodoTag tag, SupportedLanguage language);
}
