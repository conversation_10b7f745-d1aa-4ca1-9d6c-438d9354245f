package com.arrivinginhighheels.visited.backend.features.stats;

import com.arrivinginhighheels.visited.backend.features.todoLists.TodoListTagRepository;
import com.arrivinginhighheels.visited.backend.features.todoLists.TodoListTranslationService;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.model.User;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class StatsService {

    private final TodoListTagRepository todoListTagRepository;

    private final TodoListTranslationService translationService;

    @PersistenceContext
    private EntityManager entityManager;

    public StatsService(TodoListTagRepository todoListTagRepository, TodoListTranslationService translationService) {
        this.todoListTagRepository = todoListTagRepository;
        this.translationService = translationService;
    }

    public List<TravelTypeDto> getTravellerTypeByUser(User user) {
        if (translationService.isInEnglish()) {
            final var results = todoListTagRepository.fetchTravellerType(user.getId());
            return parseTravellerType(results);
        }

        var language = translationService.getCurrentLanguage();
        if (language == null) {
            language = new SupportedLanguage();
            language.setId(1L);
            language.setCode(SupportedLanguage.ENGLISH_CODE);
        }

        final var results = todoListTagRepository.fetchLocalizedTravellerType(user.getId(), language.getId());
        return parseTravellerType(results);
    }

    private List<TravelTypeDto> parseTravellerType(List<Object[]> queryResults) {
        return queryResults.stream().map(e -> {
            final var name = (String) e[0];
            final var count = (Long) e[1];

            return new TravelTypeDto(name, count);
        }).toList();
    }

    private static final long BUDGET_HOTEL_ID = 305;
    private static final long MIDSCALE_HOTEL_ID = 326;
    private static final long LUXURY_HOTEL_ID = 323;

    public Optional<HotelPreferenceDto> getHotelPreference(User user) {
        final var sql = "select " +
                "count(list_id), list_id " +
                "from user_todo_list_items " +
                "where user_id = ?1 and list_id in (305, 323, 326) " +
                "group by list_id;";

        final var query = entityManager.createNativeQuery(sql);
        query.setParameter(1, user.getId());
        final var travelerType = getTravelerType(query);

        if (Double.isNaN(travelerType) || Double.isInfinite(travelerType)) {
            return Optional.empty();
        }

        return Optional.of(new HotelPreferenceDto(travelerType, 1, 2, 3));
    }

    private double getTravelerType(Query query) {
        final var results = query.getResultList();

        var budget = 0d;
        var midscale = 0d;
        var luxury = 0d;

        for (var result : results) {
            final var object = (Object[]) result;
            final var listId = (Long) object[1];
            final var total = (Long) object[0];

            if (listId == BUDGET_HOTEL_ID) {
                budget = total;
            } else if (listId == MIDSCALE_HOTEL_ID) {
                midscale = total;
            } else if (listId == LUXURY_HOTEL_ID) {
                luxury = total;
            }
        }

        final var sum = budget + midscale + luxury;
        final var proratedSum = budget + (midscale * 2) + (luxury * 3);

        return proratedSum / sum;
    }
}
