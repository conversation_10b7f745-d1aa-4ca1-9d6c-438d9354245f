package com.arrivinginhighheels.visited.backend.features.emails;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.TRACKED_LINK_URL;

@RestController
@RequestMapping(path = TRACKED_LINK_URL)
public class TrackedLinkController {

    final TrackedLinkService service;

    public TrackedLinkController(TrackedLinkService service) {
        this.service = service;
    }

    @GetMapping(path = "{hash}")
    @ResponseBody
    public ResponseEntity<String> click(@PathVariable String hash, final HttpServletRequest request) {
        final var ipAddress = request.getRemoteAddr();
        final var urlRedirect = service.clickTrackedLink(hash, ipAddress);
        return ResponseEntity.status(302)
                .header(HttpHeaders.LOCATION, urlRedirect)
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .header(HttpHeaders.PRAGMA, "no-cache")
                .header(HttpHeaders.EXPIRES, "0")
                .build();
    }
}
