package com.arrivinginhighheels.visited.backend.features.poster;

import com.arrivinginhighheels.visited.backend.dto.PaymentIntentDto;
import com.arrivinginhighheels.visited.backend.dto.PosterRequest;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.HttpRequestUtils;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/v2/stripe")
public class StripeController {

    private final LoggedInUserUtil userUtil;

    private final StripeService service;

    private final HttpRequestUtils httpRequestUtils;

    private final ResponseHelper responseHelper = new ResponseHelper();

    public StripeController(LoggedInUserUtil userUtil, StripeService service, HttpRequestUtils httpRequestUtils) {
        this.userUtil = userUtil;
        this.service = service;
        this.httpRequestUtils = httpRequestUtils;
    }

    @PostMapping("/webhook")
    public ResponseEntity onWebHookReceived(
            final HttpServletRequest request,
            @RequestBody String data) {
        var stripeSignature = request.getHeader("Stripe-Signature");
        return  service.processWebhook(stripeSignature, data);
    }

    @PostMapping("/coupon")
    public CouponDto validateCoupon(@RequestBody CouponRequest couponRequest) {
        return service.validateCoupon(couponRequest);
    }

    @PostMapping("/payment")
    public PaymentIntentDto orderPoster(final HttpServletRequest request,
                                        @RequestBody PosterRequest posterRequest) {
        final var user = userUtil.getLoggedInUser(request);
        return service.buyPoster(user, posterRequest);
    }

    @GetMapping("/productDetails")
    public ResponseEntity<ProductPurchasingInformation> getProductDetails(HttpServletRequest request) {
        var product = service.getProductInformation(request);
        return responseHelper.standardCacheableResponse(product);
    }

    // These are debug services, which is why they are only accessible to admin users
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @GetMapping("/debug")
    public Map<String, String> getCurrency(HttpServletRequest request) {
       return service.getCurrencyDebug(request);
    }
}
