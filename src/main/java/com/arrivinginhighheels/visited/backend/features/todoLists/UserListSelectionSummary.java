package com.arrivinginhighheels.visited.backend.features.todoLists;

import java.util.Objects;

final public class UserListSelectionSummary {
    private final Long listId;
    private final Long count;

    public UserListSelectionSummary(Long listId, Long count) {
        this.count = count;
        this.listId = listId;
    }

    public Long getListId() {
        return listId;
    }

    public Long getCount() {
        return count;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (UserListSelectionSummary) obj;
        return Objects.equals(this.count, that.count) &&
                Objects.equals(this.listId, that.listId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, listId);
    }

    @Override
    public String toString() {
        return "UserListSelectionSummary[" +
                "count=" + count + ", " +
                "listId=" + listId + ']';
    }
}
