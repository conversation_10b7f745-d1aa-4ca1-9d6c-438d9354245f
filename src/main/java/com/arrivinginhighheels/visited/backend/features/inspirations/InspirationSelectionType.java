package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.model.SelectionType;

public enum InspirationSelectionType {
    PENDING(-1), DELETE(0), WANT(1), BEEN(2);

    private final int weight;

    InspirationSelectionType(int weight) {this.weight = weight; }

    public int getWeight() {
        return weight;
    }

    public SelectionType toGeoSelectionType() {
        if (this.equals(InspirationSelectionType.WANT)) {
            return SelectionType.WANT;
        }

        if (this.equals(InspirationSelectionType.BEEN)) {
            return SelectionType.BEEN;
        }

        throw new RuntimeException("only WANT and BEEN can be converted to a SelectionType");
    }
}
