package com.arrivinginhighheels.visited.backend.features.itineraries;

import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.AreaSimpleDTO;
import com.arrivinginhighheels.visited.backend.exception.GeoAreaNotFoundException;
import com.arrivinginhighheels.visited.backend.model.*;
import com.arrivinginhighheels.visited.backend.features.cities.CityDto;
import com.arrivinginhighheels.visited.backend.features.cities.UserCitiesRepository;
import com.arrivinginhighheels.visited.backend.features.experiences.ExperienceDTO;
import com.arrivinginhighheels.visited.backend.features.experiences.UserExperienceAreaRepository;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.features.inspirations.InspirationDTO;
import com.arrivinginhighheels.visited.backend.features.inspirations.UserInspirationsRepository;
import com.arrivinginhighheels.visited.backend.features.todoLists.*;
import com.arrivinginhighheels.visited.backend.repository.GeoAreaRepository;
import com.arrivinginhighheels.visited.backend.service.translations.InspirationTranslationService;
import com.arrivinginhighheels.visited.backend.utils.AdaptiveImageUtils;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityManager;
import jakarta.persistence.FlushModeType;
import jakarta.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ItineraryService {

    private final UserPurchasesRepository userPurchasesRepository;
    private final GeoAreaRepository areaRepository;
    private final UserExperienceAreaRepository userExperienceRepository;
    private final UserInspirationsRepository inspirationsRepository;
    private final UserTodoListItemRepository todoListItemRepository;
    private final InspirationTranslationService translationService;
    private final AdaptiveImageUtils imageUtils;
    private final TodoListService todoListService;
    private final UserItineraryRepository userItineraryRepository;
    private final UserCitiesRepository userCitiesRepository;
    private final UserTodoListItemRepository userTodoListItemRepository;
    private final YamlConfig yamlConfig;

    @PersistenceContext
    private EntityManager em;

    public ItineraryService(
            UserPurchasesRepository userPurchasesRepository,
            GeoAreaRepository areaRepository,
            UserExperienceAreaRepository userExperienceRepository,
            UserInspirationsRepository inspirationsRepository,
            UserTodoListItemRepository todoListItemRepository,
            InspirationTranslationService translationService,
            AdaptiveImageUtils imageUtils,
            TodoListService todoListService,
            UserItineraryRepository userItineraryRepository,
            UserCitiesRepository userCitiesRepository,
            UserTodoListItemRepository userTodoListItemRepository,
            YamlConfig yamlConfig
    ) {
        this.userPurchasesRepository = userPurchasesRepository;
        this.areaRepository = areaRepository;
        this.userExperienceRepository = userExperienceRepository;
        this.inspirationsRepository = inspirationsRepository;
        this.todoListItemRepository = todoListItemRepository;
        this.translationService = translationService;
        this.imageUtils = imageUtils;
        this.todoListService = todoListService;
        this.userItineraryRepository = userItineraryRepository;
        this.userCitiesRepository = userCitiesRepository;
        this.userTodoListItemRepository = userTodoListItemRepository;
        this.yamlConfig = yamlConfig;
    }

    List<ItinerarySummaryDTO> getItinerarySummaries(
            User user,
            double resolution,
            boolean useUpdatedQuery) {
        var query = em.createNativeQuery(useUpdatedQuery ? aggregationQueryV2 : aggregationQuery);
        query.setParameter("user_id", user.getId());
        query.setFlushMode(FlushModeType.AUTO);
        var results = (List<Object[]>) query.getResultList();

        return results
                .stream()
                .map((e) -> buildItinerarySummaryDto(e, resolution))
                .collect(Collectors.toList());
    }

    public ItineraryV2dto getAreaDetailsV2(User user, String isoCode, double resolution) {
        var area = areaRepository.findByIsoKey(isoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(isoCode));
        var lang = translationService.getCurrentLanguage();

        final var experienceDtos = getExperienceDTOS(user, lang, area);
        final var inspirationDtos = getInspirationDTOS(user, resolution, lang, area);
        final var cityDtos = getCityDtos(user, lang, area);

        List<TodoListItemDTO> placesDtos = new ArrayList<>();
        List<TodoListItemDTO> foodDtos = new ArrayList<>();

        if (lang.isEnglish()) {
            List<TodoListXref> places = new ArrayList<>();
            List<TodoListXref> food = new ArrayList<>();

            final var items = userTodoListItemRepository.findUserSelectedWantByArea(user, area);
            for (var xref : items) {
                final var type = xref.getTodoList().getType();
                if (type.equals(TodoListType.PLACE)) {
                    places.add(xref);
                } else if (type.equals(TodoListType.FOOD)) {
                    food.add(xref);
                } else if (type.equals(TodoListType.CITIES)) {
                    addToCityDtosIfNeeded(xref.getListItem().getCity(), cityDtos);
                }

                placesDtos = places.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
                foodDtos = food.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
            }
        } else {
            List<LocalizedTodoListItem> places = new ArrayList<>();
            List<LocalizedTodoListItem> food = new ArrayList<>();

            final var items = userTodoListItemRepository.findUserSelectedLocalizedWantByArea(user, area, lang);
            for (var localizedItem : items) {
                final var type = localizedItem.getList().getType();
                if (type.equals(TodoListType.PLACE)) {
                    places.add(localizedItem);
                } else if (type.equals(TodoListType.FOOD)) {
                    food.add(localizedItem);
                } else if (type.equals(TodoListType.CITIES)) {
                    addToCityDtosIfNeeded(localizedItem.getItem().getCity(), cityDtos,
                            localizedItem.getTranslation().getName());
                }

                placesDtos = places.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
                foodDtos = food.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
            }
        }

        return new ItineraryV2dto(
                inspirationDtos,
                experienceDtos,
                cityDtos,
                placesDtos,
                foodDtos);
    }

    private void addToCityDtosIfNeeded(City city, ArrayList<CityDto> cityDtos, String translation) {
        if (city == null) {
            return;
        }

        final var exists = cityDtos.stream().anyMatch(e -> e.getId().equals(city.getId()));

        if (exists) {
            return;
        }

        final var dto = buildCityDto(city);

        if (dto != null) {
            cityDtos.add(dto);
        }

    }

    private void addToCityDtosIfNeeded(City city, ArrayList<CityDto> cityDtos) {
        addToCityDtosIfNeeded(city, cityDtos, null);
    }

    private ArrayList<CityDto> getCityDtos(User user, SupportedLanguage lang, GeoArea area) {
        final var cities = lang.isEnglish()
                ? userCitiesRepository.findAllByUserAndTypeAndArea(user, SelectionType.WANT, area)
                : userCitiesRepository.findAllByUserAndTypeAndAreaAndLanguage(user, SelectionType.WANT, area, lang);

        return cities
                .stream()
                .map(this::buildCityDto)
                .collect(Collectors.toCollection(ArrayList::new)); // Explicitly mutatable
    }

    private CityDto buildCityDto(City city) {
        return new CityDto()
                .setId(city.getId())
                .setName(city.getName())
                .setCoordinate(city.getLongitude().doubleValue(), city.getLatitude().doubleValue())
                .setLevelOneIso(city.getGeoAreaLevelOne().getIsoKey())
                .setLevelTwoIso(city.getGeoAreaLevelTwo() != null ? city.getGeoAreaLevelTwo().getIsoKey() : null);
    }

    ItineraryDTO getByArea(User user, String isoCode, double resolution) {
        var area = areaRepository.findByIsoKey(isoCode)
                .orElseThrow(() -> new GeoAreaNotFoundException(isoCode));
        var lang = translationService.getCurrentLanguage();

        final var experienceDtos = getExperienceDTOS(user, lang, area);
        final var inspirationDtos = getInspirationDTOS(user, resolution, lang, area);

        List<TodoListItemDTO> listItemsDto;
        if (lang.isEnglish()) {
            var listItems = todoListItemRepository.findUserSelectedWantByArea(user, area);
            listItemsDto = listItems.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
        } else {
            var listItems = todoListItemRepository.findUserSelectedLocalizedWantByArea(user, area, lang);
            listItemsDto = listItems.stream().map((e) -> todoListService.buildTodoListItemDto(e, resolution)).toList();
        }

        var dto = new ItineraryDTO();
        if (!experienceDtos.isEmpty()) {
            dto.setExperiences(experienceDtos);
        }

        if (!inspirationDtos.isEmpty()) {
            dto.setInspirations(inspirationDtos);
        }

        if (!listItemsDto.isEmpty()) {
            dto.setListItems(listItemsDto);
        }

        return dto;
    }

    private List<InspirationDTO> getInspirationDTOS(User user, double resolution, SupportedLanguage lang,
            GeoArea area) {
        List<InspirationDTO> inspirationDtos;
        if (lang.isEnglish()) {
            var inspiration = inspirationsRepository.findWantInspirationsByUserAndArea(user, area);
            inspirationDtos = inspiration
                    .stream()
                    .map((e) -> buildInspirationDto(e, Optional.empty(), Optional.empty(), resolution))
                    .toList();
        } else {
            var inspirations = inspirationsRepository.findWantInspirationsByUserAreaAndLanguage(user, area, lang);
            inspirationDtos = inspirations
                    .stream()
                    .map(e -> buildInspirationDto(e.inspiration(), Optional.of(e.translation()),
                            Optional.of(e.areaTranslation()), resolution))
                    .toList();
        }
        return inspirationDtos;
    }

    private List<ExperienceDTO> getExperienceDTOS(User user, SupportedLanguage lang, GeoArea area) {
        List<ExperienceDTO> experienceDtos;
        if (lang.isEnglish()) {
            var experiences = userExperienceRepository.findWantExperienceByUserGeoArea(user, area);
            experienceDtos = experiences
                    .stream()
                    .map((e) -> buildExperienceDto(e, Optional.empty()))
                    .toList();
        } else {
            var experiences = userExperienceRepository.findWantExperienceByUserGeoAreaAndLanguage(user, area, lang);
            experienceDtos = experiences
                    .stream()
                    .map((e) -> buildExperienceDto(e.experience(), Optional.of(e.translation())))
                    .toList();
        }
        return experienceDtos;
    }

    private ExperienceDTO buildExperienceDto(
            Experience experience,
            Optional<ExperienceTranslation> translation) {
        return new ExperienceDTO()
                .setId(experience.getId())
                .setName(translation.isPresent() ? translation.get().getName() : experience.getName())
                .setIconUrl(experience.getIconUrl())
                .setFile(experience.getFile());
    }

    private InspirationDTO buildInspirationDto(
            Inspiration inspiration,
            Optional<InspirationTranslation> translation,
            Optional<GeoAreaTranslation> geoAreaTranslation,
            double resolution) {
        return new InspirationDTO(
                inspiration.getId(),
                translation.isPresent() ? translation.get().getName() : inspiration.getName(),
                new AreaSimpleDTO(
                        inspiration.getGeoArea().getId(),
                        inspiration.getGeoArea().getIsoKey(),
                        geoAreaTranslation.isPresent() ? geoAreaTranslation.get().getName()
                                : inspiration.getGeoArea().getName()),
                imageUtils.getResolution(inspiration.getImage(), resolution),
                inspiration.getImage().getBlurHash());
    }

    private ItinerarySummaryDTO buildItinerarySummaryDto(Object[] rawResponse, double resolution) {
        var dto = new ItinerarySummaryDTO();

        dto.setAmount(unboxRawLong(rawResponse[1]));

        var iso = (String) rawResponse[0];
        var image = new AdaptiveImage();
        image.setOneUrl((String) rawResponse[2]);
        image.setOnePointFiveUrl((String) rawResponse[3]);
        image.setTwoUrl((String) rawResponse[4]);
        image.setThreeUrl((String) rawResponse[5]);
        image.setFourUrl((String) rawResponse[6]);

        var thumbnail = imageUtils.getResolution(image, resolution);

        dto.setArea(new ItineraryArea(iso, thumbnail));

        return dto;
    }

    private Long unboxRawLong(Object raw) {
        return ((BigDecimal) raw).longValue();
    }

    public boolean hasAccess(User user) {
        final var purchased = userPurchasesRepository.countByUserAndProductIdIn(user, Arrays.asList(
                yamlConfig.getUnlockItinerariesBundleId(),
                yamlConfig.getProLifetimeBundleId(),
                yamlConfig.getProSubscriptionAnnualBundleId(),
                yamlConfig.getProSubscriptionMonthlyBundleId()
        ));

        return purchased > 0;
    }

    Optional<UserItineraryDto> getUserItinerary(User user, String isoCode) {
        final var area = areaRepository
                .findByIsoKey(isoCode)
                .orElseThrow();

        final var maybeUserItinerary = userItineraryRepository
                .findByUserAndGeoArea(user, area);

        if (maybeUserItinerary.isEmpty()) {
            return Optional.empty();
        }

        final var userItinerary = maybeUserItinerary.get();

        final var dto = new UserItineraryDto(
                userItinerary.getStartDate(),
                userItinerary.getEndDate(),
                buildList(userItinerary.getHotels()),
                buildList(userItinerary.getNotes()));

        return Optional.of(dto);
    }

    boolean saveUserItinerary(User user, String isoCode, UserItineraryDto dto) {
        final var area = areaRepository.findByIsoKey(isoCode).orElseThrow();
        final var userItinerary = userItineraryRepository
                .findByUserAndGeoArea(user, area)
                .orElse(new UserItinerary());

        userItinerary.setUser(user);
        userItinerary.setGeoArea(area);
        userItinerary.setStartDate(dto.startDate());
        userItinerary.setEndDate(dto.endDate());
        userItinerary.setHotels(buildNativeStringArray(dto.hotels()));
        userItinerary.setNotes(buildNativeStringArray(dto.notes()));
        userItinerary.setLastModificationTime(new Date());

        try {
            userItineraryRepository.save(userItinerary);
            return true;
        } catch (DataIntegrityViolationException e) {
            return false;
        }
    }

    private List<String> buildList(String[] array) {
        if (array == null) {
            return null;
        }

        return Arrays.stream(array).toList();
    }

    private String[] buildNativeStringArray(List<String> list) {
        if (list == null) {
            return null;
        }

        return list.toArray(new String[0]);
    }

    private static final String aggregationQuery = """
            select
              results.iso_key,
              sum(amount) as amount,
              results.one_x_url,
              results.one_point_five_x_url,
              results.two_x_url,
              results.three_x_url,
              results.four_x_url

            from (
                -- Experiences
                select
                  geoareas.iso_key,
                  count(u.user_id) as amount,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url
                from user_experience_areas as u
                INNER join geoareas on u.geo_area_id = geoareas.id
                INNER join geoarea_details as details on u.geo_area_id = details.geo_area_id
                INNER join adaptive_images as image on details.thumbnail_url_id = image.id
                where u.user_id = :user_id
                AND selection_type = 'WANT'
                group by
                  u.user_id,
                  geoareas.iso_key,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url

                UNION ALL

                -- Inspirations for top level
                select
                  geoareas.iso_key,
                  count(inspiration_id) as amount,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url
                from user_inspirations
                INNER join inspirations on user_inspirations.inspiration_id = inspirations.id
                INNER join geoareas on inspirations.geo_area_id = geoareas.id
                INNER join geoarea_details as details on geoareas.id = details.geo_area_id
                INNER join adaptive_images as image on details.thumbnail_url_id = image.id
                where
                  user_id = :user_id
                  AND selection_type = 'WANT'
                  AND geoareas.parent_id is NULL
                group by
                  inspirations.geo_area_id,
                  geoareas.iso_key,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url

                UNION ALL

                -- Inspirations For 2nd Level Subdivisions (Might need to make recursive)
                select
                  parent.iso_key,
                  count(inspiration_id) as amount,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url
                from user_inspirations
                INNER join inspirations on user_inspirations.inspiration_id = inspirations.id
                INNER join geoareas as child on inspirations.geo_area_id = child.id
                INNER join geoareas as parent on parent.id = child.parent_id
                INNER join geoarea_details as details on parent.id = details.geo_area_id
                INNER join adaptive_images as image on details.thumbnail_url_id = image.id
                where
                  user_id = :user_id
                  AND selection_type = 'WANT'
                  AND child.parent_id is not null
                group by
                  parent.iso_key,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url

                UNION ALL

                -- Todo Lists
                select
                  geoareas.iso_key,
                  count(list_item_id) as amount,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url
                from user_todo_list_items
                INNER join todo_list_items on user_todo_list_items.list_item_id = todo_list_items.id
                INNER join geoareas on todo_list_items.geo_area_id = geoareas.id
                INNER join geoarea_details as details on geoareas.id = details.geo_area_id
                INNER join adaptive_images as image on details.thumbnail_url_id = image.id
                where
                user_id = :user_id
                AND geoareas.iso_key is not null
                AND selection_type = 'WANT'
                group by
                  todo_list_items.geo_area_id,
                  geoareas.iso_key,
                  image.one_x_url,
                  image.one_point_five_x_url,
                  image.two_x_url,
                  image.three_x_url,
                  image.four_x_url
                ) as results
            group by
            results.iso_key,
            results.one_x_url,
            results.one_point_five_x_url,
            results.two_x_url,
            results.three_x_url,
            results.four_x_url
            ORDER BY amount DESC;
            """;

    private static final String aggregationQueryV2 = """
            SELECT
            	results.iso_key,
            	sum(amount) AS amount,
            	results.one_x_url,
            	results.one_point_five_x_url,
            	results.two_x_url,
            	results.three_x_url,
            	results.four_x_url
            FROM (
            	-- Experiences
            	SELECT
            		geoareas.iso_key,
            		count(u.user_id) AS amount,
            		image.one_x_url,
            		image.one_point_five_x_url,
            		image.two_x_url,
            		image.three_x_url,
            		image.four_x_url
            	FROM
            		user_experience_areas AS u
            	INNER JOIN geoareas ON u.geo_area_id = geoareas.id
            	INNER JOIN geoarea_details AS details ON u.geo_area_id = details.geo_area_id
            	INNER JOIN adaptive_images AS image ON details.thumbnail_url_id = image.id
            WHERE
            	u.user_id = :user_id
            	AND selection_type = 'WANT'
            GROUP BY
            	u.user_id,
            	geoareas.iso_key,
            	image.one_x_url,
            	image.one_point_five_x_url,
            	image.two_x_url,
            	image.three_x_url,
            	image.four_x_url
            UNION ALL
            -- CITIES
            SELECT
            	cities_and_lists.iso_key,
            	count(cities_and_lists.iso_key) AS amount,
            	cities_and_lists.one_x_url,
            	cities_and_lists.one_point_five_x_url,
            	cities_and_lists.two_x_url,
            	cities_and_lists.three_x_url,
            	cities_and_lists.four_x_url
            FROM (
            	-- SUBQUERY TO MERGE CITY SELECTIONS AND LIST SELECTIONS THAT ARE ALSO CITIES
            	SELECT
            		user_cities.id,
            		geoareas.iso_key,
            		image.one_x_url,
            		image.one_point_five_x_url,
            		image.two_x_url,
            		image.three_x_url,
            		image.four_x_url
            	FROM
            		user_cities
            	INNER JOIN cities ON cities.id = user_cities.city_id
            	INNER JOIN geoareas ON cities.level_one_geo_area_id = geoareas.id
            	INNER JOIN geoarea_details AS details ON geoareas.id = details.geo_area_id
            	INNER JOIN adaptive_images AS image ON details.thumbnail_url_id = image.id
            WHERE
            	user_cities.user_id = :user_id
            	AND user_cities.selection_type = 'WANT'
            UNION ALL
            --TODO LIST ITEMS THAT ARE ACTUALLY CITIES
            SELECT
            	todo_list_items.city_id AS id,
            	geoareas.iso_key,
            	image.one_x_url,
            	image.one_point_five_x_url,
            	image.two_x_url,
            	image.three_x_url,
            	image.four_x_url
            FROM
            	user_todo_list_items
            	INNER JOIN todo_list_items ON todo_list_items.id = user_todo_list_items.user_id
            	INNER JOIN cities ON cities.id = todo_list_items.city_id
            	INNER JOIN geoareas ON cities.level_one_geo_area_id = geoareas.id
            	INNER JOIN geoarea_details AS details ON geoareas.id = details.geo_area_id
            	INNER JOIN adaptive_images AS image ON details.thumbnail_url_id = image.id
            WHERE
            	todo_list_items.city_id IS NOT NULL
            	AND user_todo_list_items.user_id = :user_id
            	AND user_todo_list_items.selection_type = 'WANT') AS cities_and_lists
            GROUP BY
            	cities_and_lists.iso_key,
            	cities_and_lists.one_x_url,
            	cities_and_lists.one_point_five_x_url,
            	cities_and_lists.two_x_url,
            	cities_and_lists.three_x_url,
            	cities_and_lists.four_x_url
            UNION ALL
            -- Places Todo Lists
            SELECT
            	geoareas.iso_key,
            	count(list_item_id) AS amount,
            	image.one_x_url,
            	image.one_point_five_x_url,
            	image.two_x_url,
            	image.three_x_url,
            	image.four_x_url
            FROM
            	user_todo_list_items
            	INNER JOIN todo_list_items ON user_todo_list_items.list_item_id = todo_list_items.id
            	INNER JOIN todo_lists ON user_todo_list_items.list_id = todo_lists.id
            	INNER JOIN geoareas ON todo_list_items.geo_area_id = geoareas.id
            	INNER JOIN geoarea_details AS details ON geoareas.id = details.geo_area_id
            	INNER JOIN adaptive_images AS image ON details.thumbnail_url_id = image.id
            WHERE
            	user_id = :user_id
            	AND todo_lists.type = 'PLACE'
            	AND geoareas.iso_key IS NOT NULL
            	AND selection_type = 'WANT'
            GROUP BY
            	todo_list_items.geo_area_id,
            	geoareas.iso_key,
            	image.one_x_url,
            	image.one_point_five_x_url,
            	image.two_x_url,
            	image.three_x_url,
            	image.four_x_url) AS results
            GROUP BY
            	results.iso_key,
            	results.one_x_url,
            	results.one_point_five_x_url,
            	results.two_x_url,
            	results.three_x_url,
            	results.four_x_url
            ORDER BY
            	amount DESC;
            """;
}
