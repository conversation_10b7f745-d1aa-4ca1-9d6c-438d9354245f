package com.arrivinginhighheels.visited.backend.features.emails;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.util.Optional;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;


@Service
public class TrackedLinkService {
    private final TrackedLinkRepository trackedLinkRepository;
    private final TrackedLinkClickRepository trackedLinkClickRepository;

    @Value("classpath:tracked_link_response.html")
    private Resource trackedLinkTemplate;

    public TrackedLinkService(
            TrackedLinkRepository trackedLinkRepository,
            TrackedLinkClickRepository trackedLinkClickRepository) throws NoSuchAlgorithmException {
        this.trackedLinkRepository = trackedLinkRepository;
        this.trackedLinkClickRepository = trackedLinkClickRepository;
    }

    public Optional<TrackedLink> createNewTrackedLink(String url, String title) {
        final var existing = trackedLinkRepository.findByUrl(url);
        if (existing.isPresent()) {
            return existing;
        }

        final var uuid = UUID.nameUUIDFromBytes(url.getBytes());
        final var shortened = uuid.toString().replaceAll("-", "").substring(0, 10);
        final var link = new TrackedLink();
        link.setUrl(url);
        link.setTitle(title);
        link.setHash(shortened);
        trackedLinkRepository.save(link);

        return Optional.of(link);
    }

    public String clickTrackedLink(String hash, String ipAddress){
        final var maybeLink = trackedLinkRepository.findByHash(hash);
        final var link = maybeLink.orElseThrow(() -> new RuntimeException("Link " + hash + " not found"));


        final var click = new TrackedLinkClick();
        click.setLink(link);
        click.setTimestamp(Instant.now());
        try {
            final var inet = InetAddress.getByName(ipAddress);
            click.setIpAddress(inet);
        } catch (UnknownHostException e) {
            // Just do nothing if it fails
        }

        trackedLinkClickRepository.save(click);

        return link.getUrl();

//        var logger = Logger.getGlobal();
//        var template = loadTemplate();
//        try {
//            template = template.replace("##redirect_url##", link.getUrl());
//        } catch (Exception e) {
//            logger.log(Level.WARNING, "Failed to replace redirect url", e);
//        }
//
//        try {
//            template = template.replace("##deal_name##", link.getUrl());
//        } catch (Exception e) {
//            logger.log(Level.WARNING, "Failed to replace deal name url", e);
//        }
//
//        return template;
    }

//    private String loadTemplate() {
//        try (Reader reader = new InputStreamReader(trackedLinkTemplate.getInputStream(), UTF_8)) {
//            return FileCopyUtils.copyToString(reader);
//        } catch (IOException e) {
//            throw new UncheckedIOException(e);
//        }
//    }

    public Optional<TrackedLink> findLink(String url) {
        try {
            var results = trackedLinkRepository.findByUrl(url);
            if (results.isEmpty()) {
                Logger.getGlobal().log(Level.INFO, "Cannot find tracking link: " + url);
                return results;
            }

            return results;
        } catch (Exception e) {
            Logger.getGlobal().log(Level.INFO, "Cannot find tracking link: " + url);
            return Optional.empty();
        }
    }
}
