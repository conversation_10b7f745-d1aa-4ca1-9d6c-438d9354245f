package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import com.arrivinginhighheels.visited.backend.model.Sponsor;
import lombok.Getter;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.Objects;

@Getter
@Entity
@Table(name = "todo_lists")
public class TodoList {

    @Id
    @SequenceGenerator(name = "lists_id_seq", sequenceName = "lists_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "lists_id_seq")
    private Long id;

    @NotNull
    @Column(length = 128, nullable = false, updatable = false)
    private String name;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "adaptive_image_id", updatable = false, insertable = false)
    private AdaptiveImage image;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "sponsor_id", updatable = false, insertable = false)
    private Sponsor sponsor;

    @NotNull
    private Integer count;

    private Integer ordinal;

    @Column(length = 15, updatable = false, name = "min_version")
    private String minVersion;

    @UpdateTimestamp
    @Column(name = "last_modification_time")
    private Date lastModificationTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", length = 12, nullable = false)
    private TodoListType type;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 12, nullable = false)
    private TodoListStatus status;


    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoList that = (TodoList) o;
        return Objects.equals(id, that.id) && Objects.equals(name, that.name) && Objects.equals(image, that.image)
                && Objects.equals(sponsor, that.sponsor)
                && Objects.equals(lastModificationTime, that.lastModificationTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, image, sponsor, lastModificationTime);
    }

    @Override
    public String toString() {
        return "ListEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", image=" + image +
                ", sponsor=" + sponsor +
                ", lastModificationTime=" + lastModificationTime +
                '}';
    }
}
