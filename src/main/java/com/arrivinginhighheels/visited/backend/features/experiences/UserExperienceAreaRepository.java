package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserExperienceAreaRepository extends JpaRepository<UserExperienceArea, Long> {

    void deleteByUser(User user);

    UserExperienceArea findByUserAndExperienceAndGeoArea(User user, Experience experience, GeoArea area);

    List<UserExperienceArea> findAllByUser(User user);

    List<UserExperienceArea> findAllByUserAndGeoArea(User user, GeoArea area);

    List<UserExperienceArea> findAllByUserAndExperience(User user, Experience experience);

    @Query("""
    SELECT new com.arrivinginhighheels.visited.backend.features.experiences.LocalizedExperiencePrep(u.experience, t)\s
    FROM UserExperienceArea u\s
    INNER JOIN ExperienceTranslation t ON u.experience = t.experience
    WHERE u.user = ?1 AND u.geoArea = ?2 AND u.type = 'WANT' AND t.supportedLanguage = ?3
    """)
    List<LocalizedExperiencePrep> findWantExperienceByUserGeoAreaAndLanguage(User user, GeoArea area, SupportedLanguage lang);

    @Query("""
    SELECT u.experience
    FROM UserExperienceArea u
    WHERE u.user = ?1 AND u.geoArea = ?2 AND u.type = 'WANT'
    """)
    List<Experience> findWantExperienceByUserGeoArea(User user, GeoArea area);
}
