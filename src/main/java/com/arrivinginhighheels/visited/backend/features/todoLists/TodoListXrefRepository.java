package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TodoListXrefRepository extends JpaRepository<TodoListXref, Long> {

    List<TodoListXref> findAllByTodoListOrderByOrdinalAsc(TodoList list);

    @Query("""
       select new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoListItem(xref.listItem, xref.todoList, translation, xref.thumbnail, xref.popularity)
       from TodoListXref xref
       INNER join TodoListItemTranslation translation ON xref.listItem = translation.listItem
       where
        xref.todoList = ?1 AND
        translation.supportedLanguage = ?2
       ORDER BY xref.ordinal asc
    """)
    List<LocalizedTodoListItem> findAllByListAndLanguage(TodoList list, SupportedLanguage language);

    List<TodoListXref> findAllByListItem_GeoAreaOrderByPopularityDesc(GeoArea area);

    List<TodoListXref> findAllByListItem_GeoAreaAndTodoList_TypeOrderByPopularityDesc(GeoArea area, TodoListType type);

    @Query("""
       select new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoXref(xref, translation)
       from TodoListXref xref
       INNER join TodoListItemTranslation translation ON xref.listItem = translation.listItem
       where
        xref.listItem.geoArea = ?1
        AND translation.supportedLanguage = ?2
       order by xref.popularity desc
    """)
    List<LocalizedTodoXref> findAllByGeoAreaAndLanguage(GeoArea area, SupportedLanguage language);

    @Query("""
       select new com.arrivinginhighheels.visited.backend.features.todoLists.LocalizedTodoXref(xref, translation)
       from TodoListXref xref
       INNER join TodoListItemTranslation translation ON xref.listItem = translation.listItem
       where
        xref.listItem.geoArea = ?1
        AND xref.todoList.type = ?2
        AND translation.supportedLanguage = ?3
       order by xref.popularity desc
    """)
    List<LocalizedTodoXref> findAllByGeoAreaAndTypeAndLanguage(GeoArea area, TodoListType type, SupportedLanguage language);
}
