package com.arrivinginhighheels.visited.backend.features.inspirations;

import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.INSPIRATION_URL;

@RestController
@RequestMapping(path = INSPIRATION_URL)
public class InspirationController {

    private final InspirationService inspirationService;

    private final LoggedInUserUtil userUtil;

    private final ResponseHelper responseHelper;

    public InspirationController(InspirationService inspirationService, LoggedInUserUtil userUtil,
            ResponseHelper responseHelper) {
        this.inspirationService = inspirationService;
        this.userUtil = userUtil;
        this.responseHelper = responseHelper;
    }

    @RequestMapping(path = "{id}", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<InspirationDTO> getInspiration(
            HttpServletRequest request,
            @PathVariable Long id,
            @RequestParam(required = false) Double resolution) {
        final User user = userUtil.getLoggedInUser(request);
        final double actualResolution = resolution == null ? 1.0 : resolution;
        final Date date = inspirationService.getMaxModificationTimeOfInspiration(id);
        return responseHelper.wrap(request, date, ResponseHelper.standardCacheLength,
                (r) -> inspirationService.getInspirationById(user, id, actualResolution));
    }

    @RequestMapping(path = "feed", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<InspirationDTO>> getInspirations(
            HttpServletRequest request,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Double resolution) {
        final User user = userUtil.getLoggedInUser(request);
        if (resolution == null) {
            resolution = 1.0;
        }

        var inspirations = inspirationService.getNextSetOfInspirations(user, page, resolution);
        return responseHelper.standardCacheableResponse(inspirations);
    }

    @RequestMapping(path = "feed/{isoCode}", method = RequestMethod.GET, produces = "application/json")
    public ResponseEntity<List<InspirationDTO>> getInspirationsForArea(
            HttpServletRequest request,
            @PathVariable String isoCode,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Double resolution) {
        final User user = userUtil.getLoggedInUser(request);
        if (resolution == null) {
            resolution = 1.0;
        }

        var inspirations = inspirationService.getNextSetOfInspirationsForArea(user, isoCode, page, resolution);
        return responseHelper.doNotCacheResponse(inspirations);
    }

    @RequestMapping(path = "select", method = RequestMethod.GET)
    public ResponseEntity<Map<Long, InspirationSelectionType>> getUserSelections(final HttpServletRequest request) {
        final User user = userUtil.getLoggedInUser(request);
        var selections = inspirationService.getAllSelectionsForUser(user);
        return responseHelper.doNotCacheResponse(selections);
    }

    @RequestMapping(path = "select", method = RequestMethod.POST)
    public SelectionsDTO saveSelection(final HttpServletRequest request,
            @Valid @RequestBody final UserInspirationSelectionDTO selectionDTO) {
        final User user = userUtil.getLoggedInUser(request);
        return inspirationService.handleSelection(user, selectionDTO);
    }
}
