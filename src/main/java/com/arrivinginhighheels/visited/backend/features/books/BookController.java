package com.arrivinginhighheels.visited.backend.features.books;

import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.BOOKS_URL;

@RestController()
@RequestMapping(path = BOOKS_URL)
public class BookController {

    private final BookService bookService;

    private final ResponseHelper responseHelper;

    public BookController(
            BookService bookService,
            ResponseHelper responseHelper) {
        this.bookService = bookService;
        this.responseHelper = responseHelper;
    }

    @GetMapping("/areas/{isoCode}")
    public ResponseEntity<Optional<BookDto>> findAllByAreaIso(@PathVariable String isoCode) {
        return responseHelper.standardCacheableResponse(bookService.fetchBooksByAreaIsoCode(isoCode));
    }

    @GetMapping("/lists/{listId}")
    public ResponseEntity<Optional<BookDto>> findAllByAreaIso(@PathVariable Long listId) {
        return responseHelper.standardCacheableResponse(bookService.fetchBooksByTodoListId(listId));
    }

    @GetMapping("/experiences/{experienceId}")
    public ResponseEntity<Optional<BookDto>> findAllByExperienceId(@PathVariable Long experienceId) {
        return responseHelper.standardCacheableResponse(bookService.fetchBooksByExperienceId(experienceId));
    }
}
