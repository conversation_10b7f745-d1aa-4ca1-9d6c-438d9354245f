package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import com.arrivinginhighheels.visited.backend.model.GeoArea;
import com.arrivinginhighheels.visited.backend.model.GeoAreaTranslation;
import lombok.Getter;

import java.util.Objects;

@Getter
public class TodoAreaList {
    private final GeoArea area;
    private GeoAreaTranslation translation;
    private final AdaptiveImage image;
    private final Long itemCount;

    public TodoAreaList(GeoArea area, GeoAreaTranslation translation, AdaptiveImage image, Long itemCount) {
        this.area = area;
        this.translation = translation;
        this.image = image;
        this.itemCount = itemCount;
    }

    public TodoAreaList(GeoArea area, AdaptiveImage image, Long itemCount) {
        this.area = area;
        this.image = image;
        this.itemCount = itemCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TodoAreaList that = (TodoAreaList) o;
        return Objects.equals(area, that.area) && Objects.equals(translation, that.translation) && Objects.equals(image, that.image) && Objects.equals(itemCount, that.itemCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(area, translation, image, itemCount);
    }

    @Override
    public String toString() {
        return "TodoAreaList{" +
                "area=" + area +
                ", translation=" + translation +
                ", image=" + image +
                ", itemCount=" + itemCount +
                '}';
    }
}
