package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.City;
import com.arrivinginhighheels.visited.backend.model.CityTranslation;
import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.service.translations.AbstractTranslationService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class CityTranslationService extends AbstractTranslationService {

    private final CityTranslationRepository cityTranslationRepository;

    public CityTranslationService(CityTranslationRepository cityTranslationRepository) {
        this.cityTranslationRepository = cityTranslationRepository;
    }

    public String getCityName(final City city) {
        final SupportedLanguage language = getCurrentLanguage();
        if (language == null || language.isEnglish()) {
            return city.getName();
        }

        final var translation = cityTranslationRepository.findByCityAndSupportedLanguage(city, language);

        if (translation.isEmpty()) {
            return city.getName();
        }

        return translation.get().getName();
    }

    public Map<City, String> getTranslations(final List<City> cities) {
        final SupportedLanguage language = getCurrentLanguage();
        if (language == null || language.getCode().equals("en")) {
            final Map<City, String> english = new HashMap<>();
            for (final City city : cities) {
                english.put(city, city.getName());
            }

            return english;
        }

        final List<Long> ids = cities.stream().map(City::getId).collect(Collectors.toList());
        final List<CityTranslation> translations = cityTranslationRepository.findBySupportedLanguageAndCityIdIn(language, ids);

        final Map<City, String> prepared = new HashMap<>();
        for (final CityTranslation cityTranslation : translations) {
            prepared.put(cityTranslation.getCity(), cityTranslation.getName());
        }

        return prepared;
    }

}
