package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

public interface TodoListRepository extends JpaRepository<TodoList, Long> {

    List<TodoList> findAllByOrderByOrdinalAsc();

    @Query( "select max(l.lastModificationTime) from TodoList l")
    Date getMaxModificationTime();

    @Query("""
    select new com.arrivinginhighheels.visited.backend.features.todoLists.TodoAreaList(
        items.geoArea,
        details.thumbnail,
        count(items.geoArea)
    )
    from TodoListItem items
    INNER join AreaDetails details on details.geoArea = items.geoArea
    group by items.geoArea, items.geoArea.name, details.thumbnail
    order by items.geoArea.name ASC
    """)
    List<TodoAreaList> getCountryTodoLists();

    @Query("""
    select new com.arrivinginhighheels.visited.backend.features.todoLists.TodoAreaList(
        items.geoArea,
        translation,
        details.thumbnail,
        count(items.geoArea)
    )
    from TodoListItem items
    INNER join AreaDetails details on details.geoArea = items.geoArea
    INNER join GeoAreaTranslation translation on translation.geoArea = items.geoArea
    where translation.supportedLanguage = ?1
    group by items.geoArea, translation, items.geoArea.name, details.thumbnail
    order by translation.name ASC
    """)
    List<TodoAreaList> getLocalizedCountryTodoLists(SupportedLanguage language);

//    SELECT
//  DISTINCT item.geo_area_id as id,
//  geoareas.name,
//  geoarea_details.thumbnail_url_id,
//  NULL as sponsor_id,
//  geoareas.last_modification_time,
//  count(item.geo_area_id) as count,
//  '4.0.5' as min_version
//FROM todo_list_items as item
//INNER JOIN geoareas on geoareas.id = item.geo_area_id
//INNER JOIN geoarea_details on geoareas.id = geoarea_details.geo_area_id
//WHERE
//  item.geo_area_id is not null
//GROUP BY
//  item.geo_area_id,
//  geoareas.name,
//  geoarea_details.thumbnail_url_id,
//  geoareas.last_modification_time;


    @Modifying
    @Transactional
    @Query(nativeQuery = true,
            value = """
                    create sequence if not exists list_popularity_id;
                    ALTER SEQUENCE list_popularity_id RESTART WITH 1;
                    update todo_lists set ordinal = (select count(*) from todo_lists);
                    
                    update todo_lists set
                      last_modification_time = CURRENT_TIMESTAMP,
                      ordinal = popularity.ordinal
                    from (
                      select
                        nextval('list_popularity_id') as ordinal,
                        todo_lists.id as id,
                        count(user_todo_list_items.list_id) as count
                      from todo_lists, user_todo_list_items
                      where todo_lists.id = user_todo_list_items.list_id
                      group by todo_lists.id
                      order by count DESC) as popularity
                    where todo_lists.id = popularity.id;
                    """)
    void sortByPopularity();
}
