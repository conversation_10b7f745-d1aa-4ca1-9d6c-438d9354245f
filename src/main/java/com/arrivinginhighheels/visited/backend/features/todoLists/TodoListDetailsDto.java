package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.features.books.BookDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;
import java.util.Optional;

import static com.fasterxml.jackson.annotation.JsonInclude.*;

@JsonInclude(Include.NON_EMPTY)
public record TodoListDetailsDto(
        TodoListDTO list,
        List<TodoListItemDTO> items,
        Optional<BookDto> bookLink) {
}
