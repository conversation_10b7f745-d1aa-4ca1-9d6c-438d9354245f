package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

import jakarta.persistence.*;
import java.util.Objects;

@Getter
@Entity
@Table(name = "todo_lists_xref")
public class TodoListXref {

    @Setter
    @Id
    @SequenceGenerator(name = "lists_xref_id_seq", sequenceName = "lists_xref_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "lists_xref_id_seq")
    private Long id;

    @Setter
    @OneToOne
    @JoinColumn(name = "todo_list_id")
    private TodoList todoList;

    @Setter
    @OneToOne
    @JoinColumn(name = "todo_list_item_id")
    private TodoListItem listItem;

    @Setter
    private Integer ordinal;

    @Setter
    private Integer popularity;

    @OneToOne
    @JoinColumn(name = "thumbnail_id")
    @Nullable
    private AdaptiveImage thumbnail;

    public TodoListXref(
            Long id,
            TodoList todoList,
            TodoListItem listItem,
            Integer ordinal,
            Integer popularity,
            @Nullable AdaptiveImage thumbnail) {
        this.id = id;
        this.todoList = todoList;
        this.listItem = listItem;
        this.ordinal = ordinal;
        this.popularity = popularity;
        this.thumbnail = thumbnail;
    }

    public TodoListXref() {
    }

    @Nullable
    public AdaptiveImage getThumbnail() {
        return thumbnail;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (!(o instanceof TodoListXref that))
            return false;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getTodoList(), that.getTodoList())
                && Objects.equals(getListItem(), that.getListItem()) && Objects.equals(getOrdinal(), that.getOrdinal())
                && Objects.equals(getPopularity(), that.getPopularity())
                && Objects.equals(getThumbnail(), that.getThumbnail());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getTodoList(), getListItem(), getOrdinal(), getPopularity(), getThumbnail());
    }

    @Override
    public String toString() {
        return "TodoListXref{" +
                "id=" + id +
                ", todoList=" + todoList +
                ", listItem=" + listItem +
                ", ordinal=" + ordinal +
                ", popularity=" + popularity +
                ", thumbnail=" + thumbnail +
                '}';
    }
}
