package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;
import com.arrivinginhighheels.visited.backend.service.translations.AbstractTranslationService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TodoListTranslationService extends AbstractTranslationService {
    private final TodoListTranslationRepository listTranslationRepository;

    private final TodoListItemTranslationRepository itemTranslationRepository;

    public TodoListTranslationService(TodoListTranslationRepository listTranslationRepository, TodoListItemTranslationRepository itemTranslationRepository) {
        this.listTranslationRepository = listTranslationRepository;
        this.itemTranslationRepository = itemTranslationRepository;
    }

    public String getListName(final TodoList list) {
        final SupportedLanguage lang = getCurrentLanguage();
        if (lang == null || lang.isEnglish()) {
            return list.getName();
        }

        final TodoListTranslation translation = listTranslationRepository.findByListAndSupportedLanguage(list, lang);
        if (translation == null) {
            return list.getName();
        }

        return translation.getName();
    }

    public String getListItemName(final TodoListItem item) {
        final SupportedLanguage lang = getCurrentLanguage();
        if (lang == null || lang.isEnglish()) {
            return item.getName();
        }

        final TodoListItemTranslation translation = itemTranslationRepository.findByListItemAndSupportedLanguage(item, lang);
        if (translation == null) {
            return item.getName();
        }

        return translation.getName();
    }

    public List<TodoListItemTranslation> getAll(SupportedLanguage language, List<Long> ids) {
        return itemTranslationRepository.findBySupportedLanguageAndListItemIdIn(language, ids);
    }
}
