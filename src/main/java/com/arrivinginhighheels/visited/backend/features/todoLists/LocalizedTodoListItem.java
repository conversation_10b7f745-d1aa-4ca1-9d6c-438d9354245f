package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.AdaptiveImage;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;


@Getter
@Setter
public final class LocalizedTodoListItem {
    private final TodoListItem item;
    private final TodoListItemTranslation translation;
    private final AdaptiveImage thumbnail;
    private final Integer popularity;
    private final TodoList list;

    public LocalizedTodoListItem(TodoListItem item, TodoList list, TodoListItemTranslation translation, AdaptiveImage thumbnail, Integer popularity) {
        this.item = item;
        this.translation = translation;
        this.thumbnail = thumbnail;
        this.popularity = popularity;
        this.list = list;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LocalizedTodoListItem that)) return false;
        return Objects.equals(getItem(), that.getItem()) && Objects.equals(getTranslation(), that.getTranslation()) && Objects.equals(getThumbnail(), that.getThumbnail()) && Objects.equals(getPopularity(), that.getPopularity());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getItem(), getTranslation(), getThumbnail(), getPopularity());
    }

    @Override
    public String toString() {
        return "LocalizedTodoListItem{" +
                "item=" + item +
                ", translation=" + translation +
                ", thumbnail=" + thumbnail +
                ", popularity=" + popularity +
                '}';
    }
}
