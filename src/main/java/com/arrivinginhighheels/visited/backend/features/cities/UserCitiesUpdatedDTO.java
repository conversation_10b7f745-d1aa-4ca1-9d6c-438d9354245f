package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCitiesUpdatedDTO {
    private UserCitiesDTO cities;
    private SelectionsDTO areas;

    public UserCitiesUpdatedDTO(final UserCitiesDTO cities, final SelectionsDTO areas) {
        this.cities = cities;
        this.areas = areas;
    }

    public UserCitiesDTO getCities() {
        return cities;
    }

    public void setCities(final UserCitiesDTO cities) {
        this.cities = cities;
    }

    public SelectionsDTO getAreas() {
        return areas;
    }

    public void setAreas(final SelectionsDTO areas) {
        this.areas = areas;
    }
}
