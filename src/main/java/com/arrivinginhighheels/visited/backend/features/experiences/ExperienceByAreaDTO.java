package com.arrivinginhighheels.visited.backend.features.experiences;

import java.util.ArrayList;
import java.util.List;

public class ExperienceByAreaDTO {
    private List<ExperienceDTO> been = new ArrayList<>();
    private List<ExperienceDTO> want = new ArrayList<>();

    public ExperienceByAreaDTO() {
    }

    public ExperienceByAreaDTO(List<ExperienceDTO> been, List<ExperienceDTO> want) {
        this.been = been;
        this.want = want;
    }

    public List<ExperienceDTO> getBeen() {
        return been;
    }

    public void setBeen(List<ExperienceDTO> been) {
        this.been = been;
    }

    public List<ExperienceDTO> getWant() {
        return want;
    }

    public void setWant(List<ExperienceDTO> want) {
        this.want = want;
    }
}
