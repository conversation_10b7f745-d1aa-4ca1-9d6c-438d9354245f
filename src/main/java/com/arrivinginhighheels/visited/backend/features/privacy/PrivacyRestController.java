package com.arrivinginhighheels.visited.backend.features.privacy;

import com.arrivinginhighheels.visited.backend.dto.PrivacyAgreementDTO;
import com.arrivinginhighheels.visited.backend.dto.PrivacyDTO;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.CASL_URL;

/**
 * REST Controller for the Rank API method.
 */
@RestController
public class PrivacyRestController {

        private final LoggedInUserUtil loggedInUserUtil;

        private final PrivacyService privacyService;

        public PrivacyRestController(LoggedInUserUtil loggedInUserUtil, PrivacyService privacyService) {
                this.loggedInUserUtil = loggedInUserUtil;
                this.privacyService = privacyService;
        }

        /**
         * CASL GET method, returns the information on CASL for the logged-in user
         *
         * @param request
         * @return
         */
        @RequestMapping(path = CASL_URL, method = RequestMethod.GET)
        public PrivacyDTO getPrivacyStatusForUser(HttpServletRequest request) {
                User user = loggedInUserUtil.getLoggedInUser(request);

                return privacyService.getPrivacyStatusForTheUser(user);
        }

        /**
         * CASL POST method, changes CASL configuration for the logged-in user
         *
         * @param request
         * @return
         */
        @RequestMapping(path = CASL_URL, method = RequestMethod.POST)
        public PrivacyDTO updatePrivacyForUser(
                        HttpServletRequest request,
                        @RequestBody @Valid PrivacyAgreementDTO privacyAgreementDTO) {

                User user = loggedInUserUtil.getLoggedInUser(request);
                return privacyService.setPrivacyStatusForTheUser(user, privacyAgreementDTO);
        }

}
