package com.arrivinginhighheels.visited.backend.features.stats;

import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.STATS_URL;

@RestController
@RequestMapping(path = STATS_URL)
public class StatsController {

    private final ResponseHelper responseHelper;

    private final StatsService statsService;

    private final LoggedInUserUtil userUtil;

    public StatsController(
            ResponseHelper responseHelper,
            StatsService statsService,
            LoggedInUserUtil userUtil) {
        this.responseHelper = responseHelper;
        this.statsService = statsService;
        this.userUtil = userUtil;
    }

    @GetMapping(path = "/travellerType", produces = "application/json")
    ResponseEntity<List<TravelTypeDto>> getTravelTypes(final HttpServletRequest request) {
        final var user = userUtil.getLoggedInUser(request);
        final var types = statsService.getTravellerTypeByUser(user);
        return responseHelper.doNotCacheResponse(types);
    }

    @GetMapping(path = "/hotelPreference", produces = "application/json")
    ResponseEntity<Optional<HotelPreferenceDto>> getHotelPreferences(final HttpServletRequest request) {
        final var user = userUtil.getLoggedInUser(request);
        final var preference = statsService.getHotelPreference(user);
        return responseHelper.doNotCacheResponse(preference);
    }
}
