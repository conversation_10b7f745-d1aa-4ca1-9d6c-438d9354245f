package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SelectionType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor @Getter @Setter
public class TodoListSelectionDTO {
    private Map<SelectionType, List<Long>> selections = new HashMap<>();
}
