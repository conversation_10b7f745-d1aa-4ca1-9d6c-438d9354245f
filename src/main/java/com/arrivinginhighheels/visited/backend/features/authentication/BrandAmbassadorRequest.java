package com.arrivinginhighheels.visited.backend.features.authentication;

import java.util.List;
import java.util.Objects;

public final class BrandAmbassadorRequest {
    private final String email;
    private final String name;
    private final String country;
    private final List<SocialMediaFollowing> socialMedia;
    private final String otherSocialMedia;
    private final String anythingElse;

    public BrandAmbassadorRequest(
            String email,
            String name,
            String country,
            List<SocialMediaFollowing> socialMedia,
            String otherSocialMedia,
            String anythingElse
    ) {
        this.email = email;
        this.name = name;
        this.country = country;
        this.socialMedia = socialMedia;
        this.otherSocialMedia = otherSocialMedia;
        this.anythingElse = anythingElse;
    }

    public String email() {
        return email;
    }

    public String name() {
        return name;
    }

    public String country() {
        return country;
    }

    public List<SocialMediaFollowing> socialMedia() {
        return socialMedia;
    }

    public String otherSocialMedia() {
        return otherSocialMedia;
    }

    public String anythingElse() {
        return anythingElse;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == this) return true;
        if (obj == null || obj.getClass() != this.getClass()) return false;
        var that = (BrandAmbassadorRequest) obj;
        return Objects.equals(this.email, that.email) &&
                Objects.equals(this.name, that.name) &&
                Objects.equals(this.country, that.country) &&
                Objects.equals(this.socialMedia, that.socialMedia) &&
                Objects.equals(this.otherSocialMedia, that.otherSocialMedia) &&
                Objects.equals(this.anythingElse, that.anythingElse);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email, name, country, socialMedia, otherSocialMedia, anythingElse);
    }

    @Override
    public String toString() {
        return "BrandAmbassadorRequest[" +
                "email=" + email + ", " +
                "name=" + name + ", " +
                "country=" + country + ", " +
                "socialMedia=" + socialMedia + ", " +
                "otherSocialMedia=" + otherSocialMedia + ", " +
                "anythingElse=" + anythingElse + ']';
    }
}
