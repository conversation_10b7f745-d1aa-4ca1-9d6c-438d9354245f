package com.arrivinginhighheels.visited.backend.features.experiences;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ExperienceNotFoundException extends RuntimeException {
    public ExperienceNotFoundException(Long experienceId) {
        super("Could not find any experience with the id " + experienceId);
    }
}
