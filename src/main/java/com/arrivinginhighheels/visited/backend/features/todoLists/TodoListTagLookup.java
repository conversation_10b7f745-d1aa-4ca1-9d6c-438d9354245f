package com.arrivinginhighheels.visited.backend.features.todoLists;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import java.util.Objects;

@Entity
@Table(name = "todo_list_tags_xref")
@IdClass(TodoListTagLookupId.class)
public class TodoListTagLookup {
    @Id
    private Long listId;

    @Id
    private Long tagId;

    public TodoListTagLookup() {
    }

    public Long getListId() {
        return listId;
    }

    public void setListId(Long listId) {
        this.listId = listId;
    }

    public Long getTagId() {
        return tagId;
    }

    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoListTagLookup that = (TodoListTagLookup) o;
        return Objects.equals(listId, that.listId) && Objects.equals(tagId, that.tagId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(listId, tagId);
    }

    @Override
    public String toString() {
        return "TodoListTagLookup{" +
                "listId=" + listId +
                ", tagId=" + tagId +
                '}';
    }
}
