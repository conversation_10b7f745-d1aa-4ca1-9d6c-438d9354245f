package com.arrivinginhighheels.visited.backend.features.privacy;

import com.arrivinginhighheels.visited.backend.model.User;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import java.util.Date;

@Entity
@Table(name = "casl_agreements")
public class PrivacyAgreement {

    @Id
    @SequenceGenerator(name = "casl_agreements_id_seq", sequenceName = "casl_agreements_id_seq", allocationSize = 1)
    @GeneratedValue(generator = "casl_agreements_id_seq", strategy = GenerationType.SEQUENCE)
    private Long id;

    @NotNull
    @OneToOne(optional = false)
    @JoinColumn(unique = true)
    private User user;

    @Column(nullable = false)
    private Boolean required = false;

    @Enumerated(EnumType.STRING)
    @Column(length = 5, nullable = false)
    private TriStateValue optin = TriStateValue.NA;

    @Enumerated(EnumType.STRING)
    @Column(length = 5, nullable = false)
    private TriStateValue terms = TriStateValue.NA;

    @Temporal(TemporalType.TIMESTAMP)
    private Date timestamp;

    public PrivacyAgreement() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public TriStateValue getOptin() {
        return optin;
    }

    public void setOptin(TriStateValue optin) {
        this.optin = optin;
    }

    public TriStateValue getTerms() {
        return terms;
    }

    public void setTerms(TriStateValue terms) {
        this.terms = terms;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String toString() {
        return "PrivacyAgreement{" +
                "id=" + id +
                ", user=" + user +
                ", required=" + required +
                ", optin=" + optin +
                ", terms=" + terms +
                ", timestamp=" + timestamp +
                '}';
    }
}
