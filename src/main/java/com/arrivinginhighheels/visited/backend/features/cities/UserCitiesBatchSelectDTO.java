package com.arrivinginhighheels.visited.backend.features.cities;

import com.arrivinginhighheels.visited.backend.model.SelectionType;

import java.util.List;
import java.util.Map;

public class UserCitiesBatchSelectDTO {

    private Map<SelectionType, List<Long>> updates;

    public Map<SelectionType, List<Long>> getUpdates() {
        return updates;
    }

    public void setUpdates(Map<SelectionType, List<Long>> updates) {
        this.updates = updates;
    }


    public SelectionType findSelection(Long cityId) {
        for (final var entrySet : updates.entrySet()) {
            if (entrySet.getValue().contains(cityId)) {
                return entrySet.getKey();
            }
        }

        return SelectionType.CLEAR;
    }
}
