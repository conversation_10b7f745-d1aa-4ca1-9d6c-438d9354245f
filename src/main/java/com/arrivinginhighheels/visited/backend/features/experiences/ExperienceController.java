package com.arrivinginhighheels.visited.backend.features.experiences;

import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.security.utils.LoggedInUserUtil;
import com.arrivinginhighheels.visited.backend.utils.ResponseHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.EXPERIENCES_URL;

@RestController
@RequestMapping(path = EXPERIENCES_URL)
public class ExperienceController {

        private final ExperiencesService experiencesService;

        private final LoggedInUserUtil loggedInUserUtil;

        private final ResponseHelper responseHelper;

        public ExperienceController(ExperiencesService experiencesService, LoggedInUserUtil loggedInUserUtil,
                        ResponseHelper responseHelper) {
                this.experiencesService = experiencesService;
                this.loggedInUserUtil = loggedInUserUtil;
                this.responseHelper = responseHelper;
        }

        @RequestMapping(method = RequestMethod.GET, produces = "application/json")
        public ResponseEntity<List<ExperienceDTO>> getFirstLevelAreasOrganizedByRegion(HttpServletRequest request) {
                Date lastModificationTime = experiencesService.getMaxModificationTimeOfExperiences();
                return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                                (r) -> experiencesService.getAllExperiences());
        }

        @RequestMapping(path = "/{experienceId}/areas", method = RequestMethod.GET, produces = "application/json")
        public ResponseEntity<List<String>> getFirstLevelAreasOrganizedByRegion(@PathVariable Long experienceId,
                        HttpServletRequest request) {
                Date lastModificationTime = experiencesService.getMaxModificationTimeOfAnExperienceById(experienceId);
                return responseHelper.wrap(request, lastModificationTime, ResponseHelper.standardCacheLength,
                                (r) -> experiencesService.getAreasByExperienceId(experienceId));
        }

        @RequestMapping(path = "/select/area/{areaIsoCode}", method = RequestMethod.POST)
        public Boolean selectArea(HttpServletRequest request,
                        @PathVariable String areaIsoCode,
                        @Valid @RequestBody ExperienceAreaSelectionsDto selections) {

                User user = loggedInUserUtil.getLoggedInUser(request);
                return experiencesService.handleAreaSelection(user, areaIsoCode, selections.getSelections());
        }

        @RequestMapping(path = "/select/batch", method = RequestMethod.POST)
        public Boolean selectBatchAreas(HttpServletRequest request,
                        @Valid @RequestBody ExperienceAreaBatchSelectionsDTO selections) {
                User user = loggedInUserUtil.getLoggedInUser(request);
                return experiencesService.handleBatchSelections(user, selections);
        }

        @RequestMapping(path = "/select/area/{areaIsoCode}", method = RequestMethod.GET)
        public ResponseEntity<ExperienceByAreaDTO> getAreaSelections(HttpServletRequest request,
                        @PathVariable String areaIsoCode) {
                User user = loggedInUserUtil.getLoggedInUser(request);
                var selections = experiencesService.getUserExperienceSelectionsByAreaIsoCode(user, areaIsoCode);
                return responseHelper.doNotCacheResponse(selections);
        }

        @RequestMapping(path = "/select/type/{experienceId}", method = RequestMethod.GET)
        public ResponseEntity<ExperienceByTypeDTO> getExperienceSelections(HttpServletRequest request,
                        @PathVariable Long experienceId) {
                User user = loggedInUserUtil.getLoggedInUser(request);
                var selections = experiencesService.getExperienceSelectionsByExperienceId(user, experienceId);
                return responseHelper.doNotCacheResponse(selections);
        }

        @RequestMapping(path = "/select", method = RequestMethod.POST)
        public Boolean selectPreferredExperiences(HttpServletRequest request,
                        @Valid @RequestBody ExperienceAreaSelectionsDto selections) {

                User user = loggedInUserUtil.getLoggedInUser(request);
                return experiencesService.handlePreferredExperienceSelections(user, selections);
        }

        @RequestMapping(path = "/select/preferred", method = RequestMethod.GET)
        public ResponseEntity<List<ExperienceDTO>> getPreferredExperiences(HttpServletRequest request) {
                User user = loggedInUserUtil.getLoggedInUser(request);
                var selections = experiencesService.getPreferredExperiences(user);
                return responseHelper.doNotCacheResponse(selections);
        }

        @RequestMapping(path = "/select", method = RequestMethod.GET)
        public ResponseEntity<List<ExperienceSelectionsDTO>> getExperienceSummary(
                        HttpServletRequest request) {
                User user = loggedInUserUtil.getLoggedInUser(request);
                var selections = experiencesService.getSelections(user);
                return responseHelper.doNotCacheResponse(selections);
        }
}
