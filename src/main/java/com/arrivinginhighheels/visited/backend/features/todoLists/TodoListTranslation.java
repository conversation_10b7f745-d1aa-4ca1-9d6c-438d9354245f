package com.arrivinginhighheels.visited.backend.features.todoLists;

import com.arrivinginhighheels.visited.backend.model.SupportedLanguage;

import jakarta.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "todo_list_translations")
public class TodoListTranslation {

    @Id
    @SequenceGenerator(name = "list_translations_id_seq", sequenceName = "list_translations_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "list_translations_id_seq")
    private Long id;

    @ManyToOne(optional = false)
    private TodoList list;

    @ManyToOne(optional = false)
    private SupportedLanguage supportedLanguage;

    @Column(nullable = false)
    private String name;

    public TodoListTranslation() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public TodoList getList() {
        return list;
    }

    public void setList(TodoList list) {
        this.list = list;
    }

    public SupportedLanguage getSupportedLanguage() {
        return supportedLanguage;
    }

    public void setSupportedLanguage(SupportedLanguage supportedLanguage) {
        this.supportedLanguage = supportedLanguage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        TodoListTranslation that = (TodoListTranslation) o;
        return Objects.equals(id, that.id) && Objects.equals(list, that.list)
                && Objects.equals(supportedLanguage, that.supportedLanguage) && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, list, supportedLanguage, name);
    }

    @Override
    public String toString() {
        return "TodoListTranslation{" +
                "id=" + id +
                ", list=" + list +
                ", supportedLanguage=" + supportedLanguage +
                ", name='" + name + '\'' +
                '}';
    }
}
