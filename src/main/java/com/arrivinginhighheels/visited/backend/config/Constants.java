package com.arrivinginhighheels.visited.backend.config;

public interface Constants {

    interface Routes {
        String VERSION = "/v2";
        String SELECT_URL = VERSION + "/select";
        String CASL_URL = VERSION + "/casl";
        String SELECT_MULTIPLE_URL = VERSION + "/selectMultiple";
        String AUTHENTICATION_URL = VERSION + "/auth";
        String SIGNIN_URL = VERSION + "/signin";
        String SIGNUP_URL = VERSION + "/signup";
        String AREAS_URL = VERSION + "/areas";
        String CITIES_URL = VERSION + "/cities";
        String INSPIRATION_URL = VERSION + "/inspirations";
        String GEOMETRY_URL = VERSION + "/geometry";
        String LANGUAGES_URL = VERSION + "/languages";
        String EXPERIENCES_URL = VERSION + "/experiences";
        String LISTS_URL = VERSION + "/lists";
        String BOOKS_URL = VERSION + "/books";
        String DEALS_URL = VERSION + "/deals";
        String SOJERN_URL = VERSION + "/tracking";
        String RECEIPT_URL = VERSION + "/receiptValidation";
        String AD_URL = VERSION + "/ads";
        String RANK_URL = VERSION + "/rank";
        String ITINERARIES_URL = VERSION + "/itineraries";
        String FULL_RECALC_URL = RANK_URL + "/recalcAreas";
        String EMAIL_URL = VERSION + "/email";
        String STATS_URL = VERSION + "/stats";
        String TRACKED_LINK_URL = "/travel";
        String SNS_URL = VERSION + "/sns";
        String PRINTING_URL = VERSION + "/printing";
        String USERS_URL = VERSION + "/users";
        String UPDATE_PASSWORD_URL = USERS_URL + "/updatePassword";
        String RESET_USER_SELECTIONS_URL = USERS_URL + "/{username}/select/reset";
        String USERS_ME_URL = USERS_URL + "/me";
        String GET_USER_SESSIONS_URL = USERS_URL + "/{username}/sessions";
        String GET_USER_SELECTION_LOGS_URL = USERS_URL + "/{username}/select/logs";
        String RESET_USER_SELECTION_LOGS_URL = USERS_URL + "/{username}/select/logs/reset";
    }


    interface NativeQueries {
        String USER_GET_RANK_SQL =
                """
                        select coalesce(sum(num_users), 0) + 1 as rank\s
                          from ranking\s
                         where num_areas_visited > (\s
                                   select num_areas_visited\s
                                     from users\s
                                    where id = ?1 )\s
                        \s""";

//        String USER_GET_RANK_SQL =
//                "select count(*) + 1 as rank \n " +
//                "  from users \n " +
//                " where id <> ?1 \n " +
//                "  and num_areas_visited > ( \n " +
//                "          select num_areas_visited \n " +
//                "            from users \n " +
//                "           where id = ?1 ) \n ";

        String USER_GET_TOTAL_AREAS_VISITED_SQL =
                "select count(*) \n " +
                        "  from selections s \n " +
                        "       INNER JOIN geoareas as g \n " +
                        "               ON g.id = s.geo_area_id \n " +
                        "              AND g.parent_id is null \n " +
                        " where s.user_id = ?1 \n " +
                        "   AND (   s.selection_type = 'BEEN' \n " +
                        "        OR s.selection_type = 'LIVED') \n ";

    }

    interface Counters {
        String TOTAL_USERS = "TOTAL_USERS";
    }
}
