package com.arrivinginhighheels.visited.backend.config;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties
@Getter
@NoArgsConstructor
public class YamlConfig {

    @Value("${visited.host}")
    private String host;

    @Value("${iap.secret}")
    private String iapAppSecret;

    @Value("${iap.sandbox}")
    private boolean sandbox;

    @Value("${email.host}")
    private String emailHost;

    @Value("${email.port}")
    private int emailPort;

    @Value("${email.address}")
    private String emailAddress;

    @Value("${email.password}")
    private String emailPassword;

    @Value("${stripe.publishKey}")
    private String stripePublishKey;

    @Value("${stripe.secretKey}")
    private String stripeSecretKey;

    @Value("${stripe.webhook}")
    private String stripeWebhook;

    @Value("${stripe.canadianTax}")
    private String stripeCanadianTaxId;

    @Value("${bitly.key}")
    private String bitlyApiKey;

    @Value("${bitly.groupName}")
    private String bitlyGroupName;

    @Value("${bitly.groupGuid}")
    private String bitlyGroupGuid;

    @Value("${bitly.organizationGuid}")
    private String bitlyOrganizationGuid;

    @Value("${appStoreConnect.issuerId}")
    private String appStoreConnectIssuerId;

    @Value("${appStoreConnect.keyId}")
    private String appStoreConnectKeyId;

    @Value("${appStoreConnect.bundleId}")
    private String appStoreConnectBundleId;

    @Value("${appStoreConnect.appleId}")
    private Long appStoreAppleId;

    @Value("${googlePlay.packageName}")
    private String googlePlayPackageName;

    @Value("${googlePlay.applicationName}")
    private String googlePlayApplicationName;

    @Value("${googlePlay.key}")
    private String googlePlayKeyPath;

    @Value("${iap.proSubscriptionMonthly}")
    private String proSubscriptionMonthlyBundleId;

    @Value("${iap.proSubscriptionAnnual}")
    private String proSubscriptionAnnualBundleId;

    @Value("${iap.proLifetime}")
    private String proLifetimeBundleId;

    @Value("${iap.removeAds}")
    private String removeAdsBundleId;

    @Value("${iap.unlockRegions}")
    private String unlockRegionsBundleId;

    @Value("${iap.unlockInspirations}")
    private String unlockInspirationsBundleId;

    @Value("${iap.unlockCities}")
    private String unlockCitiesBundleId;

    @Value("${iap.unlockItineraries}")
    private String unlockItinerariesBundleId;

    @Value("${awsAIHHAccount1.key}")
    private String aws1Key;

    @Value("${awsAIHHAccount1.secret}")
    private String aws1Secret;

    @Value("${awsAIHHAccount1.region}")
    private String aws1Region;

    @Value("${awsAIHHAccount2.key}")
    private String aws2Key;

    @Value("${awsAIHHAccount2.secret}")
    private String aws2Secret;

    @Value("${awsAIHHAccount2.region}")
    private String aws2Region;
}
