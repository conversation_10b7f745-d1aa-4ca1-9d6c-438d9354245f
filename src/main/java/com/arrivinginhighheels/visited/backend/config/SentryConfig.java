package com.arrivinginhighheels.visited.backend.config;

import io.sentry.SentryEvent;
import io.sentry.SentryOptions.BeforeSendCallback;
import io.sentry.protocol.SentryException;
import io.sentry.Hint;
import org.springframework.stereotype.Component;

@Component
class CustomBeforeSendCallback implements BeforeSendCallback {
    @Override
    public SentryEvent execute(SentryEvent event, Hint hint) {
        if (event.getExceptions() != null) {
            for (SentryException ex : event.getExceptions()) {
                String type = ex.getType();
                String msg = ex.getValue();
                if ("AsyncRequestNotUsableException".equals(type) ||
                        (msg != null && msg.contains("Broken pipe"))) {
                    return null; // Drop the event
                }


                if ("ResourceNotFoundException".equals(type) ||
                        (msg != null && msg.contains("User notes for the area"))) {
                    return null; // Drop the event
                }
            }
        }
        return event;
    }
}
