CREATE SEQUENCE IF NOT EXISTS ad_category_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS adaptive_image_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS authorities_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS book_links_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS cached_responses_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS casl_agreements_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS cities_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS city_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS counters_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS deals_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS disputed_territories_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS experience_geo_areas_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS experience_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS experiences_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_availability_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_labels_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_must_see_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_must_see_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoarea_user_notes_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoareas_enclosed_whitelist_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geoareas_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS geometry_updates_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS image_updates_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS inspiration_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS inspirations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS list_item_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS list_items_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS list_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS lists_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS lists_xref_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS printing_order_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS region_translations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS regions_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS remote_switches_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS selection_logs_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS selections_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS sessions_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS sponsor_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS supported_languages_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS todo_list_book_links_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS top_places_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS tracked_links_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_been_counters_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_deletion_queue_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_experiences_area_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_inspirations_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_itinerary_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_list_item_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_places_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_preferred_experiences_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS user_purchases_id_seq START WITH 1 INCREMENT BY 1;

CREATE SEQUENCE IF NOT EXISTS users_id_seq START WITH 1 INCREMENT BY 1;

CREATE TABLE ad_categories
(
    id                     BIGINT       NOT NULL,
    provider               VARCHAR(250) NOT NULL,
    inventory_name         VARCHAR(250) NOT NULL,
    inventory_hash         VARCHAR(250) NOT NULL,
    geo_area_id            BIGINT,
    experience_id          BIGINT,
    os                     VARCHAR(32)  NOT NULL,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_ad_categories PRIMARY KEY (id)
);

CREATE TABLE adaptive_images
(
    id                   BIGINT       NOT NULL,
    one_x_url            VARCHAR(150) NOT NULL,
    one_point_five_x_url VARCHAR(150) NOT NULL,
    two_x_url            VARCHAR(150) NOT NULL,
    three_x_url          VARCHAR(150) NOT NULL,
    four_x_url           VARCHAR(150) NOT NULL,
    blur_hash            VARCHAR(30)  NOT NULL,
    CONSTRAINT pk_adaptive_images PRIMARY KEY (id)
);

CREATE TABLE area_book_links
(
    id      BIGINT NOT NULL,
    book_id BIGINT,
    area_id BIGINT,
    CONSTRAINT pk_area_book_links PRIMARY KEY (id)
);

CREATE TABLE authorities
(
    id   BIGINT      NOT NULL,
    name VARCHAR(50) NOT NULL,
    CONSTRAINT pk_authorities PRIMARY KEY (id)
);

CREATE TABLE book_links
(
    id            BIGINT NOT NULL,
    name          VARCHAR(255),
    url           VARCHAR(255),
    image_url     VARCHAR(255),
    last_modified TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_book_links PRIMARY KEY (id)
);

CREATE TABLE cached_response
(
    id                     BIGINT       NOT NULL,
    path                   VARCHAR(255) NOT NULL,
    json                   BYTEA        NOT NULL,
    last_modification_time VARCHAR(255) NOT NULL,
    supported_language_id  BIGINT       NOT NULL,
    CONSTRAINT pk_cached_response PRIMARY KEY (id)
);

CREATE TABLE casl_agreements
(
    id        BIGINT     NOT NULL,
    user_id   BIGINT     NOT NULL,
    required  BOOLEAN    NOT NULL,
    optin     VARCHAR(5) NOT NULL,
    terms     VARCHAR(5) NOT NULL,
    timestamp TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_casl_agreements PRIMARY KEY (id)
);

CREATE TABLE cities
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(100) NOT NULL,
    lat                    DECIMAL(19, 10),
    long                   DECIMAL(19, 10),
    level_one_geo_area_id  BIGINT,
    level_two_geo_area_id  BIGINT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_cities PRIMARY KEY (id)
);

CREATE TABLE city_translations
(
    id                    BIGINT       NOT NULL,
    city_id               BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_city_translations PRIMARY KEY (id)
);

CREATE TABLE counters
(
    id    BIGINT       NOT NULL,
    name  VARCHAR(250) NOT NULL,
    value BIGINT       NOT NULL,
    CONSTRAINT pk_counters PRIMARY KEY (id)
);

CREATE TABLE deals
(
    id                     BIGINT                      NOT NULL,
    url                    VARCHAR(255)                NOT NULL,
    geo_area_id            BIGINT,
    experience_id          BIGINT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_deals PRIMARY KEY (id)
);

CREATE TABLE disputed_territories
(
    id                     BIGINT NOT NULL,
    geo_area_id            BIGINT,
    parent_area_id         BIGINT,
    defaults               BOOLEAN,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_disputed_territories PRIMARY KEY (id)
);

CREATE TABLE experience_book_links
(
    id            BIGINT NOT NULL,
    book_id       BIGINT,
    experience_id BIGINT,
    CONSTRAINT pk_experience_book_links PRIMARY KEY (id)
);

CREATE TABLE experience_geo_areas
(
    id                     BIGINT NOT NULL,
    geo_area_id            BIGINT NOT NULL,
    experience_id          BIGINT NOT NULL,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_experience_geo_areas PRIMARY KEY (id)
);

CREATE TABLE experience_translations
(
    id                    BIGINT       NOT NULL,
    experience_id         BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_experience_translations PRIMARY KEY (id)
);

CREATE TABLE experiences
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(250) NOT NULL,
    file                   VARCHAR(40)  NOT NULL,
    icon_url               VARCHAR(250),
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_experiences PRIMARY KEY (id)
);

CREATE TABLE geoarea_availability
(
    id                  BIGINT  NOT NULL,
    geo_area_id         BIGINT  NOT NULL,
    min_ios_version     VARCHAR(15),
    min_android_version VARCHAR(15),
    requires_purchase   BOOLEAN NOT NULL,
    CONSTRAINT pk_geoarea_availability PRIMARY KEY (id)
);

CREATE TABLE geoarea_currencies
(
    geo_area_id BIGINT NOT NULL,
    currency    VARCHAR(255),
    CONSTRAINT pk_geoarea_currencies PRIMARY KEY (geo_area_id)
);

CREATE TABLE geoarea_details
(
    geo_area_id            BIGINT NOT NULL,
    size                   DECIMAL,
    population             BIGINT,
    thumbnail_url_id       BIGINT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_geoarea_details PRIMARY KEY (geo_area_id)
);

CREATE TABLE geoarea_labels
(
    id          BIGINT          NOT NULL,
    geo_area_id BIGINT          NOT NULL,
    latitude    DECIMAL(19, 14) NOT NULL,
    longitude   DECIMAL(19, 14) NOT NULL,
    resolution  INTEGER         NOT NULL,
    CONSTRAINT pk_geoarea_labels PRIMARY KEY (id)
);

CREATE TABLE geoarea_must_see
(
    id          BIGINT       NOT NULL,
    geo_area_id BIGINT       NOT NULL,
    description VARCHAR(250) NOT NULL,
    CONSTRAINT pk_geoarea_must_see PRIMARY KEY (id)
);

CREATE TABLE geoarea_must_see_translations
(
    id                    BIGINT       NOT NULL,
    geo_area_must_see_id  BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    description           VARCHAR(255) NOT NULL,
    CONSTRAINT pk_geoarea_must_see_translations PRIMARY KEY (id)
);

CREATE TABLE geoarea_translations
(
    id                    BIGINT       NOT NULL,
    geo_area_id           BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_geoarea_translations PRIMARY KEY (id)
);

CREATE TABLE geoarea_user_notes
(
    id          BIGINT NOT NULL,
    geo_area_id BIGINT NOT NULL,
    user_id     BIGINT NOT NULL,
    notes       TEXT   NOT NULL,
    CONSTRAINT pk_geoarea_user_notes PRIMARY KEY (id)
);

CREATE TABLE geoareas
(
    id                     BIGINT                NOT NULL,
    region_id              BIGINT,
    iso_key                VARCHAR(15)           NOT NULL,
    name                   VARCHAR(250)          NOT NULL,
    flag_url               VARCHAR(250),
    flag_file_name         VARCHAR(24),
    geometry_etag          VARCHAR(40),
    geo_area_type          VARCHAR(25)           NOT NULL,
    parent_id              BIGINT,
    bounding_min_long      DECIMAL(19, 10),
    bounding_max_long      DECIMAL(19, 10),
    bounding_min_lat       DECIMAL(19, 10),
    bounding_max_lat       DECIMAL(19, 10),
    view_bounds_north_lat  DECIMAL(19, 10),
    view_bounds_west_long  DECIMAL(19, 10),
    view_bounds_south_lat  DECIMAL(19, 10),
    view_bounds_east_long  DECIMAL(19, 10),
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    is_sovereign           BOOLEAN DEFAULT FALSE NOT NULL,
    sovereign_parent_id    BIGINT,
    CONSTRAINT pk_geoareas PRIMARY KEY (id)
);

CREATE TABLE geoareas_enclosed_whitelist
(
    id                      BIGINT NOT NULL,
    geo_area_id             BIGINT NOT NULL,
    surrounding_geo_area_id BIGINT NOT NULL,
    last_modification_time  TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_geoareas_enclosed_whitelist PRIMARY KEY (id)
);

CREATE TABLE geometry_updates
(
    id                     BIGINT NOT NULL,
    file                   VARCHAR(255),
    url                    VARCHAR(255),
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_geometry_updates PRIMARY KEY (id)
);

CREATE TABLE image_updates
(
    id                     BIGINT NOT NULL,
    name                   VARCHAR(255),
    url                    VARCHAR(255),
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_image_updates PRIMARY KEY (id)
);

CREATE TABLE inspiration_translations
(
    id                    BIGINT       NOT NULL,
    inspiration_id        BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_inspiration_translations PRIMARY KEY (id)
);

CREATE TABLE inspirations
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(100) NOT NULL,
    geo_area_id            BIGINT,
    adaptive_image_id      BIGINT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_inspirations PRIMARY KEY (id)
);

CREATE TABLE popularity
(
    geo_area_id      BIGINT NOT NULL,
    geo_area_iso_key VARCHAR(255),
    geo_area_name    VARCHAR(255),
    total            BIGINT,
    rank             INTEGER,
    CONSTRAINT pk_popularity PRIMARY KEY (geo_area_id)
);

CREATE TABLE printed_map_orders
(
    id                   BIGINT NOT NULL,
    user_id              BIGINT NOT NULL,
    map_type             VARCHAR(255),
    selections           CHARACTER VARYING[](16),
    map_data             JSON,
    disputed_territories JSON,
    shipping_address     JSON,
    palette              JSON,
    language_id          BIGINT NOT NULL,
    payment_intent_id    VARCHAR(255),
    charge_id            VARCHAR(255),
    order_received       TIMESTAMP WITHOUT TIME ZONE,
    submitted_to_printer TIMESTAMP WITHOUT TIME ZONE,
    created_at           TIMESTAMP WITHOUT TIME ZONE,
    last_modified        TIMESTAMP WITHOUT TIME ZONE,
    status               VARCHAR(255),
    CONSTRAINT pk_printed_map_orders PRIMARY KEY (id)
);

CREATE TABLE ranking
(
    position          BIGINT  NOT NULL,
    num_areas_visited INTEGER NOT NULL,
    num_users         INTEGER NOT NULL,
    CONSTRAINT pk_ranking PRIMARY KEY (position)
);

CREATE TABLE region_translations
(
    id                    BIGINT       NOT NULL,
    region_id             BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_region_translations PRIMARY KEY (id)
);

CREATE TABLE regions
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(100) NOT NULL,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_regions PRIMARY KEY (id)
);

CREATE TABLE remote_switches
(
    id    BIGINT NOT NULL,
    name  VARCHAR(255),
    value BOOLEAN,
    CONSTRAINT pk_remote_switches PRIMARY KEY (id)
);

CREATE TABLE selection_logs
(
    id             BIGINT                      NOT NULL,
    geo_area_id    BIGINT                      NOT NULL,
    selection_type VARCHAR(10)                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    date           VARCHAR(10)                 NOT NULL,
    user_id        BIGINT                      NOT NULL,
    CONSTRAINT pk_selection_logs PRIMARY KEY (id)
);

CREATE TABLE selections
(
    id             BIGINT                      NOT NULL,
    user_id        BIGINT                      NOT NULL,
    geo_area_id    BIGINT                      NOT NULL,
    selection_type VARCHAR(10)                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    date           VARCHAR(10)                 NOT NULL,
    CONSTRAINT pk_selections PRIMARY KEY (id)
);

CREATE TABLE sessions
(
    id              BIGINT                      NOT NULL,
    user_id         BIGINT                      NOT NULL,
    token           VARCHAR(550)                NOT NULL,
    start_date_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    date            VARCHAR(10)                 NOT NULL,
    os              VARCHAR(50)                 NOT NULL,
    version         VARCHAR(50)                 NOT NULL,
    CONSTRAINT pk_sessions PRIMARY KEY (id)
);

CREATE TABLE sponsors
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(100) NOT NULL,
    url                    VARCHAR(255) NOT NULL,
    url_ios                VARCHAR(255) NOT NULL,
    url_android            VARCHAR(255) NOT NULL,
    promotional_text       TEXT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_sponsors PRIMARY KEY (id)
);

CREATE TABLE supported_languages
(
    id   BIGINT       NOT NULL,
    code VARCHAR(10)  NOT NULL,
    name VARCHAR(150) NOT NULL,
    CONSTRAINT pk_supported_languages PRIMARY KEY (id)
);

CREATE TABLE todo_list_book_links
(
    id           BIGINT NOT NULL,
    book_id      BIGINT,
    todo_list_id BIGINT,
    CONSTRAINT pk_todo_list_book_links PRIMARY KEY (id)
);

CREATE TABLE todo_list_item_translations
(
    id                    BIGINT       NOT NULL,
    list_item_id          BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_todo_list_item_translations PRIMARY KEY (id)
);

CREATE TABLE todo_list_items
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(128) NOT NULL,
    geo_area_id            BIGINT,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    long                   DECIMAL(19, 10),
    lat                    DECIMAL(19, 10),
    CONSTRAINT pk_todo_list_items PRIMARY KEY (id)
);

CREATE TABLE todo_list_tags
(
    id   BIGINT NOT NULL,
    name VARCHAR(255),
    CONSTRAINT pk_todo_list_tags PRIMARY KEY (id)
);

CREATE TABLE todo_list_tags_translations
(
    id                    BIGINT       NOT NULL,
    tag_id                BIGINT,
    supported_language_id BIGINT,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_todo_list_tags_translations PRIMARY KEY (id)
);

CREATE TABLE todo_list_tags_xref
(
    list_id BIGINT NOT NULL,
    tag_id  BIGINT NOT NULL,
    CONSTRAINT pk_todo_list_tags_xref PRIMARY KEY (list_id, tag_id)
);

CREATE TABLE todo_list_translations
(
    id                    BIGINT       NOT NULL,
    list_id               BIGINT       NOT NULL,
    supported_language_id BIGINT       NOT NULL,
    name                  VARCHAR(255) NOT NULL,
    CONSTRAINT pk_todo_list_translations PRIMARY KEY (id)
);

CREATE TABLE todo_lists
(
    id                     BIGINT       NOT NULL,
    name                   VARCHAR(128) NOT NULL,
    adaptive_image_id      BIGINT,
    sponsor_id             BIGINT,
    count                  INTEGER      NOT NULL,
    ordinal                INTEGER,
    min_version            VARCHAR(15),
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_todo_lists PRIMARY KEY (id)
);

CREATE TABLE todo_lists_xref
(
    id                BIGINT NOT NULL,
    todo_list_id      BIGINT,
    todo_list_item_id BIGINT,
    ordinal           INTEGER,
    popularity        INTEGER,
    thumbnail_id      BIGINT,
    CONSTRAINT pk_todo_lists_xref PRIMARY KEY (id)
);

CREATE TABLE top_places
(
    id                   BIGINT NOT NULL,
    lived_geo_area_id    BIGINT,
    been_geo_area_id     BIGINT,
    number_of_users_been INTEGER,
    CONSTRAINT pk_top_places PRIMARY KEY (id)
);

CREATE TABLE tracked_links
(
    id          BIGINT NOT NULL,
    url         VARCHAR(255),
    title       VARCHAR(255),
    tracked_url VARCHAR(255),
    CONSTRAINT pk_tracked_links PRIMARY KEY (id)
);

CREATE TABLE user_been_counters
(
    id          BIGINT                      NOT NULL,
    user_id     BIGINT                      NOT NULL,
    geo_area_id BIGINT                      NOT NULL,
    count       INTEGER,
    timestamp   TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_been_counters PRIMARY KEY (id)
);

CREATE TABLE user_cities
(
    id             BIGINT                      NOT NULL,
    user_id        BIGINT                      NOT NULL,
    city_id        BIGINT                      NOT NULL,
    selection_type VARCHAR(10)                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_cities PRIMARY KEY (id)
);

CREATE TABLE user_deletion_queue
(
    id      BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    CONSTRAINT pk_user_deletion_queue PRIMARY KEY (id)
);

CREATE TABLE user_experience_areas
(
    id             BIGINT                      NOT NULL,
    user_id        BIGINT                      NOT NULL,
    experience_id  BIGINT                      NOT NULL,
    geo_area_id    BIGINT                      NOT NULL,
    selection_type VARCHAR(10)                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_experience_areas PRIMARY KEY (id)
);

CREATE TABLE user_inspirations
(
    id                     BIGINT      NOT NULL,
    user_id                BIGINT      NOT NULL,
    inspiration_id         BIGINT      NOT NULL,
    selection_type         VARCHAR(10) NOT NULL,
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_user_inspirations PRIMARY KEY (id)
);

CREATE TABLE user_itinerary
(
    id                     BIGINT NOT NULL,
    user_id                BIGINT NOT NULL,
    geo_area_id            BIGINT,
    start_date             TIMESTAMP WITHOUT TIME ZONE,
    end_date               TIMESTAMP WITHOUT TIME ZONE,
    hotels                 TEXT[],
    notes                  TEXT[],
    last_modification_time TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_user_itinerary PRIMARY KEY (id)
);

CREATE TABLE user_preferred_experiences
(
    id            BIGINT                      NOT NULL,
    user_id       BIGINT                      NOT NULL,
    experience_id BIGINT                      NOT NULL,
    timestamp     TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_preferred_experiences PRIMARY KEY (id)
);

CREATE TABLE user_purchases
(
    id         BIGINT                      NOT NULL,
    user_id    BIGINT                      NOT NULL,
    product_id VARCHAR(255)                NOT NULL,
    timestamp  TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_purchases PRIMARY KEY (id)
);

CREATE TABLE user_scores
(
    user_id BIGINT NOT NULL,
    score   DOUBLE PRECISION,
    sum     BIGINT,
    CONSTRAINT pk_user_scores PRIMARY KEY (user_id)
);

CREATE TABLE user_todo_list_items
(
    id             BIGINT                      NOT NULL,
    user_id        BIGINT                      NOT NULL,
    list_item_id   BIGINT                      NOT NULL,
    list_id        BIGINT                      NOT NULL,
    selection_type VARCHAR(10)                 NOT NULL,
    timestamp      TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    CONSTRAINT pk_user_todo_list_items PRIMARY KEY (id)
);

CREATE TABLE users
(
    id                       BIGINT                      NOT NULL,
    username                 VARCHAR(500)                NOT NULL,
    password                 VARCHAR(100)                NOT NULL,
    enabled                  BOOLEAN                     NOT NULL,
    num_areas_visited        INTEGER,
    ranking                  BIGINT,
    last_password_reset_date TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    unsubscribed             BOOLEAN,
    creation_date            TIMESTAMP WITHOUT TIME ZONE,
    CONSTRAINT pk_users PRIMARY KEY (id)
);

CREATE TABLE users_authorities
(
    authority_id BIGINT NOT NULL,
    user_id      BIGINT NOT NULL
);

ALTER TABLE city_translations
    ADD CONSTRAINT AK_CityTranslations UNIQUE (city_id);

ALTER TABLE experience_geo_areas
    ADD CONSTRAINT AK_ExperienceGeoArea UNIQUE (experience_id);

ALTER TABLE experience_translations
    ADD CONSTRAINT AK_ExperienceTranslations UNIQUE (experience_id);

ALTER TABLE geoarea_must_see_translations
    ADD CONSTRAINT AK_GeoAreaMustSeeTranslations UNIQUE ();

ALTER TABLE geoarea_translations
    ADD CONSTRAINT AK_GeoAreaTranslations UNIQUE ();

ALTER TABLE geoarea_user_notes
    ADD CONSTRAINT AK_GeoAreaUserNotes UNIQUE (user_id);

ALTER TABLE region_translations
    ADD CONSTRAINT AK_RegionTranslations UNIQUE (region_id);

ALTER TABLE user_preferred_experiences
    ADD CONSTRAINT AK_UserExperience UNIQUE (user_id, experience_id);

ALTER TABLE casl_agreements
    ADD CONSTRAINT uc_casl_agreements_user UNIQUE (user_id);

ALTER TABLE geoareas
    ADD CONSTRAINT uc_geoareas_iso_key UNIQUE (iso_key);

ALTER TABLE printed_map_orders
    ADD CONSTRAINT uc_printed_map_orders_user UNIQUE (user_id);

ALTER TABLE ranking
    ADD CONSTRAINT uc_ranking_num_areas_visited UNIQUE (num_areas_visited);

ALTER TABLE supported_languages
    ADD CONSTRAINT uc_supported_languages_code UNIQUE (code);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT uc_todo_lists_xref_thumbnail UNIQUE (thumbnail_id);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT uc_todo_lists_xref_todo_list UNIQUE (todo_list_id);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT uc_todo_lists_xref_todo_list_item UNIQUE (todo_list_item_id);

ALTER TABLE user_inspirations
    ADD CONSTRAINT uc_user_inspirations_user UNIQUE (user_id);

ALTER TABLE users
    ADD CONSTRAINT uc_users_username UNIQUE (username);

ALTER TABLE ad_categories
    ADD CONSTRAINT FK_AD_CATEGORIES_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE ad_categories
    ADD CONSTRAINT FK_AD_CATEGORIES_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE area_book_links
    ADD CONSTRAINT FK_AREA_BOOK_LINKS_ON_AREA FOREIGN KEY (area_id) REFERENCES geoareas (id);

ALTER TABLE area_book_links
    ADD CONSTRAINT FK_AREA_BOOK_LINKS_ON_BOOK FOREIGN KEY (book_id) REFERENCES book_links (id);

ALTER TABLE cached_response
    ADD CONSTRAINT FK_CACHED_RESPONSE_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE casl_agreements
    ADD CONSTRAINT FK_CASL_AGREEMENTS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE cities
    ADD CONSTRAINT FK_CITIES_ON_LEVEL_ONE_GEO_AREA FOREIGN KEY (level_one_geo_area_id) REFERENCES geoareas (id);

ALTER TABLE cities
    ADD CONSTRAINT FK_CITIES_ON_LEVEL_TWO_GEO_AREA FOREIGN KEY (level_two_geo_area_id) REFERENCES geoareas (id);

ALTER TABLE city_translations
    ADD CONSTRAINT FK_CITY_TRANSLATIONS_ON_CITY FOREIGN KEY (city_id) REFERENCES cities (id);

ALTER TABLE city_translations
    ADD CONSTRAINT FK_CITY_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE deals
    ADD CONSTRAINT FK_DEALS_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE deals
    ADD CONSTRAINT FK_DEALS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE disputed_territories
    ADD CONSTRAINT FK_DISPUTED_TERRITORIES_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE disputed_territories
    ADD CONSTRAINT FK_DISPUTED_TERRITORIES_ON_PARENT_AREA FOREIGN KEY (parent_area_id) REFERENCES geoareas (id);

ALTER TABLE experience_book_links
    ADD CONSTRAINT FK_EXPERIENCE_BOOK_LINKS_ON_BOOK FOREIGN KEY (book_id) REFERENCES book_links (id);

ALTER TABLE experience_book_links
    ADD CONSTRAINT FK_EXPERIENCE_BOOK_LINKS_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE experience_geo_areas
    ADD CONSTRAINT FK_EXPERIENCE_GEO_AREAS_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE experience_geo_areas
    ADD CONSTRAINT FK_EXPERIENCE_GEO_AREAS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE experience_translations
    ADD CONSTRAINT FK_EXPERIENCE_TRANSLATIONS_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE experience_translations
    ADD CONSTRAINT FK_EXPERIENCE_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE geoareas_enclosed_whitelist
    ADD CONSTRAINT FK_GEOAREAS_ENCLOSED_WHITELIST_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoareas_enclosed_whitelist
    ADD CONSTRAINT FK_GEOAREAS_ENCLOSED_WHITELIST_ON_SURROUNDING_GEO_AREA FOREIGN KEY (surrounding_geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoareas
    ADD CONSTRAINT FK_GEOAREAS_ON_PARENT FOREIGN KEY (parent_id) REFERENCES geoareas (id);

ALTER TABLE geoareas
    ADD CONSTRAINT FK_GEOAREAS_ON_REGION FOREIGN KEY (region_id) REFERENCES regions (id);

ALTER TABLE geoarea_availability
    ADD CONSTRAINT FK_GEOAREA_AVAILABILITY_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_currencies
    ADD CONSTRAINT FK_GEOAREA_CURRENCIES_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_details
    ADD CONSTRAINT FK_GEOAREA_DETAILS_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_details
    ADD CONSTRAINT FK_GEOAREA_DETAILS_ON_THUMBNAIL_URL FOREIGN KEY (thumbnail_url_id) REFERENCES adaptive_images (id);

ALTER TABLE geoarea_labels
    ADD CONSTRAINT FK_GEOAREA_LABELS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_must_see
    ADD CONSTRAINT FK_GEOAREA_MUST_SEE_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoarea_details (geo_area_id);

ALTER TABLE geoarea_must_see_translations
    ADD CONSTRAINT FK_GEOAREA_MUST_SEE_TRANSLATIONS_ON_GEOAREAMUSTSEE FOREIGN KEY (geo_area_must_see_id) REFERENCES geoarea_must_see (id);

ALTER TABLE geoarea_must_see_translations
    ADD CONSTRAINT FK_GEOAREA_MUST_SEE_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE geoarea_translations
    ADD CONSTRAINT FK_GEOAREA_TRANSLATIONS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_translations
    ADD CONSTRAINT FK_GEOAREA_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE geoarea_user_notes
    ADD CONSTRAINT FK_GEOAREA_USER_NOTES_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE geoarea_user_notes
    ADD CONSTRAINT FK_GEOAREA_USER_NOTES_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE inspirations
    ADD CONSTRAINT FK_INSPIRATIONS_ON_ADAPTIVE_IMAGE FOREIGN KEY (adaptive_image_id) REFERENCES adaptive_images (id);

ALTER TABLE inspirations
    ADD CONSTRAINT FK_INSPIRATIONS_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE inspiration_translations
    ADD CONSTRAINT FK_INSPIRATION_TRANSLATIONS_ON_INSPIRATION FOREIGN KEY (inspiration_id) REFERENCES inspirations (id);

ALTER TABLE inspiration_translations
    ADD CONSTRAINT FK_INSPIRATION_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE popularity
    ADD CONSTRAINT FK_POPULARITY_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE printed_map_orders
    ADD CONSTRAINT FK_PRINTED_MAP_ORDERS_ON_LANGUAGE FOREIGN KEY (language_id) REFERENCES supported_languages (id);

ALTER TABLE printed_map_orders
    ADD CONSTRAINT FK_PRINTED_MAP_ORDERS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE region_translations
    ADD CONSTRAINT FK_REGION_TRANSLATIONS_ON_REGION FOREIGN KEY (region_id) REFERENCES regions (id);

ALTER TABLE region_translations
    ADD CONSTRAINT FK_REGION_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE selections
    ADD CONSTRAINT FK_SELECTIONS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE selections
    ADD CONSTRAINT FK_SELECTIONS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE selection_logs
    ADD CONSTRAINT FK_SELECTION_LOGS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE sessions
    ADD CONSTRAINT FK_SESSIONS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE todo_lists
    ADD CONSTRAINT FK_TODO_LISTS_ON_ADAPTIVE_IMAGE FOREIGN KEY (adaptive_image_id) REFERENCES adaptive_images (id);

ALTER TABLE todo_lists
    ADD CONSTRAINT FK_TODO_LISTS_ON_SPONSOR FOREIGN KEY (sponsor_id) REFERENCES sponsors (id);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT FK_TODO_LISTS_XREF_ON_THUMBNAIL FOREIGN KEY (thumbnail_id) REFERENCES adaptive_images (id);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT FK_TODO_LISTS_XREF_ON_TODO_LIST FOREIGN KEY (todo_list_id) REFERENCES todo_lists (id);

ALTER TABLE todo_lists_xref
    ADD CONSTRAINT FK_TODO_LISTS_XREF_ON_TODO_LIST_ITEM FOREIGN KEY (todo_list_item_id) REFERENCES todo_list_items (id);

ALTER TABLE todo_list_book_links
    ADD CONSTRAINT FK_TODO_LIST_BOOK_LINKS_ON_BOOK FOREIGN KEY (book_id) REFERENCES book_links (id);

ALTER TABLE todo_list_book_links
    ADD CONSTRAINT FK_TODO_LIST_BOOK_LINKS_ON_TODO_LIST FOREIGN KEY (todo_list_id) REFERENCES todo_lists (id);

ALTER TABLE todo_list_items
    ADD CONSTRAINT FK_TODO_LIST_ITEMS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE todo_list_item_translations
    ADD CONSTRAINT FK_TODO_LIST_ITEM_TRANSLATIONS_ON_LISTITEM FOREIGN KEY (list_item_id) REFERENCES todo_list_items (id);

ALTER TABLE todo_list_item_translations
    ADD CONSTRAINT FK_TODO_LIST_ITEM_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE todo_list_tags_translations
    ADD CONSTRAINT FK_TODO_LIST_TAGS_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE todo_list_tags_translations
    ADD CONSTRAINT FK_TODO_LIST_TAGS_TRANSLATIONS_ON_TAG FOREIGN KEY (tag_id) REFERENCES todo_list_tags (id);

ALTER TABLE todo_list_translations
    ADD CONSTRAINT FK_TODO_LIST_TRANSLATIONS_ON_LIST FOREIGN KEY (list_id) REFERENCES todo_lists (id);

ALTER TABLE todo_list_translations
    ADD CONSTRAINT FK_TODO_LIST_TRANSLATIONS_ON_SUPPORTEDLANGUAGE FOREIGN KEY (supported_language_id) REFERENCES supported_languages (id);

ALTER TABLE top_places
    ADD CONSTRAINT FK_TOP_PLACES_ON_BEEN_GEO_AREA FOREIGN KEY (been_geo_area_id) REFERENCES geoareas (id);

ALTER TABLE top_places
    ADD CONSTRAINT FK_TOP_PLACES_ON_LIVED_GEO_AREA FOREIGN KEY (lived_geo_area_id) REFERENCES geoareas (id);

ALTER TABLE user_been_counters
    ADD CONSTRAINT FK_USER_BEEN_COUNTERS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE user_been_counters
    ADD CONSTRAINT FK_USER_BEEN_COUNTERS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_cities
    ADD CONSTRAINT FK_USER_CITIES_ON_CITY FOREIGN KEY (city_id) REFERENCES cities (id);

ALTER TABLE user_cities
    ADD CONSTRAINT FK_USER_CITIES_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_experience_areas
    ADD CONSTRAINT FK_USER_EXPERIENCE_AREAS_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE user_experience_areas
    ADD CONSTRAINT FK_USER_EXPERIENCE_AREAS_ON_GEOAREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE user_experience_areas
    ADD CONSTRAINT FK_USER_EXPERIENCE_AREAS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_inspirations
    ADD CONSTRAINT FK_USER_INSPIRATIONS_ON_INSPIRATION FOREIGN KEY (inspiration_id) REFERENCES inspirations (id);

ALTER TABLE user_inspirations
    ADD CONSTRAINT FK_USER_INSPIRATIONS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_itinerary
    ADD CONSTRAINT FK_USER_ITINERARY_ON_GEO_AREA FOREIGN KEY (geo_area_id) REFERENCES geoareas (id);

ALTER TABLE user_itinerary
    ADD CONSTRAINT FK_USER_ITINERARY_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_preferred_experiences
    ADD CONSTRAINT FK_USER_PREFERRED_EXPERIENCES_ON_EXPERIENCE FOREIGN KEY (experience_id) REFERENCES experiences (id);

ALTER TABLE user_preferred_experiences
    ADD CONSTRAINT FK_USER_PREFERRED_EXPERIENCES_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_purchases
    ADD CONSTRAINT FK_USER_PURCHASES_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE user_todo_list_items
    ADD CONSTRAINT FK_USER_TODO_LIST_ITEMS_ON_LIST FOREIGN KEY (list_id) REFERENCES todo_lists (id);

ALTER TABLE user_todo_list_items
    ADD CONSTRAINT FK_USER_TODO_LIST_ITEMS_ON_LISTITEM FOREIGN KEY (list_item_id) REFERENCES todo_list_items (id);

ALTER TABLE user_todo_list_items
    ADD CONSTRAINT FK_USER_TODO_LIST_ITEMS_ON_USER FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE users_authorities
    ADD CONSTRAINT fk_useaut_on_authority FOREIGN KEY (authority_id) REFERENCES authorities (id);

ALTER TABLE users_authorities
    ADD CONSTRAINT fk_useaut_on_user FOREIGN KEY (user_id) REFERENCES users (id);
