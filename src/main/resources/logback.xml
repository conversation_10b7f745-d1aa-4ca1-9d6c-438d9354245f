<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/base.xml" />

    <appender name="PAPERTRAIL" class="com.papertrailapp.logback.Syslog4jAppender">

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>

        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%highlight([%.-1level]) %35.35logger{35}:\t%m\t%cyan%ex{5}</pattern>
        </layout>

        <syslogConfig class="org.productivity.java.syslog4j.impl.net.tcp.ssl.SSLTCPNetSyslogConfig">
            <!-- remote system to log to -->
            <host>logs5.papertrailapp.com</host>
            <!-- remote port to log to -->
            <port>36542</port>
            <!-- program name to log as -->
            <ident>Visited-Backend-Java</ident>
            <!-- max log message length in bytes -->
            <maxMessageLength>128000</maxMessageLength>
        </syslogConfig>
    </appender>

    <root level="INFO">
        <appender-ref ref="PAPERTRAIL" />
        <appender-ref ref="CONSOLE" />
    </root>

</configuration>
