spring:
  config:
    activate:
      on-profile: dev
  devtools:
    add-properties: false
    livereload:
      enabled: true
---

# config context path to "/" by setting an empty string
server:
  contextPath:
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain


spring:
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        dialect: org.hibernate.dialect.PostgreSQLDialect  # or your specific database dialect
    hibernate:
      ddl-auto: validate

#Datasource
#Production Database, only enable if really needed
#  datasource:
#    url: *********************************************************************************
#    username: visited
#    password: visitedpass
#    hikari:
#      leak-detection-threshold: 10000
#    tomcat:
#      max-active: 60
#      max-wait: 60000
#      test-on-borrow: true

  # LOCAL DATABASE
  datasource:
    url: ${VISITED_DATABASE_URL}
    username: ${VISITED_DATABASE_USERNAME}
    password: ${VISITED_DATABASE_PASSWORD}
    hikari:
      leak-detection-threshold: 2000
# JACKSON
  jackson:
    serialization:
      INDENT_OUTPUT: true
      WRITE_DATES_AS_TIMESTAMPS: false
  mail:
    host: email-smtp.us-east-1.amazonaws.com
    username: ********************
    password: BFSdogeZHuyUy4iI3koCZNZGGt9+kdz7vlBukBnNTQqZ
    properties:
      mail:
        smtp:
          port: 587
          auth: true
          starttls:
            enabled: true
            required: true

jwt:
  header: Authorization
  secret: my$3cr37w0rd
  expiration: 1800
#  route:
#    authentication:
#      path: auth
#      refresh: refresh

logging:
  level:
    org.springframework.core.env: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql: TRACE
    org.hibernate: DEBUG

swagger:
  ui:
    enabled: true

visited:
  host: http://localhost:8080

iap:
  secret: b38c2a419c4844ab96b8cf7329daa1c6
  sandbox: true
  proLifetime: com.highheels.visited.dev.unlimited
  proSubscriptionMonthly: com.visited.dev.subscription.monthly
  proSubscriptionAnnual: com.visited.dev.subscription.yearly
  removeAds: com.highheels.visited.dev.removeads
  unlockRegions:  com.highheels.visited.dev.regions
  unlockInspirations: com.highheels.visited.dev.inspirations
  unlockCities: com.higheels.visited.dev.cities
  unlockItineraries: com.highheels.visited.dev.itineraries

email:
  host: smtp.maileroo.com
  port: 587
  address: <EMAIL>
  password: 66cf0f9387791423215cb201

#email:
#  host: email-smtp.us-east-1.amazonaws.com
#  port: 587
#  address: ********************
#  password: BFSdogeZHuyUy4iI3koCZNZGGt9+kdz7vlBukBnNTQqZ

stripe:
  publishKey: pk_test_51LP6J9JYxEcRrjMrsBK0Bj6yWeqE8SJUlVW254pwYwcyp1DCx9IRqkT1hhg74KZAYkwXdWiSNNcWYlQ87IEBPugL00goWyRbe9
  secretKey: sk_test_51LP6J9JYxEcRrjMrDkcly7o7Dk2ELXV93nt2kIn13kCTJotauC2ev8SpFOnD91KVfitzm1EunKjoEzF50Rx9r2aT00EO15tMWN
  webhook: whsec_fb825e6600d41e6e3abd50ac7414a150fbd7c09d26e6876f72c9e5fac6cc79e7
  canadianTax: txr_1LPVvMJYxEcRrjMrdGQX3v5q

sentry:
  dsn: https://<EMAIL>/5936023

  # Add data like request headers and IP for users,
  # see https://docs.sentry.io/platforms/java/guides/spring-boot/data-management/data-collected/ for more info
  send-default-pii: true
  exception-resolver-order: -**********
  environment: dev

bitly:
  key: ****************************************
  groupName: visited
  groupGuid: Bn8hkBQFK0n
  organizationGuid: On8hkzIujSV

cloud:
  aws:
    stack:
      auto: false
    region:
      auto: false
      static: us-east-1

appStoreConnect:
  issuerId: 69a6de80-15ef-47e3-e053-5b8c7c11a4d1
  keyId: 4GUV723F56
  bundleId: com.HighHeels.Travelist-DEV
  appleId: **********

awsAIHHAccount1:
  key: ********************
  secret: gvwDDZfTu7SB7kg47oE6gy/84Hin2WEJcRiP0nAE
  region: us-east-1

awsAIHHAccount2:
  key: ********************
  secret: 5B1evyMyC7/0t4U6Amc37IUB2cgCrNaS/mZifUhD
  region: us-east-2

googlePlay:
  packageName: com.arrivinginhighheels.visited.dev
  applicationName: Visited Dev
  key: classpath:googlePlay/google_play_api_key.json
