spring:
  config:
    activate:
      on-profile: aws
---

# config context path to "/" by setting an empty string
server:
  contextPath:
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
  # JPA
  jpa:
    properties:
      hibernate:
        show_sql: false
        use_sql_comments: false
        format_sql: false
        hbm2ddl.auto: validate
    hibernate:
      ddl-auto: validate
  #Datasource
  datasource:
    url: *********************************************************************************
    username: visited
    password: visitedpass
    hikari:
      leak-detection-threshold: 10000
    tomcat:
      max-active: 60
      max-wait: 60000
      test-on-borrow: true

  # JACKSON
  jackson:
    serialization:
      INDENT_OUTPUT: true
      WRITE_DATES_AS_TIMESTAMPS: false
  mail:
    host: email-smtp.us-east-1.amazonaws.com
    username: AKIAXCIXR4FH5UXHBPBY
    password: B<PERSON>dogeZHuyUy4iI3koCZNZGGt9+kdz7vlBukBnNTQqZ
    properties:
      mail:
        smtp:
          port: 587
          auth: true
          starttls:
            enabled: true
            required: true

jwt:
  header: Authorization
  secret: AW$andV1s173dB4ck3nd
  expiration: 1800
#  route:
#    authentication:
#      path: auth
#      refresh: refresh

swagger:
  ui:
    enabled: false

visited:
  host: https://visitedbackend.arrivinginhighheels.com

iap:
  secret: b38c2a419c4844ab96b8cf7329daa1c6
  sandbox: false
  proLifetime: com.highheels.visited.unlimited
  proSubscriptionMonthly: com.visited.subscription.monthly
  proSubscriptionAnnual: com.visited.subscription.yearly
  removeAds: com.highheels.visited.removeads
  unlockRegions: com.highheels.visited.regions
  unlockInspirations: com.highheels.visited.inspirations
  unlockCities: com.higheels.visited.cities
  unlockItineraries: com.highheels.visited.itineraries

sentry:
  dsn: https://<EMAIL>/5936023

  # Add data like request headers and IP for users,
  # see https://docs.sentry.io/platforms/java/guides/spring-boot/data-management/data-collected/ for more info
  send-default-pii: true
  exception-resolver-order: -2147483647
  environment: aws

email:
  host: smtp.maileroo.com
  port: 587
  address: <EMAIL>
  password: 66cf0f9387791423215cb201

stripe:
  publishKey: pk_live_51LP6J9JYxEcRrjMr0oWDWNidmQnGBkNSLDJOLxz6XJFYhhS9LVidPwmiRjkbGG7vuIhLpJ6vSXVsSQ93um1w8Fag00USBySHFx
  secretKey: ***********************************************************************************************************
  webhook: whsec_14Kv8kHK2hUczLtOKxzJTAcEAdNPbi9O
  canadianTax: txr_1Le2uIJYxEcRrjMrapXjDdvz

bitly:
  key: ****************************************
  groupName: visited
  groupGuid: Bn8hkBQFK0n
  organizationGuid: On8hkzIujSV


cloud:
  aws:
    stack:
      auto: false
    region:
      auto: false
      static: us-east-1

appStoreConnect:
  issuerId: 69a6de80-15ef-47e3-e053-5b8c7c11a4d1
  keyId: 4GUV723F56
  bundleId: com.HighHeels.Travelist
  appleId: *********

awsAIHHAccount1:
  key: ********************
  secret: gvwDDZfTu7SB7kg47oE6gy/84Hin2WEJcRiP0nAE
  region: us-east-1

awsAIHHAccount2:
  key: ********************
  secret: 5B1evyMyC7/0t4U6Amc37IUB2cgCrNaS/mZifUhD
  region: us-east-2

googlePlay:
  packageName: com.arrivinginhighheels.visited
  applicationName: Visited
  key: classpath:googlePlay/google_play_api_key.json
