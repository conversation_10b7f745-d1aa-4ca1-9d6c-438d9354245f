<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="description" content="Flutter Summernote HTML Editor">
  <meta name="author" content="tneotia">
  <title>Summernote Text Editor HTML</title>
  <script src="jquery.min.js"></script>
  <!--summernote js and css-->
  <link href="summernote-lite.min.css" rel="stylesheet">
  <script src="summernote-lite.min.js"></script>
</head>
<body>
<div id="summernote-2"></div>
<!--summernote at mention plugin-->
<script src="plugins/summernote-at-mention/summernote-at-mention.js"></script>
<!--minor styling to improve editor design-->
<style>
  body {
      display: block;
      margin: 0px;
  }
  .note-editor.note-airframe, .note-editor.note-frame {
      border: 0px solid #a9a9a9;
  }
  .note-frame {
      border-radius: 0px;
  }
</style>
</body>
</html>