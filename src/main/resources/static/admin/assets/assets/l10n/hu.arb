{"@@last_modified": "2021-08-18 22:56:33.358390", "@@locale": "hu", "appName": "Visited", "clear": "Nullázás", "been": "Volt", "want": "Szeretném", "live": "<PERSON><PERSON><PERSON><PERSON>", "lived": "<PERSON><PERSON><PERSON>", "water": "Víz", "land": "Szárazföld", "borders": "<PERSON><PERSON><PERSON>", "labels": "Cetlik", "legend": "<PERSON>a", "inspiration": "Inspir<PERSON><PERSON><PERSON>", "inspirations": "Inspirációk", "delete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlockVisitedUpsellTitle": "<PERSON><PERSON><PERSON><PERSON> többet l<PERSON>?", "unlockVisitedUpsellSubtitle": "Oldja fel az összes funkciót és élvezze a Visited-ot teljes valójában", "checkTheDetails": "Nézze meg a részleteket", "moreInspirationsComingSoon": "Dolgozunk a képek beszerzésén. Jöjjön v<PERSON>za később!", "unlockPremiumFeatures": "Oldja fel a prémium funkiciókat", "purchased": "Megvéve!", "buy": "Vétel", "restorePurchases": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ok": "Rendben", "areYouSure": "<PERSON><PERSON><PERSON> benne?", "deleteInspirationConfirmMessage": "E kártya kitörtlése végleges. Nincs mód a kép visszaszerzésére.", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "map": "Térkép", "progress": "<PERSON><PERSON><PERSON>", "myTravelGoal": "Az utazási célom", "goalRemaining": "{remaining} még ennyi kell!", "@goalRemaining": {"description": "Shows on the dashboard how many countries remaining to visit.", "placeholders": {"remaining": {"description": "number of countries", "example": "15"}}}, "top": "TOP", "ofTheWorld": "A világról!", "countries": "országok", "topPlacesVisitedFromCountry": "{country}-b<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> országok", "@topPlacesVisitedFromCountry": {"placeholders": {"country": {}}}, "login": "Bejelentkezés", "logout": "Kijelentkezés", "enterYourEmail": "Adja meg az ímé<PERSON>", "privacyPolicy": "Adatvédelmi <PERSON>", "privatePolicyUrl": "https://www.arrivinginhighheels.com/privacy-policy-hungarian/", "termsOfUse": "Felhasználási Feltételek", "termsOfUserUrl": "https://www.arrivinginhighheels.com/terms-of-use-hungarian/", "errorTitle": "Hoppá!", "enterValidEmail": "Kérjük érvényem ímél címet adjon meg", "settings": "Beállítások", "whereDoYouLive": "Hol él?", "whereHaveYouBeen": "Hol járt már?", "whereDoYouFlyFrom": "Honnan repül ki?", "next": "Következő", "missingAirports": "Nem tal<PERSON>lja amit keres? Küldjön egy ímé<NAME_EMAIL> ímél címre", "missingAirportsEmailTitle": "Hiányzó Repterek!", "supportEmailAddress": "", "welcomeTitle": "Üdvözöljük a Visited-ban", "welcomeSubtitle": "<PERSON><PERSON>re szóló ka<PERSON> várja", "getStarted": "<PERSON><PERSON><PERSON><PERSON><PERSON> bele", "privacyAgreement": "Adatvédel<PERSON>", "privacyAgreementSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fogadja el a következő elemeket, mielőtt folytatná a Visited használatát.", "privacyAgreementTermsMarkdown": "Ennek a négyzetnek a bepipálásával tudomásul veszi, és elfogadja, hogy a Arriving in High Heels kötelezi Önt [Adatvédelmi irányelvek] (https://www.arrivinginhighheels.com/terms-of-use).", "privacyAgreementOptIn": "Bel<PERSON><PERSON><PERSON><PERSON>, hogy a Arriving High Heels-től érkező elektronikus üzeneteket kapjak, amelyek információkat és ajánlatokat tartalmaznak a számomra érdekes termékekről, alkalmazásokról és szolgáltatásokról, beleértve az értékesítésről szóló értesítést, akciókat, ajánlatokat és hírlevelet. Ezt a hozzájárulást bármikor visszavonhatom az Adatvédelmi irányelvekben leírtak szerint, vagy az elektronikus üzenetek „leiratkozás” linkjére kattintva.", "submit": "<PERSON><PERSON><PERSON><PERSON>", "companyName": "Arriving In High Heels Corporation", "companyAddress": "31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6", "privacyAgreementRequired": "El kell fogadnia mindkét feltételt és döntést, hogy tov<PERSON><PERSON> használhass a Visited-ot", "deleteAccount": "Felhasz<PERSON><PERSON><PERSON> törl<PERSON>", "removeAdsUpsell": "Szeretne leiratkozni a hirdetésekről, és íméles marketingről?", "deleteAccountWarning": "A felhasználója törtésével minden információ kitörtlődik a szerverünkről. Ezt a folyamatot nem lehet v<PERSON>zavonni.", "about": "<PERSON><PERSON><PERSON> meg többet", "popularity": "Népszerűség", "regions": "Régiók", "population": "Népessék", "size": "<PERSON><PERSON><PERSON>", "coverage": "Átfedés", "percentOfCountryVisited": "Az országok %-a meglátogatva", "visited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notes": "Jegyzetek", "kmSquared": "km²", "customize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onlyCountSovereign": "Csak a független államok beszámítása", "countUkSeparately": "Az Egyesült Királyság országainak külön beszámítása", "showLegend": "<PERSON><PERSON>", "showLivedPin": "Jelenlegi tűzők mutatása", "useMyColours": "Az én színeim használata", "mapColors": "Térkép <PERSON>", "traveller": "<PERSON>z u<PERSON>ó", "nightTraveller": "<PERSON><PERSON> u<PERSON>", "original": "Az E<PERSON>eti", "explorer": "A Felfedező", "weekender": "A Hétvégés", "naturalist": "A Természettudós", "historian": "A Történész", "thrillSeeker": "Az IzgalomKereső", "culturalBuff": "A MűvelődésiGorilla", "myColors": "Az én színeim", "experiences": "Tapasztalatok", "done": "<PERSON><PERSON><PERSON>", "experiencesInstructions": "A kezdés<PERSON>z nyomja meg a + gombot!", "continueText": "<PERSON><PERSON><PERSON><PERSON>", "experiencesDescription": "<PERSON>t szeret c<PERSON>álni az utazása során?", "visitedWebsiteShortLink": "https://www.visitedapp.com", "sharingHashtag": "#Visited", "myTravelMap": "Az én utazási térképem", "percentOfWorldSeen": "<PERSON><PERSON><PERSON> a vil<PERSON>g {percentage}%-<PERSON><PERSON> l<PERSON>m", "@percentOfWorldSeen": {"description": "Message sent when sharing your map", "placeholders": {"percentage": {}}}, "requiresOnline": "Sajnáljuk, Visited-hoz aktív internetkapcsolat szükséges. Kérjük nyissa meg a beállításokat és nézze meg, hogy a wifi be van-e kapcsolva a repülő üzemmód pedig ki.", "list": "Lista", "more": "<PERSON><PERSON><PERSON>", "myCountrySelections": "A Kiválasztott Országaim", "cities": "<PERSON><PERSON><PERSON><PERSON>", "citiesInstructions": "Koppintson az egy városra, hogy elkezdje a városok kiválasztásán", "missingCitiesEmailTitle": "Hiányzó Városok!", "disputedTerritories": "Vitatott területek", "places": "<PERSON><PERSON><PERSON>", "noListsError": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> nincs el<PERSON>, kérjük próbálja meg kés<PERSON>bb", "noInspirationsError": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON> nincs el<PERSON>r<PERSON> fotó, kérjük próbálja meg kés<PERSON>bb", "sponsored": "Sz<PERSON>nzor<PERSON>lt", "lists": "Listák", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mostFrequentlyVisitedCountries": "A leggyakrabban látogatott országok:", "loginWallSubtitle": "Hozzon létre egy ingyenes fiókot, hogy megtapasztalja az Visited teljes verzióját", "loseAllSelectionsWarning": "Az alkalmazás bezárása után elveszíti az összes választását.", "createAccount": "Fiók létrehozása", "continueWithoutAccount": "Folytatás fiók nélkül", "inspirationPromotion": "Ihletet kap a gyönyörű utazási fotózás", "saveStatsPromotion": "Mentsd meg utazási statisztikáidat!", "selectRegionsPromotion": "Államok és tartományok kiválasztása", "experiencesPromotion": "Élmények nyomon követése a világ minden tájáról", "missingListItem": "Hiányzott valami? <PERSON><PERSON><PERSON><PERSON> ide, hogy e -mailt küldjön <PERSON>ü<PERSON>, hogy hozzáadja a kedvenc helyét.", "missingListItemEmailTitle": "Hiányzó tétel a {list}", "@missingListItemEmailTitle": {"description": "Subject of email when requesting a missing item", "placeholders": {"list": {}}}, "signup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listShareMessage": "Meglátogattam a {amount} {listName} -et", "orderPoster": "Poszter", "shareMap": "Megosztási térkép", "posterLandingPageTitle": "Szerezd meg a posztered", "posterNotAvailableError": "A poszter vásárlása jelenleg nem érhető el<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "posterPricePlusShipping": "{price} + {shipping} Szállítás", "posterDescriptionMarkdown": "## Az egyedi nyomtatási térképeinkről\nNyomtassa ki személyre szabott világtérképét. Testreszabhatja a saj<PERSON><PERSON>, és egyenesen otthonához szállítsa.\n \n### <PERSON><PERSON><PERSON><PERSON> adatok:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Táj orientációja.\n- <PERSON><PERSON><PERSON>, cseppe<PERSON> pontos nyomatokhoz, 8 bites s<PERSON><PERSON>, szinte fotónyomtatás,\n- 0,22 mm vastag szatén papír\n\n### Szállítási adatok:\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ka<PERSON>d<PERSON>b<PERSON><PERSON> a vil<PERSON><PERSON> b<PERSON><PERSON>, a Canada Post segítségével. K<PERSON>rjük, hagyjon 2–4 hetet a legtöbb rendeltetési helyre történő szállításhoz. Az összes megrendelést egy kartondobozba rakják a benyújtott szállítási címre. Az összes fizetést az a Google Pay vagy a Stripe kezeli.\n\n\n### Lemondás/visszatérítés:\nA megrendeléseket azonnal feldolgozzák, miután a lehető leggyorsabban benyújtották. Ezért nincs elérhető visszatérítés/lemondás.", "posterDescriptionMarkdownApple": "## Az egyedi nyomtatási térképeinkről\nNyomtassa ki személyre szabott világtérképét. Testreszabhatja a saj<PERSON><PERSON>, és egyenesen otthonához szállítsa.\n \n### <PERSON><PERSON><PERSON><PERSON> adatok:\n- 16\" x 20\" (40.64cm x 50.8cm)\n- Táj orientációja.\n- <PERSON><PERSON><PERSON>, c<PERSON>ppe<PERSON> pontos nyomatokhoz, 8 bites s<PERSON><PERSON>, szinte fotónyomtatás,\n- 0,22 mm vastag szatén papír\n\n### Szállítási adatok:\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ka<PERSON>d<PERSON>b<PERSON><PERSON> a vil<PERSON><PERSON> b<PERSON><PERSON>, a Canada Post segítségével. K<PERSON>rjük, hagyjon 2–4 hetet a legtöbb rendeltetési helyre történő szállításhoz. Az összes megrendelést egy kartondobozba rakják a benyújtott szállítási címre. Az összes fizetést az Apple Pay, a vagy a Stripe kezeli.\n\n\n### Lemondás/visszatérítés:\nA megrendeléseket azonnal feldolgozzák, miután a lehető leggyorsabban benyújtották. Ezért nincs elérhető visszatérítés/lemondás.", "posterCustomizeTitle": "Testreszabja a plakátot", "enterShippingAddress": "<PERSON><PERSON><PERSON> be a szállítási címet", "price": "<PERSON><PERSON>", "formattedPlusTax": "{formattedPrice} + ad<PERSON>", "showSelections": "A kiválasztás megjelenítése", "posterNoRefunds": "A poszter kinyomtatása után nem állnak rendelkezésre visszatérítés.", "posterReviewOrder": "Nézze át rende<PERSON>t", "email": "Email", "emailEmptyError": "<PERSON><PERSON><PERSON>, <PERSON>rja be az e-mail címét", "fullName": "Teljes név", "fullNameEmptyError": "<PERSON><PERSON><PERSON> adja meg a teljes nev<PERSON>t", "streetAddressEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON> be az utcai címét", "cityEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, írja be a városát", "fieldEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be a {fieldName}", "country": "<PERSON><PERSON><PERSON><PERSON>", "countryEmptyError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be <PERSON> orsz<PERSON>", "posterReviewOrderTitle": "Nézze át rende<PERSON>t", "buyNow": "<PERSON><PERSON><PERSON><PERSON><PERSON> most", "secureCheckoutDisclaimer": "Biztons<PERSON><PERSON>, amelyet a Stripe biztosít", "total": "<PERSON><PERSON><PERSON>", "tax": "<PERSON><PERSON>", "subtotal": "Alto<PERSON><PERSON>", "posterProductName": "Egyéni me<PERSON>látogatott térképplakát", "shipping": "Szállítás", "posterOrderReceivedTitle": "A megrendelés beérkezett", "posterOrderReceivedSubtitle": "Megkaptuk a megrendelését!", "posterOrderReceivedInstructionsMarkdown": "További friss<PERSON><PERSON>sek<PERSON>rt ellenőrizze e-mailjeit. \nK<PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> legfeljebb 4 hetet, amíg a poszter megérkezik. \nHa bármilyen kérd<PERSON> van, k<PERSON><PERSON><PERSON><PERSON><PERSON>, írjon nekünk az [<EMAIL>] (<EMAIL>) címre.", "posterOrderReceivedEmailSubject": "Nyomtatott poszter megrendelés állapota", "moreInfo": "<PERSON><PERSON><PERSON>", "logoutConfirm": "Szeretne bejelentkezni az alkalmazásból?", "emailNotAvailable": "Ezt az e -mailt megkapták.", "alphabetical": "Betűrendes", "firstTimeLiveTutorial": "A szülőföld és a város biztosítása személyre szabja az alkalmazás élményét.", "firstTimeBeenTutorial": "Ha kiválasztja, hogy hol volt, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy megtekintse az összes ország té<PERSON>t, ahol já<PERSON>, és láthatja a személyes statisztikákat.", "progressTooltipGoal": "Utazási c<PERSON> azon országok számán <PERSON>, am<PERSON><PERSON><PERSON> \"szeretne\" utazni olyan országokhoz képest, ahol \"voltál\".", "progressTooltipRank": "<PERSON>z a <PERSON><PERSON> meg<PERSON>, hogyan rang<PERSON>olod az utazókhoz képest a világ minden tájáról. Növelheti rang<PERSON>, ha több országba utazik.", "progressTooltipPercentageOfWorld": "Ez a grafikon azon országok számán alapul, amelyekben a világ összes országa összehasonlítása volt.", "sortBy": "<PERSON><PERSON><PERSON>", "updateWishlist": "Frissítse a kívánságlistát", "mapInfo": "Kattintson az or<PERSON>, hogy válassza ki a választást, a Want vagy a Live -t. A lista nézethez kattintson a bal felső sarokban található ikonra is.", "oneTimePurchase": "Minden egyszeri vásárlás!", "contact": "Kapcsolatba lépni", "contactUs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>ü<PERSON>", "noCitiesSelected": "Még nem választott ki egyetlen várost sem ...", "updateTravelGoal": "Frissítse az utazási célt", "travelGoalComplete": "Gratulálunk! \n\ntap a + gombot további országok hozzáadásához.", "loginEmailNotFoundError": "<PERSON><PERSON><PERSON> s<PERSON> társítva az {email} e -mailt. Szeretné most létrehozni?", "tryAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itineraries": "Utazási tervek", "itinerary": "Utazási terv", "place": "<PERSON><PERSON>", "itinerariesDescription": "Ezek a<PERSON> a he<PERSON><PERSON>, am<PERSON><PERSON> ir<PERSON>t érdeklődését kifejezte.\nHasználja ezt az útmutatót, hogy segítsen megtervezni a következő nyaralását.", "addMore": "Továbbiak hozzáadása", "interests": "Érdeklődés", "selection": "Kiválasztás", "goal": "<PERSON><PERSON><PERSON>", "noItineraries": "Nincsenek Útiterv", "noItinerariesExplanation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ins<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, hogy l<PERSON>thassa az útiterv automatikusan generálódik.", "clusterPins": "Csoportosított Térkép Jelölők", "toggleRegions": "Régiók megjelenítése zoomoláskor", "mapProjection": "Térképvetület", "mercator": "Mercator", "equirectangular": "Egy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "yourTravellerType": "Az Ön utazói típusa:", "yourHotelPreferences": "Hotel preferenciái:", "budget": "Költségvetés", "midScale": "Közepes szint", "luxury": "Luxus", "noTravellerType": "Adjon hozzá elemeket a kívánságlistájához, hogy me<PERSON>, mi<PERSON><PERSON> tí<PERSON>.", "unlockLived": "<PERSON><PERSON><PERSON><PERSON>", "unlockLivedDescription": "Válassza ki a térképen, hol élt korábban!", "futureFeaturesDescription": "...és minden jövőbeli funkció", "departureDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>", "returnDate": "Visszaté<PERSON><PERSON> d<PERSON>", "hotels": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "food": "Étel", "travelDates": "Utazási <PERSON>", "posterForMe": "<PERSON><PERSON><PERSON>", "posterSendGift": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>e", "addSelections": "Választások hozzáadása", "posterType": "Poszter típusa"}