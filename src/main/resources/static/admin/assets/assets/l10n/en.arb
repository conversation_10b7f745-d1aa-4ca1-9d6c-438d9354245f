{"@@last_modified": "2021-08-18 22:56:33.325169", "@@locale": "en", "appName": "Visited", "clear": "Clear", "been": "Been", "want": "Want", "live": "Live", "lived": "Lived", "water": "Water", "land": "Land", "borders": "Borders", "labels": "Labels", "legend": "Legend", "inspiration": "Inspiration", "inspirations": "Inspirations", "delete": "Delete", "unlockVisitedUpsellTitle": "Want to see more?", "unlockVisitedUpsellSubtitle": "Unlock all features and enjoy Visited in it’s full strength", "checkTheDetails": "Check the Details", "moreInspirationsComingSoon": "We are working on getting more images.  Check back soon!", "unlockPremiumFeatures": "Unlock premium features", "purchased": "Purchased!", "buy": "Buy", "restorePurchases": "Restore Purchase", "ok": "Ok", "areYouSure": "Are you sure?", "deleteInspirationConfirmMessage": "Deleting this card is permanent.  There is no way to recover this image.", "cancel": "Cancel", "map": "Map", "progress": "Progress", "myTravelGoal": "My Travel Goal", "goalRemaining": "{remaining} more to go!", "@goalRemaining": {"description": "Shows on the dashboard how many countries remaining to visit.", "placeholders": {"remaining": {"description": "number of countries", "example": "15"}}}, "top": "TOP", "ofTheWorld": "of the world!", "countries": "countries", "topPlacesVisitedFromCountry": "Top Countries Visited from {country}:", "@topPlacesVisitedFromCountry": {"placeholders": {"country": {}}}, "login": "Log In", "logout": "Log Out", "enterYourEmail": "Enter your email", "privacyPolicy": "Privacy Policy", "privatePolicyUrl": "https://www.arrivinginhighheels.com/privacy-policy", "termsOfUse": "Terms of Use", "termsOfUserUrl": "https://www.arrivinginhighheels.com/terms-of-use", "errorTitle": "Whoops!", "enterValidEmail": "Please enter a valid email", "settings": "Settings", "whereDoYouLive": "Where do you live?", "whereHaveYouBeen": "Where have you been?", "whereDoYouFlyFrom": "Where do you fly out of?", "next": "Next", "missingAirports": "Don't see what you are looking for? Send us an <NAME_EMAIL>", "missingAirportsEmailTitle": "Missing Airports!", "supportEmailAddress": "<EMAIL>", "welcomeTitle": "Welcome to Visited", "welcomeSubtitle": "The adventure of a lifetime awaits", "getStarted": "Get Started", "privacyAgreement": "Privacy Agreement", "privacyAgreementSubtitle": "Please agree to the following items before continuing to use Visited.", "privacyAgreementTermsMarkdown": "By checking this box, you acknowledge that you have read and agree to be bound by Arriving in High Heels’  [Privacy Policy](https://www.arrivinginhighheels.com/privacy-policy) and [Terms of Use](https://www.arrivinginhighheels.com/terms-of-use).", "privacyAgreementOptIn": "I agree to receive electronic messages from Arriving in High Heels containing information and offers with respect to products, applications and services that may be of interest to me, including notification of sales, promotions, offers and newsletters. I may withdraw this consent at any time as described in the Privacy Policy or by clicking on the “unsubscribe” link in the electronic messages.", "submit": "Submit", "companyName": "Arriving In High Heels Corporation", "companyAddress": "31 Claudview Street\nKing City, Ontario\nCanada\nL7B 0C6", "privacyAgreementRequired": "You must agree to both our terms and opt in order to continue using Visited.", "deleteAccount": "Delete Account", "removeAdsUpsell": "Do you wish to opt out of ads and unsubscribe from email marketing instead?", "deleteAccountWarning": "Deleting your account will remove all of your information from our servers.\nThis process cannot be undone.", "about": "About", "popularity": "Popularity", "regions": "Regions", "population": "Population", "size": "Size", "coverage": "Coverage", "percentOfCountryVisited": "% of country visited", "visited": "visited", "notes": "Notes", "kmSquared": "km²", "customize": "Customize", "onlyCountSovereign": "Only Count Sovereign Countries", "countUkSeparately": "Count U.K. Countries separately", "showLegend": "Show Legend", "showLivedPin": "Show Lived Pin", "useMyColours": "Use My Colors", "mapColors": "Map Colors", "traveller": "The Traveller", "nightTraveller": "The Night Traveller", "original": "The Original", "explorer": "The Explorer", "weekender": "The Weekender", "naturalist": "The Naturalist", "historian": "The Historian", "thrillSeeker": "The Thrill Seeker", "culturalBuff": "The Cultural Buff", "myColors": "My Colors", "experiences": "Experiences", "done": "Done", "experiencesInstructions": "Tap the + button to get started!", "continueText": "Continue", "experiencesDescription": "What do you like to do when you travel?", "visitedWebsiteShortLink": "https://www.visitedapp.com", "sharingHashtag": "#Visited", "myTravelMap": "My Travel Map", "percentOfWorldSeen": "I've seen {percentage}% of the world", "@percentOfWorldSeen": {"description": "Message sent when sharing your map", "placeholders": {"percentage": {}}}, "requiresOnline": "Sorry, Visited requires an active network connection.  Please open your settings app and ensure that either \\r Wi-Fi or Cellular data is enabled and Airplane Mode is disabled", "list": "List", "more": "More", "myCountrySelections": "My Country Selections", "cities": "Cities", "citiesInstructions": "Tap on any country to start selecting cities.", "missingCitiesEmailTitle": "Missing Cities!", "lists": "Lists", "disputedTerritories": "Disputed Territories", "sponsored": "Sponsored", "places": "Places", "noListsError": "Op<PERSON>, no lists available at this time, please try a bit later", "noInspirationsError": "<PERSON><PERSON>, no photos are available right now, please try a bit later", "mostFrequentlyVisitedCountries": "Your most frequently visited countries:", "update": "update", "signup": "Sign Up", "loginWallSubtitle": "Create a free account to experience the full version of Visited", "loseAllSelectionsWarning": "You will lose all your selections after closing the app.", "createAccount": "Create Account", "continueWithoutAccount": "Continue without an Account", "inspirationPromotion": "Get inspired with beautiful travel photography", "saveStatsPromotion": "Save Your Travel Stats!", "selectRegionsPromotion": "Select States and Provinces", "experiencesPromotion": "Track Experiences all around the World", "missingListItem": "Did we miss something?  Tap here to send us an email to get your favorite place added.", "missingListItemEmailTitle": "Missing Item from {list}", "@missingListItemEmailTitle": {"description": "Subject of email when requesting a missing item", "placeholders": {"list": {}}}, "listShareMessage": "I have visited {amount} {listName}", "@listShareMessage": {"placeholders": {"amount": {}, "listName": {}}}, "orderPoster": "Poster", "shareMap": "Share Map", "posterLandingPageTitle": "Get Your Poster", "posterNotAvailableError": "Poster purchasing is not available right now.  Please try again later.", "posterPricePlusShipping": "{price} + {shipping} shipping", "@posterPricePlusShipping": {"placeholders": {"price": {}, "shipping": {}}}, "posterDescriptionMarkdownApple": "## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Apple Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.", "posterDescriptionMarkdown": "## About Our Custom Print Maps\nPrint your personalized world map. Customize it with your very own colors and have it delivered straight to your home.\n \n### Specifications: \n- 16\" x 20\" (40.64cm x 50.8cm)\n- Landscape orientation.\n- Micro ink, droplets for precise prints, 8 bit color, almost photo print quality, \n- 0.22mm thick Satin Paper\n\n### Shipping Details:\n- Shipping from Toronto, Canada to anywhere in the world using Canada Post. \n- Please allow 2 to 4 weeks for delivery to most destinations. \n- All orders are shipped rolled up in a cardboard tube box to the shipping address submitted.\n\n### How Payment is Handled: \n All payment is handled by Google Pay or Stripe.\n\n\n### Cancellation/Refund: \nOrders are processed immediately after being submitted for the fastest turnaround possible. Therefore, there is no refund/cancellation available.", "posterCustomizeTitle": "Customize Poster", "enterShippingAddress": "Enter Shipping Address", "price": "Price", "formattedPlusTax": "{formattedPrice} + tax", "@formattedPlusTax": {"placeholders": {"formattedPrice": {}}}, "showSelections": "Show Selections", "posterNoRefunds": "No refunds are available after your poster has been printed.", "posterReviewOrder": "Review Your Order", "email": "Email", "emailEmptyError": "Please enter your email", "fullName": "Full Name", "fullNameEmptyError": "Please enter your full name", "streetAddressEmptyError": "Please enter your street address", "cityEmptyError": "Please enter your city", "fieldEmptyError": "Please enter your {fieldName}", "@fieldEmptyError": {"placeholders": {"fieldName": {}}}, "country": "Country", "countryEmptyError": "Please enter your country", "posterReviewOrderTitle": "Review Your Order", "buyNow": "Buy Now", "secureCheckoutDisclaimer": "Secure checkout provided by Stripe", "total": "Total", "tax": "Tax", "subtotal": "Subtotal", "posterProductName": "Custom Visited Map Poster", "shipping": "Shipping", "posterOrderReceivedTitle": "Order Received", "posterOrderReceivedSubtitle": "We received your order!", "posterOrderReceivedInstructionsMarkdown": "Check your email for more updates.\nPlease allow up to 4 week for your poster to arrive.\nIf you have any questions, please email us at [<EMAIL>](<EMAIL>)", "posterOrderReceivedEmailSubject": "Printed Poster Order Status", "moreInfo": "More Info", "logoutConfirm": "Would you like to log out of Visited?", "emailNotAvailable": "That email has been taken.", "alphabetical": "Alphabetical", "firstTimeLiveTutorial": "Providing your home country and city will personalize your app experience.", "firstTimeBeenTutorial": "Selecting where you have been allows you to view your map of all the countries you have been to and see personal stats.", "progressTooltipGoal": "Your travel goals are based on the number of countries you \"Want\" to travel compared to countries where you have \"Been\".", "progressTooltipRank": "This number shows how you rank compared to travellers around the world.  You can increase your rank by travelling to more countries.", "progressTooltipPercentageOfWorld": "This graph is based on number of countries you have been to compared to total countries of the world.", "sortBy": "Sort By", "updateWishlist": "Update Wish List", "mapInfo": "Click on the country to select been, want or live. You can also click on the icon found in the top left corner for the list view.", "oneTimePurchase": "Everything is a one time purchase!", "contact": "Contact", "contactUs": "Contact Us", "noCitiesSelected": "You have not selected any cities, yet...", "updateTravelGoal": "Update Travel Goal", "travelGoalComplete": "Congratulations!\n\nYou have completed your travel goal! \n\nTap the + button to add more countries.", "loginEmailNotFoundError": "There is no account associated the email {email}.  Would you like to create it now?", "@loginEmailNotFoundError": {"description": "Presents an error if the user tries to log in with an email that does not exist", "placeholders": {"email": {"description": "The email address the user requested", "example": "<EMAIL>"}}}, "tryAgain": "Try Again", "itineraries": "Itineraries", "itinerary": "Itinerary", "place": "Place", "itinerariesDescription": "These are places you've expressed interest in.\nUse this guide to help plan your next vacation.", "addMore": "Add More", "interests": "Interests", "selection": "Selection", "goal": "Goal", "noItineraries": "No Itineraries", "noItinerariesExplanation": "Please add some places, inspirations or experiences to see your itineraries automatically generate.", "clusterPins": "Cluster Pins", "toggleRegions": "Show Regions", "mapProjection": "Map Projection", "mercator": "Mercator", "equirectangular": "Equirectangular", "yourTravellerType": "Your Traveller Type:", "yourHotelPreferences": "Your Hotel Preferences:", "budget": "Budget", "midScale": "Mid Scale", "luxury": "Luxury", "noTravellerType": "Add items to your bucket list to discover what type of traveller you are.", "unlockLived": "Unlock Lived", "unlockLivedDescription": "Select where you have previously lived on the map!", "futureFeaturesDescription": "...and all future features", "yourMostFrequentlyVisitedCountry": "Your Most Frequently Visited Country:", "departureDate": "Departure Date", "returnDate": "Return Date", "hotels": "Hotels", "food": "Food", "travelDates": "Travel Dates", "posterForMe": "For Me", "posterSendGift": "Send a gift", "addSelections": "Add Selections", "posterType": "Poster Type"}