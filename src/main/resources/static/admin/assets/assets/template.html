<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visited App Newsletter</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333333;
            line-height: 1.5;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }

        .header {
            background-color: #0058b7;
            border-bottom: 3px solid #ffd70f;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .header-logo {
            height: 60px;
            width: auto;
        }

        .header-text {
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            text-decoration: none;
            margin-left: 15px;
        }

        .consent-notice {
            background-color: #e9ecf0;
            padding: 12px 20px;
            text-align: center;
            font-size: 14px;
            color: #666666;
            margin: 0;
        }

        .content {
            padding: 30px 20px;
            font-size: 16px;
        }

        .footer {
            background-color: #0058b7;
            border-top: 3px solid #ffd70f;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #ffffff;
        }

        .footer a {
            color: #ffffff;
            text-decoration: underline;
            margin: 0 10px;
        }

        .address {
            margin-top: 15px;
            font-size: 11px;
            color: #dddddd;
        }

        @media screen and (max-width: 480px) {
            .header-text {
                font-size: 24px;
            }
        }
    </style>
</head>

<body>
<div class="container">
    <div class="header">
        <img class="header-logo"
             src="https://visitedapp.com/wp-content/uploads/2021/07/cropped-visitedtravelapp.png"
             height="80"
             alt="Visited Logo">
        <span class="header-text">Visited</span>
    </div>

    <div class="consent-notice">
        You're receiving this email because you signed up for Visited using %%email_address%%.
    </div>

    <div class="content">
        {{insert_friendly_content_here}}
        <br>
        {{insert content here}}
    </div>

    <div class="footer">
        <div>
            <a href="%%unsubscribe_link%%" target="_blank">Unsubscribe</a> |
            <a href="https://www.arrivinginhighheels.com/privacy-policy/" target="_blank">Privacy Policy</a> |
            <a href="https://www.arrivinginhighheels.com/terms-of-use/" target="_blank">Terms of Use</a>
        </div>
        <div class="address">
            Arriving in High Heels Corporation, 31 Claudview St., King City, ON, L7B0C6, Canada
        </div>
    </div>
</div>
</body>

</html>
