'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"flutter_bootstrap.js": "320cfee0ce81a463c0368011feac7048",
"version.json": "ea0ba6e47e7618e4e48551ba03e73223",
"index.html": "2066b8aa466d14868c467af2b48ce4e1",
"/": "2066b8aa466d14868c467af2b48ce4e1",
"main.dart.js": "e732b2979fff3800ba1bedb9ea59a0e3",
"flutter.js": "76f08d47ff9f5715220992f993002504",
"favicon.png": "5dcef449791fa27946b3d35ad8803796",
"icons/Icon-192.png": "ac9a721a12bbc803b44f645561ecb1e1",
"icons/Icon-maskable-192.png": "c457ef57daa1d16f64b27b786ec2ea3c",
"icons/Icon-maskable-512.png": "301a7604d45b3e739efc881eb04896ea",
"icons/Icon-512.png": "96e752610906ba2a93c65f8abe1645f1",
"manifest.json": "3886a15be459766a603598f73b6a0cca",
"assets/AssetManifest.json": "52bf2f0f15e4fc0df0f722875c939e3d",
"assets/NOTICES": "2fa53ba528b5a7fbc5fe16a08dbfa2db",
"assets/FontManifest.json": "dc3d03800ccca4601324923c0b1d6d57",
"assets/AssetManifest.bin.json": "6a6c98ab039fcbadc8e91b752d53c8cd",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "33b7d9392238c04c131b6ce224e13711",
"assets/packages/flutter_inappwebview_web/assets/web/web_support.js": "509ae636cfdd93e49b5a6eaf0f06d79f",
"assets/packages/flex_color_picker/assets/opacity.png": "49c4f3bcb1b25364bb4c255edcaaf5b2",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css": "5a8d0222407e388155d7d1395a75d5b9",
"assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html": "16911fcc170c8af1c5457940bd0bf055",
"assets/packages/html_editor_plus/assets/plugins/summernote-at-mention/summernote-at-mention.js": "b11db8ec59b494470e6a9ecfe498e67a",
"assets/packages/html_editor_plus/assets/summernote-lite-dark.css": "224219a4d108f1e47aa8d883be6ecd7f",
"assets/packages/html_editor_plus/assets/summernote.html": "0a4697b4b9d3eba6074a152e66b83aaf",
"assets/packages/html_editor_plus/assets/jquery.min.js": "dc5e7f18c8d36ac1d3d4753a87c98d0a",
"assets/packages/html_editor_plus/assets/summernote-no-plugins.html": "e21ecac610dd49340a7c8da0c1cf1a9f",
"assets/packages/html_editor_plus/assets/font/summernote.ttf": "82fa597f29de41cd41a7c402bcf09ba5",
"assets/packages/html_editor_plus/assets/font/summernote.eot": "f4a47ce92c02ef70fc848508f4cec94a",
"assets/packages/html_editor_plus/assets/summernote-lite.min.css": "1cd76f2bcd4d6d7bb8d765a234734796",
"assets/packages/html_editor_plus/assets/summernote-lite.min.js": "1ab07b822bdb41c14c6fab2d4d041d4a",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"assets/AssetManifest.bin": "d28331bb0f86235fd45c5d13f06cead0",
"assets/fonts/MaterialIcons-Regular.otf": "904b0db75f76ba6151548063ae31ae55",
"assets/assets/l10n/it.arb": "d4af11265f258795abec95e27cb799e1",
"assets/assets/l10n/de.arb": "6744bd2efab4bbcfaf8f194949db3748",
"assets/assets/l10n/pt.arb": "3d8781f2a1b55dbada9fe92df0ff9f60",
"assets/assets/l10n/hr.arb": "620b92a0ef5d5148a1d599dec87c88ad",
"assets/assets/l10n/sk.arb": "b9b11b4ccd9a87e79d56dec71bea3104",
"assets/assets/l10n/ro.arb": "a77844ea4c0b1ac472a851a919025a3a",
"assets/assets/l10n/id.arb": "53f63cce2a5f222fd8c7dab96f21d31f",
"assets/assets/l10n/fi.arb": "97411c484989055bb372aff4f2505476",
"assets/assets/l10n/da.arb": "a026df93dc8ec17094e28a5b136000c7",
"assets/assets/l10n/hu.arb": "41cb7ab42b78a03f2de3ecd47c982af5",
"assets/assets/l10n/ko.arb": "18140b44ac9b1a25273ec4acdad3440e",
"assets/assets/l10n/es.arb": "3aabc19f0e3eccd7da9dd5cb824fa944",
"assets/assets/l10n/th.arb": "f3963a8ee6b12f9eb655ef1a53366174",
"assets/assets/l10n/ca.arb": "4c453a763933a4743962594475e4b3a1",
"assets/assets/l10n/nb.arb": "cb17adcb2ea66b1918cfd7223712ff39",
"assets/assets/l10n/cs.arb": "4b3e36fdefe6d984d74a9bff682db882",
"assets/assets/l10n/uk.arb": "c883d082e2306e8a35a12313f5c9ccfc",
"assets/assets/l10n/nl.arb": "482984aa82466b02e41641fde35dad51",
"assets/assets/l10n/zh-CN.arb": "ff71af89e735e1a672f0785342ddc948",
"assets/assets/l10n/vi.arb": "722d8266ca1ae045a263e2d331728bae",
"assets/assets/l10n/tr.arb": "5aa4d5652f5228ed04159954afa1ace2",
"assets/assets/l10n/ms.arb": "8d908d9ab343626574aec9b3e3933a88",
"assets/assets/l10n/zh-TW.arb": "984306a240b598ebae1bad8da5e0df31",
"assets/assets/l10n/fr.arb": "f691750f3bc0546d7da353b3f28244b6",
"assets/assets/l10n/sr.arb": "3f8a9717d767f3df2ac6a3e82c166a2a",
"assets/assets/l10n/ru.arb": "55e5d852849d99cbae2e0ff1e7b522aa",
"assets/assets/l10n/ja.arb": "b60aca84c7c371a31bd06d9cdef26dfb",
"assets/assets/l10n/en.arb": "beec31c21a770d494380c53f641bf020",
"assets/assets/l10n/el.arb": "c589aca9c26346af8fea6050254b0176",
"assets/assets/l10n/sv.arb": "d385f5fe757a28a98580e7307e9dd445",
"assets/assets/l10n/pl.arb": "5175ae670b25ef20d0cecef681d52380",
"assets/assets/palettes.json": "4db4fee93fb28809e3c29e6dd5c9979e",
"assets/assets/visited_logo.svg": "a3636179fbdca409109c2ebdda3ccf93",
"assets/assets/VisitedIcon.svg": "431eaa2da6178c13945e6c6b140dbd6e",
"assets/assets/geometry.bin": "dda32f2ae094305c4714cff071a973ce",
"assets/assets/template.html": "4d4da25121930a00817808f8615b207a",
"assets/assets/disputed.bin": "ad7d37c1f611f29ca893816e31dde4bb",
"canvaskit/skwasm_st.js": "d1326ceef381ad382ab492ba5d96f04d",
"canvaskit/skwasm.js": "f2ad9363618c5f62e813740099a80e63",
"canvaskit/skwasm.js.symbols": "80806576fa1056b43dd6d0b445b4b6f7",
"canvaskit/canvaskit.js.symbols": "68eb703b9a609baef8ee0e413b442f33",
"canvaskit/skwasm.wasm": "f0dfd99007f989368db17c9abeed5a49",
"canvaskit/chromium/canvaskit.js.symbols": "5a23598a2a8efd18ec3b60de5d28af8f",
"canvaskit/chromium/canvaskit.js": "34beda9f39eb7d992d46125ca868dc61",
"canvaskit/chromium/canvaskit.wasm": "64a386c87532ae52ae041d18a32a3635",
"canvaskit/skwasm_st.js.symbols": "c7e7aac7cd8b612defd62b43e3050bdd",
"canvaskit/canvaskit.js": "86e461cf471c1640fd2b461ece4589df",
"canvaskit/canvaskit.wasm": "efeeba7dcc952dae57870d4df3111fad",
"canvaskit/skwasm_st.wasm": "56c3973560dfcbf28ce47cebe40f3206"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"flutter_bootstrap.js",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
