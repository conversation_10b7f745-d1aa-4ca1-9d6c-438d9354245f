spring:
  config:
    activate:
      on-profile: dev
---

# config context path to "/" by setting an empty string
server:
  contextPath:
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain

spring:
# JPA
  jpa:
    properties:
      hibernate:
        show_sql: true
        use_sql_comments: true
        format_sql: true
        hbm2ddl.auto: create-drop
    hibernate:
      ddl-auto: create-drop
#HSQLDB - In-memory database used only for testing the app
  h2:
    console:
      enabled: true
# JACKSON
  jackson:
    serialization:
      INDENT_OUTPUT: true
      WRITE_DATES_AS_TIMESTAMPS: false

jwt:
  header: Authorization
  secret: my$3cr37w0rd
  expiration: 1800
#  route:
#    authentication:
#      path: auth
#      refresh: refresh

iap:
  secret: willNotWork
  sandbox: true
