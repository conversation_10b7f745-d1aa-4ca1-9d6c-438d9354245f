INSERT INTO users (ID, USERNAME, PASS<PERSON>OR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, last_password_reset_date) VALUES (nextval('users_id_seq'), '<EMAIL>', '$2a$06$kThj8/U0Z5YP3vSvlxawPOPnA3Pn4ab0nrF0.xG8UfjoX5YbxTOV6', TRUE , '2017-01-01 00:00:00.0');
INSERT INTO users (ID, USERNAME, PASSWORD, ENABLED, last_password_reset_date) VALUES (nextval('users_id_seq'), '<EMAIL>', '$2a$06$ZoaL5o.tT8dr5vrzNkM9kO8Ga7LxCb1xQUBybNh3LEipzTReslONC', TRUE,  '2017-01-01 00:00:00.0');

INSERT INTO AUTHORITIES (ID, NAME) VALUES (nextval('authorities_id_seq'), 'ROLE_USER');
INSERT INTO AUTHORITIES (ID, NAME) VALUES (nextval('authorities_id_seq'), 'ROLE_ADMIN');

INSERT INTO USERS_AUTHORITIES (USER_ID, AUTHORITY_ID) VALUES (1, 1);
INSERT INTO USERS_AUTHORITIES (USER_ID, AUTHORITY_ID) VALUES (1, 2);
INSERT INTO USERS_AUTHORITIES (USER_ID, AUTHORITY_ID) VALUES (2, 1);

INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'North America');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AN', 'Netherlands Antilles', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'JM', 'Jamaica', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BB', 'Barbados', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CW', 'Curaçao', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NI', 'Nicaragua', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BZ', 'Belize', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MP', 'Northern Mariana Islands', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LC', 'Saint Lucia', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US', 'United States of America', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-CO', 'Colorado', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-WV', 'West Virginia', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-TX', 'Texas', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-PA', 'Pennsylvania', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-AL', 'Alabama', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-CA', 'California', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-VA', 'Virginia', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-RI', 'Rhode Island', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MI', 'Michigan', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MN', 'Minnesota', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-DC', 'District of Columbia', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-KS', 'Kansas', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-TN', 'Tennessee', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NE', 'Nebraska', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-OK', 'Oklahoma', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-OH', 'Ohio', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MS', 'Mississippi', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-AK', 'Alaska', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-SD', 'South Dakota', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MA', 'Massachusetts', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-DE', 'Delaware', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-HI', 'Hawaii', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-IA', 'Iowa', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-IL', 'Illinois', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-AZ', 'Arizona', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NJ', 'New Jersey', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-GA', 'Georgia', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-WY', 'Wyoming', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MO', 'Missouri', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-ME', 'Maine', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-ND', 'North Dakota', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MT', 'Montana', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NV', 'Nevada', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-WA', 'Washington', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NM', 'New Mexico', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-WI', 'Wisconsin', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NC', 'North Carolina', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-UT', 'Utah', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NH', 'New Hampshire', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-OR', 'Oregon', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-AR', 'Arkansas', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-FL', 'Florida', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-ID', 'Idaho', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-KY', 'Kentucky', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-VT', 'Vermont', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-MD', 'Maryland', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-SC', 'South Carolina', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-CT', 'Connecticut', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-IN', 'Indiana', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-NY', 'New York', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'US-LA', 'Louisiana', 'STATE', 9, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AI', 'Anguilla', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PR', 'Puerto Rico', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AW', 'Aruba', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KY', 'Cayman Islands', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SX', 'Sint Maarten', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TT', 'Trinidad and Tobago', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VG', 'British Virgin Islands', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GU', 'Guam', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PM', 'Saint Pierre and Miquelon', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MQ', 'Martinique', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BS', 'The Bahamas', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MX', 'Mexico', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DM', 'Dominica', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CR', 'Costa Rica', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KN', 'Saint Kitts and Nevis', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MF', 'Saint Martin', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MS', 'Montserrat', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SV', 'El Salvador', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'HT', 'Haiti', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AG', 'Antigua and Barbuda', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BM', 'Bermuda', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA', 'Canada', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-MB', 'Manitoba', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-NU', 'Nunavut', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-SK', 'Saskatchewan', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-NB', 'New Brunswick', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-NL', 'Newfoundland and Labrador', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-YT', 'Yukon', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-AB', 'Alberta', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-BC', 'British Columbia', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-ON', 'Ontario', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-QC', 'Quebec', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-NT', 'Northwest Territories', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-NS', 'Nova Scotia', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CA-PE', 'Prince Edward Island', 'STATE', 82, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GD', 'Grenada', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DO', 'Dominican Republic', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'HN', 'Honduras', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TC', 'Turks and Caicos Islands', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VC', 'Saint Vincent and the Grenadines', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CU', 'Cuba', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VI', 'United States Virgin Islands', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GT', 'Guatemala', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BL', 'Saint Barthelemy', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PA', 'Panama', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GP', 'Guadeloupe', 'COUNTRY', NULL, 1, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'Asia');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SY', 'Syria', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'JP', 'Japan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KR', 'South Korea', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CN', 'China', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BD', 'Bangladesh', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KH', 'Cambodia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KZ', 'Kazakhstan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ID', 'Indonesia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IQ', 'Iraq', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MN', 'Mongolia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TH', 'Thailand', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KW', 'Kuwait', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PW', 'Palau', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AE', 'United Arab Emirates', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'UZ', 'Uzbekistan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TW', 'Taiwan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'YE', 'Yemen', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TJ', 'Tajikistan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BT', 'Bhutan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TL', 'Timor', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'OM', 'Oman', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VN', 'Vietnam', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PH', 'Philippines', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KP', 'North Korea', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IR', 'Iran', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TR', 'Turkey', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AF', 'Afghanistan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IL', 'Israel', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BN', 'Brunei', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LK', 'Sri Lanka', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SA', 'Saudi Arabia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LB', 'Lebanon', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MY', 'Malaysia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MM', 'Myanmar', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PS', 'Palestine', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NP', 'Nepal', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BH', 'Bahrain', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TM', 'Turkmenistan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KG', 'Kyrgyzstan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MV', 'Maldives', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LA', 'Laos', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'QA', 'Qatar', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'HK', 'Hong Kong S.A.R.', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FM', 'Micronesia', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SG', 'Singapore', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'JO', 'Jordan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MO', 'Macau S.A.R', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IN', 'India', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PK', 'Pakistan', 'COUNTRY', NULL, 2, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'South America');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GF', 'French Guiana', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PE', 'Peru', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PY', 'Paraguay', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AR', 'Argentina', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BO', 'Bolivia', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'UY', 'Uruguay', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CO', 'Colombia', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VE', 'Venezuela', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FK', 'Falkland Islands', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CL', 'Chile', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SR', 'Suriname', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'EC', 'Ecuador', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GY', 'Guyana', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BR', 'Brazil', 'COUNTRY', NULL, 3, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'Europe');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AX', 'Aland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LT', 'Lithuania', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MC', 'Monaco', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'JE', 'Jersey', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BE', 'Belgium', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SM', 'San Marino', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PL', 'Poland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AM', 'Armenia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GB', 'United Kingdom', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FI', 'Finland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AT', 'Austria', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MT', 'Malta', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'HR', 'Croatia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NL', 'Netherlands', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FR', 'France', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'UA', 'Ukraine', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DE', 'Germany', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'RO', 'Romania', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ES', 'Spain', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AD', 'Andorra', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NO', 'Norway', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PT', 'Portugal', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LI', 'Liechtenstein', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GL', 'Greenland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VA', 'Vatican', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MK', 'Macedonia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CH', 'Switzerland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GR', 'Greece', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BG', 'Bulgaria', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IT', 'Italy', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'RS', 'Serbia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GE', 'Georgia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BA', 'Bosnia and Herzegovina', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AL', 'Albania', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'XK', 'Kosovo', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'EE', 'Estonia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IS', 'Iceland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SE', 'Sweden', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CY', 'Cyprus', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SI', 'Slovenia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ME', 'Montenegro', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LV', 'Latvia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BY', 'Belarus', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GI', 'Gibraltar', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'HU', 'Hungary', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IE', 'Ireland', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LU', 'Luxembourg', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'RU', 'Russia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AZ', 'Azerbaijan', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CZ', 'Czech Republic', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DK', 'Denmark', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IM', 'Isle of Man', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GG', 'Guernsey', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SK', 'Slovakia', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MD', 'Moldova', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FO', 'Faroe Islands', 'COUNTRY', NULL, 4, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'Antarctica');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TF', 'French Southern and Antarctic Lands', 'COUNTRY', NULL, 5, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AQ', 'Antarctica', 'COUNTRY', NULL, 5, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'Oceania');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CK', 'Cook Islands', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NU', 'Niue', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'FJ', 'Fiji', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PF', 'French Polynesia', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'WF', 'Wallis and Futuna', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PN', 'Pitcairn Islands', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NF', 'Norfolk Island', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TK', 'Tokelau', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CX', 'Christmas Island', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NC', 'New Caledonia', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'PG', 'Papua New Guinea', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KI', 'Kiribati', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TV', 'Tuvalu', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NZ', 'New Zealand', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'WS', 'Samoa', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NR', 'Nauru', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SB', 'Solomon Islands', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CC', 'Cocos (Keeling) Islands', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MH', 'Marshall Islands', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU', 'Australia', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-JB', 'Jervis Bay Territory', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-ACT', 'Australian Capital Territory', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-SA', 'South Australia', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-VIC', 'Victoria', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-NSW', 'New South Wales', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-WA', 'Western Australia', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-QLD', 'Queensland', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-NT', 'Northern Territory', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AU-TAS', 'Tasmania', 'STATE', 247, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AS', 'American Samoa', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'VU', 'Vanuatu', 'COUNTRY', NULL, 6, '2017-01-01 00:00:00.0');
INSERT INTO regions (id, name) VALUES (nextval('regions_id_seq'), 'Africa');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SOL', 'Somaliland', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SD', 'Sudan', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TN', 'Tunisia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'EH', 'Western Sahara', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CG', 'Congo', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'EG', 'Egypt', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GW', 'Guinea Bissau', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SL', 'Sierra Leone', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ZM', 'Zambia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ZW', 'Zimbabwe', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GQ', 'Equatorial Guinea', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GN', 'Guinea', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TZ', 'Tanzania', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TD', 'Chad', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GM', 'Gambia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'RE', 'Reunion', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TG', 'Togo', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NG', 'Nigeria', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BW', 'Botswana', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GA', 'Gabon', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DJ', 'Djibouti', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KM', 'Comoros', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ST', 'Sao Tome and Principe', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NA', 'Namibia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CI', 'Cote d''Ivoire', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LS', 'Lesotho', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CD', 'Democratic Republic of the Congo', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SO', 'Somalia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MU', 'Mauritius', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BI', 'Burundi', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MW', 'Malawi', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'NE', 'Niger', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LR', 'Liberia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'LY', 'Libya', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'IO', 'British Indian Ocean Territory', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'RW', 'Rwanda', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'UG', 'Uganda', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SH', 'Saint Helena', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SZ', 'Swaziland', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'YT', 'Mayotte', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KE', 'Kenya', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MZ', 'Mozambique', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SN', 'Senegal', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BF', 'Burkina Faso', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MA', 'Morocco', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CF', 'Central African Republic', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ZA', 'South Africa', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SS', 'South Sudan', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ER', 'Eritrea', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ET', 'Ethiopia', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GH', 'Ghana', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CM', 'Cameroon', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'DZ', 'Algeria', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'ML', 'Mali', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'BJ', 'Benin', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'AO', 'Angola', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SC', 'Seychelles', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MG', 'Madagascar', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'TO', 'Tonga', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'MR', 'Mauritania', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'CV', 'Cape Verde', 'COUNTRY', NULL, 7, '2017-01-01 00:00:00.0');

INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GB-ENG', 'England', 'STATE', 178, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GB-NIR', 'Northern Ireland', 'STATE', 178, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GB-SCT', 'Scotland', 'STATE', 178, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'GB-WLS', 'Wales', 'STATE', 178, NULL, '2017-01-01 00:00:00.0');

-- some cities for testing
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KDRO', 'Durango', 'CITY', 10, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'KJFK', 'New York', 'CITY', 59, NULL, '2017-01-01 00:00:00.0');
INSERT INTO geoareas (id, iso_key, name, geo_area_type,parent_id, region_id, last_modification_time) VALUES (nextval('geoareas_id_seq'), 'SBFL', 'Florianópolis', 'CITY', 169, NULL, '2017-01-01 00:00:00.0');

insert into ranking (position, num_areas_visited, num_users) VALUES (1, 0, 2);

insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AN'), '17.49069404602051', '-62.967643737793', 6);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'JM'), '18.1185', '-77.2755', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BB'), '13.14000000000001', '-59.616667', 6);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CW'), '12.116667', '-68.933333', 6);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NI'), '12.8715', '-85.41400000000002', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BZ'), '17.2', '-88.5', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MP'), '16.35599999999999', '145.6755000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LC'), '13.9055', '-60.98000000000002', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US'), '39.94755', '-100.038', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-CO'), '38.99804627131091', '-105.5466527982642', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-WV'), '38.3581415878796', '-80.93590287954288', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-TX'), '31.16955770519897', '-100.059985114323', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-PA'), '41.1330493427687', '-77.59897884795711', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-AL'), '33.63693583834274', '-86.67418718132677', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-CA'), '37.16740255385615', '-119.2819646847887', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-VA'), '37.49146987915039', '-78.29068042755127', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-RI'), '41.67219828955077', '-71.48710384793269', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MI'), '44.26032908790401', '-84.58413408073926', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MN'), '46.44473643654257', '-94.25891400243302', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-DC'), '38.88585673680526', '-77.01556484648997', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-KS'), '38.49666860610706', '-98.32749396441376', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-TN'), '35.81670298923438', '-85.9643080309333', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NE'), '41.50047750504488', '-99.68772101520045', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-OK'), '35.31794790296935', '-97.21669501422001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-OH'), '40.3701718206328', '-82.6699742296506', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MS'), '32.49067251233355', '-89.8600263130127', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-AK'), '65.5193053249433', '-150.0672206289118', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-SD'), '44.253461920746', '-100.2586939155934', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MA'), '42.30966085465696', '-71.90136786567437', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-DE'), '38.52388', '-75.59013', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-HI'), '20.79245000000007', '-156.353105', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-IA'), '41.94418427181096', '-93.38614183223252', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-IL'), '39.72882853856642', '-89.27847266310802', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-AZ'), '34.17203593600516', '-111.9292081126904', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NJ'), '40.14739472102252', '-74.73353359648083', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-GA'), '32.66894938814753', '-83.24519751659945', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-WY'), '43.02950562191297', '-107.565160081801', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MO'), '38.31859162042927', '-92.44977779821187', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-ME'), '45.26390412045711', '-69.02474000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-ND'), '47.48112020526411', '-100.3158351306001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MT'), '46.68286143654347', '-110.0374251981846', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NV'), '38.52656342218792', '-117.0118319333148', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-WA'), '47.29644155536104', '-120.7968689813489', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NM'), '34.17090577926036', '-106.023419562589', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-WI'), '44.90089010271473', '-89.58221381301158', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NC'), '35.66349630384794', '-78.38414089630665', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-UT'), '39.49784780532982', '-111.5397236804496', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NH'), '44.01166526090706', '-71.59161943224292', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-OR'), '44.13783728798643', '-120.4953177899417', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-AR'), '34.7752936874724', '-92.14576534703878', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-FL'), '27.66', '-81.85', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-ID'), '43.49674520525617', '-114.1220595976149', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-KY'), '37.26186605483477', '-85.45544603142068', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-VT'), '43.87769735368716', '-72.75659186470094', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-MD'), '39.35', '-77.058', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-SC'), '33.75663117119175', '-80.66018256297706', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-CT'), '41.50387640785181', '-72.74281531440903', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-IN'), '39.77222382258354', '-86.42548143181405', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-NY'), '42.81756765397592', '-75.85904220977214', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'US-LA'), '31.08065241176657', '-91.63871352313257', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AI'), '18.2205', '-63.06999999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PR'), '18.2', '-66.4335', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AW'), '12.376667', '-70.013333', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KY'), '19.7315', '-79.82400000000003', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SX'), '17.14400000000001', '-64.068', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TT'), '10.4525', '-61.41200000000001', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VG'), '18.6875', '-64.38449999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GU'), '13.44', '144.795', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PM'), '46.947', '-56.32100000000001', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MQ'), '14.6398605', '-61.017785', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BS'), '24.125', '-73.34949999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MX'), '23.63000000000001', '-101.95', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DM'), '15.43000000000001', '-61.36599999999999', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CR'), '10.229999999999995', '-84.236', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KN'), '17.3135', '-62.7355', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MF'), '17.14400000000001', '-64.068', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MS'), '16.74550000000001', '-62.18549999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SV'), '13.7975', '-88.9105', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'HT'), '19.04000000000001', '-72.3', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AG'), '17.6315', '-61.80800000000001', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BM'), '32.32350000000001', '-64.76549999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA'), '54.5', '-100.34049999999999', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-MB'), '54.49676637628129', '-97.47507184877213', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NU'), '65.5051873284458', '-97.0149036412156', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NU'), '69.52', '-73.43', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NU'), '81.04', '-76.31', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-SK'), '54.49708497980907', '-105.6836479828273', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NB'), '46.5671200776955', '-66.44278680573143', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NL'), '54.11', '-62.086', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NL'), '48.94', '-56.36', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-YT'), '64.82593669218289', '-137.4106469882212', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-AB'), '54.49708497980907', '-115.0002248763124', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-BC'), '53.96647827011493', '-124.65', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-ON'), '50.6834901630289', '-86.23890791421077', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-QC'), '53.84', '-72.54', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NT'), '65.28746136799178', '-119.2160361943594', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-NS'), '45.05', '-62.99', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CA-PE'), '46.51423189208427', '-63.21343792765877', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GD'), '12.12249999999999', '-61.69449999999999', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DO'), '18.9', '-70.5395', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'HN'), '15', '-85.89750000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TC'), '21.80199999999999', '-71.74249999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VC'), '13.2505', '-61.20049999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CU'), '22.2135', '-79.94200000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VI'), '18.35299999999999', '-64.9345', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GT'), '15.7765', '-90.2315', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BL'), '17.89849999999998', '-62.83749999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PA'), '9.25', '-78.90200000000002', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GP'), '16.15', '-61.46', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SY'), '34.807', '38.0615', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'JP'), '36.728', '139.4115', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KR'), '36.331', '127.811', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CN'), '34.87049999999999', '104.3505', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BD'), '24.2825', '90.08750000000003', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KH'), '12.55799999999999', '104.9625', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KZ'), '47.9995', '66.96599999999998', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ID'), '-7.323999999999998', '110.5355', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IQ'), '33.24445900000007', '43.67012800000001', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MN'), '46.85649999999999', '103.8205', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TH'), '15.736999999999995', '102.0285', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KW'), '29.79049999999999', '48.21449999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PW'), '3.041499999999999', '131.1615', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AE'), '24.3445', '53.97799999999998', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'UZ'), '41.36349999999999', '64.55649999999997', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TW'), '23.60099999999998', '121.0005', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'YE'), '15.2175', '46.81350000000003', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TJ'), '38.85949999999999', '71.23449999999997', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BT'), '27.5065', '90.411', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TL'), '-8.914000000000001', '126.1055000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'OM'), '20.4425', '56.79599999999996', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VN'), '20.80500000000001', '104.9875', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PH'), '11.5509', '123.1702', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KP'), '39.553', '126.556', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IR'), '32.4890935', '53.68804550000004', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TR'), '38.947', '35.45649999999998', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AF'), '33.924', '65.6885', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IL'), '32.0214', '34.8706', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BN'), '4.52300000000001', '114.556', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LK'), '7.881', '80.79250000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SA'), '24.24799999999999', '45.1285', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LB'), '33.8775', '35.84700000000004', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MY'), '4.007999999999996', '103.2035', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MY'), '2.877999999999996', '113.0135', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MM'), '21.276', '96.6635', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PS'), '31.43', '34.367', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NP'), '28.374', '84.10700000000003', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BH'), '26.0465', '50.537', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TM'), '38.97449999999999', '59.56150000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KG'), '41.224', '74.73749999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MV'), '3.259500000000017', '73.4125', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LA'), '20.208', '102.884', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'QA'), '25.35899999999999', '51.18199999999996', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'HK'), '22.2715', '113.9415', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FM'), '9.506', '138.138', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SG'), '1.355999999999995', '103.823', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'JO'), '30.58100000000001', '36.41800000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MO'), '22.221', '113.5135', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IN'), '22.78700000000001', '78.75450000000001', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PK'), '28.395', '67.946', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GF'), '3.9702755', '-53.1439235', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PE'), '-9.194000000000003', '-75.96100000000001', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PY'), '-23.42000000000002', '-58.44650000000001', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AR'), '-37.08', '-63.62250000000002', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BO'), '-16.30099999999997', '-63.571', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'UY'), '-32.517', '-55.78200000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CO'), '4.09899999999999', '-72.95049999999999', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VE'), '7.082999999999993', '-65.7075', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FK'), '-52.04900000000001', '-58.48699999999998', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CL'), '-35.718', '-71.31450000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SR'), '3.91749999999999', '-56.02250000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'EC'), '-1.002499999999995', '-78.946', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GY'), '4.875', '-58.937', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BR'), '-9.26199999999999', '-54.35849999999998', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AX'), '60.3075', '19.83449999999995', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LT'), '55.152', '23.91099999999997', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MC'), '43.75149999999999', '7.408499999999975', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'JE'), '49.218', '-2.12299999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BE'), '50.801', '4.445000000000022', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PL'), '51.9295', '19.11750000000004', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AM'), '40.63638114929199', '44.54305267333984', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GB'), '54.3355', '-2.1935', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GB-WLS'), '52.36582', '-3.62395', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GB-SCT'), '57.064630273278546', '-4.28466796875', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GB-NIR'), '54.681862461761774', '-6.657485514879227', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GB-ENG'), '53.04359516673006', '-1.338145165013174', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FI'), '64.8419999999999', '26.97749999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AT'), '47.7005', '14.3355', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MT'), '36.044', '14.25050000000005', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'HR'), '44.503', '14.99099999999996', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NL'), '52.6865', '5.887', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FR'), '46.7185', '1.688999999999993', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'UA'), '49.264', '31.8295', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DE'), '51.09100000000001', '10.43750000000003', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RO'), '45.96699999999999', '24.97399999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ES'), '39.89550000000001', '-2.964499999999987', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AD'), '42.5385', '1.577499999999986', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NO'), '60.4105', '8.99450000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PT'), '39.571', '-7.846499999999992', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LI'), '47.164', '9.544999999999987', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GL'), '72.841', '-38.75300000000001', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MK'), '41.60400000000001', '21.72750000000002', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CH'), '46.803', '8.212500000000006', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GR'), '39.7135', '21.63750000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BG'), '42.74100000000001', '25.46449999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IT'), '42.78549999999999', '12.27100000000002', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VA'), '41.902', '12.43299999999996', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SM'), '43.94199999999999', '12.45599999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RS'), '44.2055', '20.90800000000002', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GE'), '42.32000000000001', '43.32550000000003', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BA'), '43.91850000000001', '17.66049999999998', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AL'), '41.1505', '20.15600000000003', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'XK'), '42.5195', '21.1718', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'EE'), '58.58200000000001', '25.79149999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IS'), '64.9665', '-19.01599999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SE'), '63.577', '16.107', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CY'), '34.87649999999999', '33.1755', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SI'), '46.1455', '14.947', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ME'), '42.93', '19.062', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LV'), '56.8655', '24.60849999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BY'), '53.7055', '27.94250000000005', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GI'), '36.13774100000006', '-5.345154500000007', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'HU'), '47.153', '19.48500000000004', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IE'), '53.41249656677246', '-8.236945152282715', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LU'), '49.806', '6.109499999999969', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RU'), '60.78649999999999', '84.154', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RU'), '56.49649999999999', '43.17', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RU'), '66.6265', '-176.17', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AZ'), '40.195488', '47.579', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CZ'), '49.807', '15.46100000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DK'), '56.2715', '9.523500000000013', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IM'), '54.233', '-4.561499999999995', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GG'), '49.468', '-2.579000000000008', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SK'), '48.6805', '19.70099999999996', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MD'), '46.964', '28.37500000000003', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FO'), '61.774845', '-6.8411965', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TF'), '-49.18299999999999', '69.66249999999994', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AQ'), '-80.707', '19', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CK'), '-21.21800000000002', '-159.7895', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NU'), '-19.05200000000001', '-169.8705', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'FJ'), '-20.658', '-178.7205', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PF'), '-17.5205', '-149.847', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'WF'), '-14.27850000000002', '-178.119', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'WF'), '-13.28149999999998', '-176.1615', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PN'), '-24.36800000000002', '-128.32', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NF'), '-29.05499999999999', '167.948', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TK'), '-9.18777999999999', '-171.851807', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TK'), '-9.362224999999995', '-171.2197115', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TK'), '-8.57055400000003', '-172.4902955', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CX'), '-10.4663', '105.72509', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NC'), '-21.259', '165.5205', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'PG'), '-5.652000000000012', '144.036', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '1.760999999999996', '173.019', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-4.676000000000002', '-174.521', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-2.808499999999995', '-171.678', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '1.883500000000012', '172.9784999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-4.50800000000001', '-172.2045', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-1.198499999999996', '174.748', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '1.363', '173.0915', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '1.880499999999998', '-157.3775', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-4.454999999999984', '-171.247', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-0.8614999999999924', '169.539', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '3.043499999999995', '172.819', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-4.062500000000014', '-154.979', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-0.7104999999999961', '174.445', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '3.122500000000002', '172.9285', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-3.129000000000019', '-171.089', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '0.9299999999999926', '173.0285', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '3.860500000000002', '-159.334', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-5.620000000000005', '-155.8955', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KI'), '-11.42450000000001', '-151.801', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-8.50829499999999', '179.217397', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-5.661528500000003', '176.073868', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-8.003333999999995', '178.3972780000001', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-7.181110000000004', '177.1505425', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-8.045140000000004', '178.362747', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-6.101666999999992', '177.2951200000001', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-7.473330000000004', '178.694', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-5.684164999999993', '176.1345520000001', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TV'), '-6.271529999999984', '176.30275', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NZ'), '-42.6885', '171.0785', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'WS'), '-13.63450000000002', '-172.4775', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NR'), '-0.5200000000000102', '166.9325', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SB'), '-8.96250000000002', '160.984', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CC'), '-12.16221999999999', '96.84110250000003', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CC'), '-12.17596999999999', '96.90859150000006', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MH'), '5.906999999999982', '169.663', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MH'), '7.010500000000008', '171.667', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MH'), '7.120499999999993', '171.215', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MH'), '11.1575', '166.872', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MH'), '7.314999999999998', '168.7525000000001', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU'), '-25.4295', '133.719', 0);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-JB'), '-35.15483311299174', '150.666988932404', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-ACT'), '-35.53025793200057', '149.0869241748636', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-SA'), '-30.033', '134.996', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-VIC'), '-37.57536539374324', '145.4544154913726', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-NSW'), '-32.82249049472303', '147.3095339703429', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-WA'), '-24.42099237149701', '121.0933195913674', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-QLD'), '-23.327716', '144.090933', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-NT'), '-18.58987764710444', '133.5021909143377', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AU-TAS'), '-42.14581848709861', '146.4942495154142', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AS'), '-14.3085', '-170.6945', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'VU'), '-16.2255', '167.494', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SOL'), '9.74', '45.893', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SD'), '15.434', '30.21699999999998', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TN'), '33.78450000000001', '9.51600000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'EH'), '23.166664', '-13.287542', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CG'), '-0.985000000000036', '15.876', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'EG'), '26.82500000000001', '30.78700000000006', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GW'), '12.27299999999999', '-15.55099999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SL'), '8.451999999999998', '-11.78799999999998', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ZM'), '-15.11', '25.832', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ZW'), '-19.02249999999999', '29.11549999999997', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GQ'), '1.487500000000011', '10.692499999999995', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GN'), '10.86', '-11.328', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TZ'), '-6.355499999999992', '34.89350000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TD'), '15.45999999999998', '18.71549999999999', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GM'), '13.587', '-15.117', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RE'), '-21.185975', '55.46485900000002', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TG'), '8.602500000000006', '0.8439999999999657', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NG'), '10.45750000000001', '8.23399999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BW'), '-22.32100000000001', '24.67100000000002', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GA'), '-0.806999999999988', '11.59200000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DJ'), '11.825', '42.58750000000003', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KM'), '-12.30749999999999', '43.74499999999998', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ST'), '0.5455000000000109', '6.439000000000009', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NA'), '-22.95350000000002', '16.49050000000003', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CI'), '7.544031000000032', '-5.547081999999961', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CD'), '-3.560999999999998', '23.09399999999997', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SO'), '5.144499999999994', '46.17699999999999', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MU'), '-20.332', '57.627', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BI'), '-3.384499999999989', '29.91249999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MW'), '-13.369', '34.033', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'NE'), '17.607', '8.063499999999976', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LR'), '6.444499999999991', '-9.454000000000008', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LY'), '26.3395', '17.23000000000002', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'IO'), '-7.327500000000001', '72.42449999999997', 6);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'RW'), '-1.935999999999993', '29.86749999999998', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'UG'), '1.375', '32.27000000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SH'), '-15.95500000000001', '-5.72150000000002', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SH'), '-7.92949999999999', '-14.35900000000001', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SZ'), '-26.52650000000001', '31.45050000000001', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'YT'), '-12.8431', '45.1383', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'KE'), '-0.102000000000004', '38.04799999999997', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MZ'), '-18.66299999999998', '35.53349999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SN'), '14.5035', '-14.45899999999997', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BF'), '12.25149999999999', '-1.567499999999967', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MA'), '28.6755', '-9.03449999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CF'), '6.632999999999996', '20.91699999999997', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ZA'), '-30.89349999999999', '23.739', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'LS'), '-29.61199999999999', '28.22149999999999', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SS'), '7.878588878574988', '29.59249334954691', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ER'), '15.191', '39.77199999999996', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ET'), '8.78400000000001', '38.88850000000002', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'GH'), '7.964500000000015', '-1.028500000000008', 3);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CM'), '3.377499999999998', '12.35799999999998', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'DZ'), '28.0395', '1.642500000000013', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'ML'), '17.56949999999999', '-4.022999999999968', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'BJ'), '9.3005', '2.298500000000018', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'AO'), '-11.93749999999999', '17.89500000000001', 2);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'SC'), '-4.671999999999997', '55.46299999999999', 5);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MG'), '-18.82550000000001', '46.87000000000003', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'TO'), '-21.37549999999999', '-174.943', 4);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'MR'), '19.7375', '-10.41049999999998', 1);
insert into geoarea_labels (id, geo_area_id, latitude, longitude, resolution) values (nextval('geoarea_labels_id_seq'), (select id from geoareas where iso_key = 'CV'), '15.7559', '-23.872', 3);


update geoareas set bounding_min_long = '-69.16361999511719', bounding_min_lat = '17.52055549621582', bounding_max_long = '-62.93861389160156', bounding_max_lat = '12.02055549621582' where iso_key = 'AN';
update geoareas set bounding_min_long = '-78.339502', bounding_min_lat = '18.522229', bounding_max_long = '-76.210795', bounding_max_lat = '17.71494' where iso_key = 'JM';
update geoareas set bounding_min_long = '-59.64669', bounding_min_lat = '13.317689', bounding_max_long = '-59.427633', bounding_max_lat = '13.0622' where iso_key = 'BB';
update geoareas set bounding_min_long = '-69.158874', bounding_min_lat = '12.380278', bounding_max_long = '-68.751095', bounding_max_lat = '12.045467' where iso_key = 'CW';
update geoareas set bounding_min_long = '-87.670173', bounding_min_lat = '15.008051', bounding_max_long = '-83.157528', bounding_max_lat = '10.735366' where iso_key = 'NI';
update geoareas set bounding_min_long = '-89.237493', bounding_min_lat = '18.482309', bounding_max_long = '-87.788615', bounding_max_lat = '15.888695' where iso_key = 'BZ';
update geoareas set bounding_min_long = '145.152102', bounding_min_lat = '18.806785', bounding_max_long = '145.835471', bounding_max_lat = '14.111335' where iso_key = 'MP';
update geoareas set bounding_min_long = '-61.073114', bounding_min_lat = '14.093352', bounding_max_long = '-60.886769', bounding_max_lat = '13.717561' where iso_key = 'LC';
update geoareas set bounding_min_long = '-178.194518', bounding_min_lat = '71.407683', bounding_max_long = '179.779962', bounding_max_lat = '18.963907' where iso_key = 'US';
update geoareas set bounding_min_long = '-109.058934', bounding_min_lat = '41.003906', bounding_max_long = '-102.042974', bounding_max_lat = '36.994786' where iso_key = 'US-CO';
update geoareas set bounding_min_long = '-82.621743', bounding_min_lat = '40.636951', bounding_max_long = '-77.719881', bounding_max_lat = '37.20291' where iso_key = 'US-WV';
update geoareas set bounding_min_long = '-106.643603', bounding_min_lat = '36.501861', bounding_max_long = '-93.526331', bounding_max_lat = '25.887551' where iso_key = 'US-TX';
update geoareas set bounding_min_long = '-80.518598', bounding_min_lat = '42.269079', bounding_max_long = '-74.69661', bounding_max_lat = '39.722302' where iso_key = 'US-PA';
update geoareas set bounding_min_long = '-88.471115', bounding_min_lat = '35.00118', bounding_max_long = '-84.889196', bounding_max_lat = '30.247195' where iso_key = 'US-AL';
update geoareas set bounding_min_long = '-124.410798', bounding_min_lat = '42.011663', bounding_max_long = '-114.136058', bounding_max_lat = '32.536556' where iso_key = 'US-CA';
update geoareas set bounding_min_long = '-83.673316', bounding_min_lat = '39.464886', bounding_max_long = '-75.244304', bounding_max_lat = '36.5402' where iso_key = 'US-VA';
update geoareas set bounding_min_long = '-71.859555', bounding_min_lat = '42.01714', bounding_max_long = '-71.120168', bounding_max_lat = '41.321569' where iso_key = 'US-RI';
update geoareas set bounding_min_long = '-90.415429', bounding_min_lat = '48.173221', bounding_max_long = '-82.413619', bounding_max_lat = '41.694001' where iso_key = 'US-MI';
update geoareas set bounding_min_long = '-97.228743', bounding_min_lat = '49.383625', bounding_max_long = '-89.615796', bounding_max_lat = '43.501391' where iso_key = 'US-MN';
update geoareas set bounding_min_long = '-77.117418', bounding_min_lat = '38.993869', bounding_max_long = '-76.909294', bounding_max_lat = '38.791222' where iso_key = 'US-DC';
update geoareas set bounding_min_long = '-102.053927', bounding_min_lat = '40.001626', bounding_max_long = '-94.610765', bounding_max_lat = '36.994786' where iso_key = 'US-KS';
update geoareas set bounding_min_long = '-90.311367', bounding_min_lat = '36.677123', bounding_max_long = '-81.679709', bounding_max_lat = '34.984749' where iso_key = 'US-TN';
update geoareas set bounding_min_long = '-104.053011', bounding_min_lat = '43.002989', bounding_max_long = '-95.306337', bounding_max_lat = '40.001626' where iso_key = 'US-NE';
update geoareas set bounding_min_long = '-103.001438', bounding_min_lat = '37.000263', bounding_max_long = '-94.430026', bounding_max_lat = '33.637421' where iso_key = 'US-OK';
update geoareas set bounding_min_long = '-84.817996', bounding_min_lat = '41.978802', bounding_max_long = '-80.518598', bounding_max_lat = '38.424267' where iso_key = 'US-OH';
update geoareas set bounding_min_long = '-91.636787', bounding_min_lat = '34.995703', bounding_max_long = '-88.098683', bounding_max_lat = '30.181472' where iso_key = 'US-MS';
update geoareas set bounding_min_long = '-178.123152', bounding_min_lat = '71.351633', bounding_max_long = '173.304726', bounding_max_lat = '51.61274' where iso_key = 'US-AK';
update geoareas set bounding_min_long = '-104.058488', bounding_min_lat = '45.944106', bounding_max_long = '-96.434587', bounding_max_lat = '42.488157' where iso_key = 'US-SD';
update geoareas set bounding_min_long = '-73.508114', bounding_min_lat = '42.887974', bounding_max_long = '-69.937149', bounding_max_lat = '41.496831' where iso_key = 'US-MA';
update geoareas set bounding_min_long = '-75.786521', bounding_min_lat = '39.831841', bounding_max_long = '-75.047134', bounding_max_lat = '38.451652' where iso_key = 'US-DE';
update geoareas set bounding_min_long = '-159.764448', bounding_min_lat = '22.228955', bounding_max_long = '-154.807817', bounding_max_lat = '18.948267' where iso_key = 'US-HI';
update geoareas set bounding_min_long = '-96.631756', bounding_min_lat = '43.501391', bounding_max_long = '-90.141582', bounding_max_lat = '40.379535' where iso_key = 'US-IA';
update geoareas set bounding_min_long = '-91.50534', bounding_min_lat = '42.510065', bounding_max_long = '-87.49622', bounding_max_lat = '36.983832' where iso_key = 'US-IL';
update geoareas set bounding_min_long = '-114.815198', bounding_min_lat = '37.00574', bounding_max_long = '-109.042503', bounding_max_lat = '31.331629' where iso_key = 'US-AZ';
update geoareas set bounding_min_long = '-75.561967', bounding_min_lat = '41.359907', bounding_max_long = '-73.902454', bounding_max_lat = '38.993869' where iso_key = 'US-NJ';
update geoareas set bounding_min_long = '-85.606675', bounding_min_lat = '35.00118', bounding_max_long = '-80.885553', bounding_max_lat = '30.356734' where iso_key = 'US-GA';
update geoareas set bounding_min_long = '-111.05254', bounding_min_lat = '45.002073', bounding_max_long = '-104.053011', bounding_max_lat = '40.998429' where iso_key = 'US-WY';
update geoareas set bounding_min_long = '-95.7664', bounding_min_lat = '40.615043', bounding_max_long = '-89.133825', bounding_max_lat = '35.997983' where iso_key = 'US-MO';
update geoareas set bounding_min_long = '-71.08183', bounding_min_lat = '47.461219', bounding_max_long = '-66.979601', bounding_max_lat = '43.057759' where iso_key = 'US-ME';
update geoareas set bounding_min_long = '-104.047534', bounding_min_lat = '49.000239', bounding_max_long = '-96.560556', bounding_max_lat = '45.933153' where iso_key = 'US-ND';
update geoareas set bounding_min_long = '-116.04751', bounding_min_lat = '49.000239', bounding_max_long = '-104.042057', bounding_max_lat = '44.394132' where iso_key = 'US-MT';
update geoareas set bounding_min_long = '-120.001861', bounding_min_lat = '42.000709', bounding_max_long = '-114.04295', bounding_max_lat = '35.00118' where iso_key = 'US-NV';
update geoareas set bounding_min_long = '-124.706553', bounding_min_lat = '49.000239', bounding_max_long = '-116.918344', bounding_max_lat = '45.549767' where iso_key = 'US-WA';
update geoareas set bounding_min_long = '-109.04798', bounding_min_lat = '37.000263', bounding_max_long = '-103.001438', bounding_max_lat = '31.331629' where iso_key = 'US-NM';
update geoareas set bounding_min_long = '-92.885529', bounding_min_lat = '46.95734', bounding_max_long = '-87.03068', bounding_max_lat = '42.493634' where iso_key = 'US-WI';
update geoareas set bounding_min_long = '-84.319594', bounding_min_lat = '36.589492', bounding_max_long = '-75.715321', bounding_max_lat = '33.845545' where iso_key = 'US-NC';
update geoareas set bounding_min_long = '-114.048427', bounding_min_lat = '42.000709', bounding_max_long = '-109.042503', bounding_max_lat = '37.000263' where iso_key = 'US-UT';
update geoareas set bounding_min_long = '-72.544173', bounding_min_lat = '45.303304', bounding_max_long = '-70.703921', bounding_max_lat = '42.696281' where iso_key = 'US-NH';
update geoareas set bounding_min_long = '-124.553198', bounding_min_lat = '46.261769', bounding_max_long = '-116.463758', bounding_max_lat = '41.989755' where iso_key = 'US-OR';
update geoareas set bounding_min_long = '-94.616242', bounding_min_lat = '36.501861', bounding_max_long = '-89.730812', bounding_max_lat = '33.002096' where iso_key = 'US-AR';
update geoareas set bounding_min_long = '-87.633143', bounding_min_lat = '31.003013', bounding_max_long = '-80.03115', bounding_max_lat = '25.120779' where iso_key = 'US-FL';
update geoareas set bounding_min_long = '-117.241483', bounding_min_lat = '49.000239', bounding_max_long = '-111.047063', bounding_max_lat = '41.995232' where iso_key = 'US-ID';
update geoareas set bounding_min_long = '-89.418626', bounding_min_lat = '39.103408', bounding_max_long = '-81.969987', bounding_max_lat = '36.496384' where iso_key = 'US-KY';
update geoareas set bounding_min_long = '-73.436914', bounding_min_lat = '45.013027', bounding_max_long = '-71.4926', bounding_max_lat = '42.729142' where iso_key = 'US-VT';
update geoareas set bounding_min_long = '-79.488933', bounding_min_lat = '39.722302', bounding_max_long = '-75.047134', bounding_max_lat = '37.909435' where iso_key = 'US-MD';
update geoareas set bounding_min_long = '-83.339222', bounding_min_lat = '35.198349', bounding_max_long = '-78.541422', bounding_max_lat = '32.032678' where iso_key = 'US-SC';
update geoareas set bounding_min_long = '-73.727192', bounding_min_lat = '42.050002', bounding_max_long = '-71.799309', bounding_max_lat = '40.987475' where iso_key = 'US-CT';
update geoareas set bounding_min_long = '-88.060345', bounding_min_lat = '41.759724', bounding_max_long = '-84.801565', bounding_max_lat = '37.788942' where iso_key = 'US-IN';
update geoareas set bounding_min_long = '-79.76278', bounding_min_lat = '45.018503', bounding_max_long = '-72.100541', bounding_max_lat = '40.543843' where iso_key = 'US-NY';
update geoareas set bounding_min_long = '-94.041164', bounding_min_lat = '33.018527', bounding_max_long = '-89.002379', bounding_max_lat = '29.009407' where iso_key = 'US-LA';
update geoareas set bounding_min_long = '-63.160015', bounding_min_lat = '18.269738', bounding_max_long = '-62.979561', bounding_max_lat = '18.171398' where iso_key = 'AI';
update geoareas set bounding_min_long = '-67.937062', bounding_min_lat = '18.522151', bounding_max_long = '-65.294872', bounding_max_lat = '17.947251' where iso_key = 'PR';
update geoareas set bounding_min_long = '-70.066131', bounding_min_lat = '12.614114', bounding_max_long = '-69.895676', bounding_max_lat = '12.423015' where iso_key = 'AW';
update geoareas set bounding_min_long = '-81.419082', bounding_min_lat = '19.765745', bounding_max_long = '-79.742311', bounding_max_lat = '19.271899' where iso_key = 'KY';
update geoareas set bounding_min_long = '-63.12472', bounding_min_lat = '18.068942', bounding_max_long = '-63.011168', bounding_max_lat = '18.019185' where iso_key = 'SX';
update geoareas set bounding_min_long = '-61.906112', bounding_min_lat = '11.325407', bounding_max_long = '-60.525499', bounding_max_lat = '10.064657' where iso_key = 'TT';
update geoareas set bounding_min_long = '-64.695089', bounding_min_lat = '18.752706', bounding_max_long = '-64.273591', bounding_max_lat = '18.399136' where iso_key = 'VG';
update geoareas set bounding_min_long = '144.64929', bounding_min_lat = '13.622373', bounding_max_long = '144.940848', bounding_max_lat = '13.257537' where iso_key = 'GU';
update geoareas set bounding_min_long = '-56.386889', bounding_min_lat = '47.098986', bounding_max_long = '-56.137343', bounding_max_lat = '46.752857' where iso_key = 'PM';
update geoareas set bounding_min_long = '-61.21972', bounding_min_lat = '14.875268', bounding_max_long = '-60.826282', bounding_max_lat = '14.426252' where iso_key = 'MQ';
update geoareas set bounding_min_long = '-78.985664', bounding_min_lat = '26.940087', bounding_max_long = '-72.747285', bounding_max_lat = '20.937379' where iso_key = 'BS';
update geoareas set bounding_min_long = '-118.401346', bounding_min_lat = '32.715342', bounding_max_long = '-86.696305', bounding_max_lat = '14.545392' where iso_key = 'MX';
update geoareas set bounding_min_long = '-61.481151', bounding_min_lat = '15.633103', bounding_max_long = '-61.251062', bounding_max_lat = '15.227288' where iso_key = 'DM';
update geoareas set bounding_min_long = '-85.908032', bounding_min_lat = '11.189447', bounding_max_long = '-82.563585', bounding_max_lat = '8.07067' where iso_key = 'CR';
update geoareas set bounding_min_long = '-62.840474', bounding_min_lat = '17.402607', bounding_max_long = '-62.532224', bounding_max_lat = '17.10061' where iso_key = 'KN';
update geoareas set bounding_min_long = '-63.123019', bounding_min_lat = '18.115355', bounding_max_long = '-63.00943', bounding_max_lat = '18.068942' where iso_key = 'MF';
update geoareas set bounding_min_long = '-62.22307', bounding_min_lat = '16.809569', bounding_max_long = '-62.148449', bounding_max_lat = '16.681204' where iso_key = 'MS';
update geoareas set bounding_min_long = '-90.10589', bounding_min_lat = '14.431083', bounding_max_long = '-87.715312', bounding_max_lat = '13.164003' where iso_key = 'SV';
update geoareas set bounding_min_long = '-74.47811', bounding_min_lat = '20.093632', bounding_max_long = '-71.645311', bounding_max_lat = '18.039158' where iso_key = 'HT';
update geoareas set bounding_min_long = '-61.887121', bounding_min_lat = '17.714061', bounding_max_long = '-61.686048', bounding_max_lat = '16.997154' where iso_key = 'AG';
update geoareas set bounding_min_long = '-64.862857', bounding_min_lat = '32.386912', bounding_max_long = '-64.668321', bounding_max_lat = '32.259633' where iso_key = 'BM';
update geoareas set bounding_min_long = '-141.002137', bounding_min_lat = '83.116116', bounding_max_long = '-52.653654', bounding_max_lat = '41.674873' where iso_key = 'CA';
update geoareas set bounding_min_long = '-102.0016486358272', bounding_min_lat = '60.00176841733511', bounding_max_long = '-88.94849506171704', bounding_max_lat = '48.99176433522747' where iso_key = 'CA-MB';
update geoareas set bounding_min_long = '-120.6818513747876', bounding_min_lat = '83.11611378149931', bounding_max_long = '-61.20721348589291', bounding_max_lat = '51.94488944788018' where iso_key = 'CA-NU';
update geoareas set bounding_min_long = '-110.0000254057848', bounding_min_lat = '60.0010872649654', bounding_max_long = '-101.3672705598698', bounding_max_lat = '48.99308269465275' where iso_key = 'CA-SK';
update geoareas set bounding_min_long = '-69.05366031841427', bounding_min_lat = '48.0669681573711', bounding_max_long = '-63.83191329304856', bounding_max_lat = '44.62890650278197' where iso_key = 'CA-NB';
update geoareas set bounding_min_long = '-67.7614703277149', bounding_min_lat = '60.30597985472115', bounding_max_long = '-52.65365358928807', bounding_max_lat = '46.62828646186474' where iso_key = 'CA-NL';
update geoareas set bounding_min_long = '-141.0021371841246', bounding_min_lat = '69.65078611940038', bounding_max_long = '-123.8191567923179', bounding_max_lat = '60.0010872649654' where iso_key = 'CA-YT';
update geoareas set bounding_min_long = '-120.0010176085814', bounding_min_lat = '60.0010872649654', bounding_max_long = '-109.9994321440434', bounding_max_lat = '48.99308269465275' where iso_key = 'CA-AB';
update geoareas set bounding_min_long = '-139.0565188237724', bounding_min_lat = '60.00158164974989', bounding_max_long = '-114.0506023425286', bounding_max_lat = '48.32278481752041' where iso_key = 'CA-BC';
update geoareas set bounding_min_long = '-95.16036297446225', bounding_min_lat = '56.85130687137327', bounding_max_long = '-74.34044640876104', bounding_max_lat = '41.73583984374999' where iso_key = 'CA-ON';
update geoareas set bounding_min_long = '-79.7139366039778', bounding_min_lat = '62.5725143650107', bounding_max_long = '-57.10012836828267', bounding_max_lat = '45.00386989599195' where iso_key = 'CA-QC';
update geoareas set bounding_min_long = '-136.4454365708471', bounding_min_lat = '78.75782626172816', bounding_max_long = '-101.9866358178717', bounding_max_lat = '60.0010872649654' where iso_key = 'CA-NT';
update geoareas set bounding_min_long = '-66.3241069916339', bounding_min_lat = '47.00970981625864', bounding_max_long = '-59.72712431810393', bounding_max_lat = '43.51806783735999' where iso_key = 'CA-NS';
update geoareas set bounding_min_long = '-64.40314744569886', bounding_min_lat = '47.06157627331535', bounding_max_long = '-62.02372840961863', bounding_max_lat = '45.9668875108532' where iso_key = 'CA-PE';
update geoareas set bounding_min_long = '-61.782192', bounding_min_lat = '12.237031', bounding_max_long = '-61.607035', bounding_max_lat = '12.00844' where iso_key = 'GD';
update geoareas set bounding_min_long = '-72.00038', bounding_min_lat = '19.913979', bounding_max_long = '-68.339156', bounding_max_lat = '17.635616' where iso_key = 'DO';
update geoareas set bounding_min_long = '-89.362602', bounding_min_lat = '16.513954', bounding_max_long = '-83.157528', bounding_max_lat = '12.97926' where iso_key = 'HN';
update geoareas set bounding_min_long = '-72.342375', bounding_min_lat = '21.95189', bounding_max_long = '-71.636914', bounding_max_lat = '21.751696' where iso_key = 'TC';
update geoareas set bounding_min_long = '-61.353536', bounding_min_lat = '13.35872', bounding_max_long = '-61.124015', bounding_max_lat = '12.694703' where iso_key = 'VC';
update geoareas set bounding_min_long = '-84.88719', bounding_min_lat = '23.190445', bounding_max_long = '-74.136813', bounding_max_lat = '19.855481' where iso_key = 'CU';
update geoareas set bounding_min_long = '-65.023622', bounding_min_lat = '18.385209', bounding_max_long = '-64.580445', bounding_max_lat = '17.701711' where iso_key = 'VI';
update geoareas set bounding_min_long = '-92.23514', bounding_min_lat = '17.816432', bounding_max_long = '-88.22833', bounding_max_lat = '13.736526' where iso_key = 'GT';
update geoareas set bounding_min_long = '-62.875433', bounding_min_lat = '17.922266', bounding_max_long = '-62.799727', bounding_max_lat = '17.875188' where iso_key = 'BL';
update geoareas set bounding_min_long = '-83.02733', bounding_min_lat = '9.597864', bounding_max_long = '-77.196006', bounding_max_lat = '7.220076' where iso_key = 'PA';
update geoareas set bounding_min_long = '-63.16651694148267', bounding_min_lat = '18.13444035064876', bounding_max_long = '-61.00787456922184', bounding_max_lat = '15.84479213150664' where iso_key = 'GP';
update geoareas set bounding_min_long = '35.764463', bounding_min_lat = '37.297259', bounding_max_long = '42.359048', bounding_max_lat = '32.317304' where iso_key = 'SY';
update geoareas set bounding_min_long = '123.679821', bounding_min_lat = '45.509522', bounding_max_long = '145.83299', bounding_max_lat = '24.266064' where iso_key = 'JP';
update geoareas set bounding_min_long = '126.007534', bounding_min_lat = '38.623457', bounding_max_long = '130.934262', bounding_max_lat = '33.201514' where iso_key = 'KR';
update geoareas set bounding_min_long = '73.607321', bounding_min_lat = '53.555594', bounding_max_long = '134.752323', bounding_max_lat = '18.218242' where iso_key = 'CN';
update geoareas set bounding_min_long = '88.023392', bounding_min_lat = '26.571531', bounding_max_long = '92.63169', bounding_max_lat = '20.790437' where iso_key = 'BD';
update geoareas set bounding_min_long = '102.319729', bounding_min_lat = '14.705098', bounding_max_long = '107.605453', bounding_max_lat = '10.411251' where iso_key = 'KH';
update geoareas set bounding_min_long = '46.609228', bounding_min_lat = '55.389617', bounding_max_long = '87.322815', bounding_max_lat = '40.608633' where iso_key = 'KZ';
update geoareas set bounding_min_long = '95.206623', bounding_min_lat = '5.90703', bounding_max_long = '140.976129', bounding_max_lat = '-10.90967' where iso_key = 'ID';
update geoareas set bounding_min_long = '38.77353', bounding_min_lat = '37.371879', bounding_max_long = '48.546526', bounding_max_lat = '29.063653' where iso_key = 'IQ';
update geoareas set bounding_min_long = '87.743203', bounding_min_lat = '52.117284', bounding_max_long = '119.897829', bounding_max_lat = '41.595523' where iso_key = 'MN';
update geoareas set bounding_min_long = '97.373933', bounding_min_lat = '20.424413', bounding_max_long = '105.641025', bounding_max_lat = '5.636762' where iso_key = 'TH';
update geoareas set bounding_min_long = '46.531455', bounding_min_lat = '30.09731', bounding_max_long = '48.442449', bounding_max_lat = '28.533168' where iso_key = 'KW';
update geoareas set bounding_min_long = '131.134921', bounding_min_lat = '7.712088', bounding_max_long = '134.659616', bounding_max_lat = '3.021883' where iso_key = 'PW';
update geoareas set bounding_min_long = '51.568357', bounding_min_lat = '26.068177', bounding_max_long = '56.387959', bounding_max_lat = '22.621462' where iso_key = 'AE';
update geoareas set bounding_min_long = '55.975684', bounding_min_lat = '45.555359', bounding_max_long = '73.136963', bounding_max_lat = '37.172227' where iso_key = 'UZ';
update geoareas set bounding_min_long = '118.287282', bounding_min_lat = '25.276907', bounding_max_long = '121.929023', bounding_max_lat = '21.925019' where iso_key = 'TW';
update geoareas set bounding_min_long = '42.549062', bounding_min_lat = '18.996153', bounding_max_long = '54.511123', bounding_max_lat = '12.31899' where iso_key = 'YE';
update geoareas set bounding_min_long = '67.349616', bounding_min_lat = '41.035092', bounding_max_long = '75.118805', bounding_max_lat = '36.684014' where iso_key = 'TJ';
update geoareas set bounding_min_long = '88.738801', bounding_min_lat = '28.311192', bounding_max_long = '92.083455', bounding_max_lat = '26.701549' where iso_key = 'BT';
update geoareas set bounding_min_long = '124.036388', bounding_min_lat = '-8.139916', bounding_max_long = '127.296137', bounding_max_lat = '-9.511926' where iso_key = 'TL';
update geoareas set bounding_min_long = '51.977634', bounding_min_lat = '26.35635', bounding_max_long = '59.837516', bounding_max_lat = '16.64839' where iso_key = 'OM';
update geoareas set bounding_min_long = '102.127441', bounding_min_lat = '23.34519', bounding_max_long = '109.444927', bounding_max_lat = '8.583249' where iso_key = 'VN';
update geoareas set bounding_min_long = '116.969533', bounding_min_lat = '20.84126', bounding_max_long = '126.593338', bounding_max_lat = '5.060208' where iso_key = 'PH';
update geoareas set bounding_min_long = '124.348617', bounding_min_lat = '42.998151', bounding_max_long = '130.687352', bounding_max_lat = '37.719042' where iso_key = 'KP';
update geoareas set bounding_min_long = '44.023184', bounding_min_lat = '39.768529', bounding_max_long = '63.305211', bounding_max_lat = '25.102112' where iso_key = 'IR';
update geoareas set bounding_min_long = '25.668956', bounding_min_lat = '42.093245', bounding_max_long = '44.817141', bounding_max_lat = '35.831457' where iso_key = 'TR';
update geoareas set bounding_min_long = '60.485797', bounding_min_lat = '38.456413', bounding_max_long = '74.891326', bounding_max_lat = '29.391953' where iso_key = 'AF';
update geoareas set bounding_min_long = '34.24528', bounding_min_lat = '33.431758', bounding_max_long = '35.913498', bounding_max_lat = '29.477322' where iso_key = 'IL';
update geoareas set bounding_min_long = '114.063818', bounding_min_lat = '5.022381', bounding_max_long = '115.326739', bounding_max_lat = '4.023992' where iso_key = 'BN';
update geoareas set bounding_min_long = '79.707828', bounding_min_lat = '9.812683', bounding_max_long = '81.876894', bounding_max_lat = '5.949353' where iso_key = 'LK';
update geoareas set bounding_min_long = '34.616213', bounding_min_lat = '32.124499', bounding_max_long = '55.641028', bounding_max_lat = '16.371792' where iso_key = 'SA';
update geoareas set bounding_min_long = '35.108586', bounding_min_lat = '34.678685', bounding_max_long = '36.584982', bounding_max_lat = '33.075708' where iso_key = 'LB';
update geoareas set bounding_min_long = '99.646249', bounding_min_lat = '7.351645', bounding_max_long = '119.266343', bounding_max_lat = '0.861963' where iso_key = 'MY';
update geoareas set bounding_min_long = '92.179573', bounding_min_lat = '28.517045', bounding_max_long = '101.147294', bounding_max_lat = '9.875367' where iso_key = 'MM';
update geoareas set bounding_min_long = '34.21666', bounding_min_lat = '32.534', bounding_max_long = '35.572', bounding_max_lat = '31.216541' where iso_key = 'PS';
update geoareas set bounding_min_long = '80.051631', bounding_min_lat = '30.387525', bounding_max_long = '88.161575', bounding_max_lat = '26.360303' where iso_key = 'NP';
update geoareas set bounding_min_long = '50.452456', bounding_min_lat = '26.246434', bounding_max_long = '50.61751', bounding_max_lat = '25.806797' where iso_key = 'BH';
update geoareas set bounding_min_long = '52.493881', bounding_min_lat = '42.778474', bounding_max_long = '66.629349', bounding_max_lat = '35.170773' where iso_key = 'TM';
update geoareas set bounding_min_long = '69.229087', bounding_min_lat = '43.240358', bounding_max_long = '80.246193', bounding_max_lat = '39.20753' where iso_key = 'KG';
update geoareas set bounding_min_long = '73.382064', bounding_min_lat = '4.247648', bounding_max_long = '73.52836', bounding_max_lat = '3.229416' where iso_key = 'MV';
update geoareas set bounding_min_long = '100.114954', bounding_min_lat = '22.495268', bounding_max_long = '107.65315', bounding_max_lat = '13.921166' where iso_key = 'LA';
update geoareas set bounding_min_long = '50.754556', bounding_min_lat = '26.153288', bounding_max_long = '51.608871', bounding_max_lat = '24.564651' where iso_key = 'QA';
update geoareas set bounding_min_long = '113.838871', bounding_min_lat = '22.565005', bounding_max_long = '114.335275', bounding_max_lat = '22.195183' where iso_key = 'HK';
update geoareas set bounding_min_long = '138.061888', bounding_min_lat = '9.593291', bounding_max_long = '162.993493', bounding_max_lat = '5.277249' where iso_key = 'FM';
update geoareas set bounding_min_long = '103.650139', bounding_min_lat = '1.447095', bounding_max_long = '103.996371', bounding_max_lat = '1.265401' where iso_key = 'SG';
update geoareas set bounding_min_long = '34.950818', bounding_min_lat = '33.372201', bounding_max_long = '39.292775', bounding_max_lat = '29.190467' where iso_key = 'JO';
update geoareas set bounding_min_long = '113.478893', bounding_min_lat = '22.245929', bounding_max_long = '113.548139', bounding_max_lat = '22.195545' where iso_key = 'MO';
update geoareas set bounding_min_long = '68.16507', bounding_min_lat = '35.495922', bounding_max_long = '97.343547', bounding_max_lat = '6.748684' where iso_key = 'IN';
update geoareas set bounding_min_long = '60.843398', bounding_min_lat = '37.036654', bounding_max_long = '77.04861', bounding_max_lat = '23.753382' where iso_key = 'PK';
update geoareas set bounding_min_long = '-54.616273', bounding_min_lat = '5.782231', bounding_max_long = '-51.652552', bounding_max_lat = '2.121059' where iso_key = 'GF';
update geoareas set bounding_min_long = '-81.336632', bounding_min_lat = '-0.041754', bounding_max_long = '-68.685234', bounding_max_lat = '-18.345598' where iso_key = 'PE';
update geoareas set bounding_min_long = '-62.651002', bounding_min_lat = '-19.286213', bounding_max_long = '-54.241826', bounding_max_lat = '-27.553822' where iso_key = 'PY';
update geoareas set bounding_min_long = '-73.576253', bounding_min_lat = '-21.802545', bounding_max_long = '-53.668527', bounding_max_lat = '-55.032144' where iso_key = 'AR';
update geoareas set bounding_min_long = '-69.645692', bounding_min_lat = '-9.710415', bounding_max_long = '-57.495658', bounding_max_lat = '-22.891677' where iso_key = 'BO';
update geoareas set bounding_min_long = '-58.438133', bounding_min_lat = '-30.101056', bounding_max_long = '-53.125563', bounding_max_lat = '-34.932803' where iso_key = 'UY';
update geoareas set bounding_min_long = '-79.025429', bounding_min_lat = '12.434358', bounding_max_long = '-66.876042', bounding_max_lat = '-4.235969' where iso_key = 'CO';
update geoareas set bounding_min_long = '-73.366188', bounding_min_lat = '12.177862', bounding_max_long = '-59.828927', bounding_max_lat = '0.687969' where iso_key = 'VE';
update geoareas set bounding_min_long = '-61.145048', bounding_min_lat = '-51.269945', bounding_max_long = '-57.791816', bounding_max_lat = '-52.308021' where iso_key = 'FK';
update geoareas set bounding_min_long = '-109.434142', bounding_min_lat = '-17.506011', bounding_max_long = '-66.435784', bounding_max_lat = '-55.89173' where iso_key = 'CL';
update geoareas set bounding_min_long = '-58.054513', bounding_min_lat = '5.993459', bounding_max_long = '-53.990472', bounding_max_lat = '1.842239' where iso_key = 'SR';
update geoareas set bounding_min_long = '-91.654142', bounding_min_lat = '1.455364', bounding_max_long = '-75.249587', bounding_max_lat = '-4.990651' where iso_key = 'EC';
update geoareas set bounding_min_long = '-61.390795', bounding_min_lat = '8.549298', bounding_max_long = '-56.4828', bounding_max_lat = '1.201219' where iso_key = 'GY';
update geoareas set bounding_min_long = '-74.002067', bounding_min_lat = '5.257974', bounding_max_long = '-34.805467', bounding_max_lat = '-33.742178' where iso_key = 'BR';
update geoareas set bounding_min_long = '19.519047', bounding_min_lat = '60.405822', bounding_max_long = '20.61128', bounding_max_lat = '60.011686' where iso_key = 'AX';
update geoareas set bounding_min_long = '20.899789', bounding_min_lat = '56.411182', bounding_max_long = '26.77571', bounding_max_lat = '53.89299' where iso_key = 'LT';
update geoareas set bounding_min_long = '7.377721', bounding_min_lat = '43.77092', bounding_max_long = '7.438699', bounding_max_lat = '43.73175' where iso_key = 'MC';
update geoareas set bounding_min_long = '-2.235827', bounding_min_lat = '49.266373', bounding_max_long = '-2.009898', bounding_max_lat = '49.169816' where iso_key = 'JE';
update geoareas set bounding_min_long = '2.524942', bounding_min_lat = '51.491095', bounding_max_long = '6.364501', bounding_max_lat = '49.51088' where iso_key = 'BE';
update geoareas set bounding_min_long = '14.128626', bounding_min_lat = '54.838178', bounding_max_long = '24.105743', bounding_max_lat = '49.020729' where iso_key = 'PL';
update geoareas set bounding_min_long = '43.439447', bounding_min_lat = '41.290968', bounding_max_long = '46.584733', bounding_max_lat = '38.869049' where iso_key = 'AM';
update geoareas set bounding_min_long = '-8.144794', bounding_min_lat = '60.831894', bounding_max_long = '1.746591', bounding_max_lat = '50.021392' where iso_key = 'GB';
update geoareas set bounding_min_long = '-5.262304687499977', bounding_min_lat = '53.41928710937501', bounding_max_long = '-2.662304687499926', bounding_max_lat = '51.39042968750005' where iso_key = 'GB-WLS';
update geoareas set bounding_min_long = '-7.54296875', bounding_min_lat = '60.83188476562498', bounding_max_long = '-0.774267578124949', bounding_max_lat = '54.68945312500006' where iso_key = 'GB-SCT';
update geoareas set bounding_min_long = '-8.144824218749989', bounding_min_lat = '55.241796875', bounding_max_long = '-5.470410156249983', bounding_max_lat = '54.05126953125' where iso_key = 'GB-NIR';
update geoareas set bounding_min_long = '-5.65625', bounding_min_lat = '55.80795898437501', bounding_max_long = '1.74658203125', bounding_max_lat = '50.02138671875002' where iso_key = 'GB-ENG';
update geoareas set bounding_min_long = '20.622183', bounding_min_lat = '70.064819', bounding_max_long = '31.536504', bounding_max_lat = '59.816039' where iso_key = 'FI';
update geoareas set bounding_min_long = '9.523998', bounding_min_lat = '49.001118', bounding_max_long = '17.147357', bounding_max_lat = '46.3997' where iso_key = 'AT';
update geoareas set bounding_min_long = '14.180354', bounding_min_lat = '36.075809', bounding_max_long = '14.566171', bounding_max_lat = '35.820191' where iso_key = 'MT';
update geoareas set bounding_min_long = '13.517139', bounding_min_lat = '46.534628', bounding_max_long = '19.401018', bounding_max_lat = '42.432914' where iso_key = 'HR';
update geoareas set bounding_min_long = '-68.371067', bounding_min_lat = '53.625513', bounding_max_long = '7.197318', bounding_max_lat = '12.032082' where iso_key = 'NL';
update geoareas set bounding_min_long = '-4.762494', bounding_min_lat = '51.09714', bounding_max_long = '9.556451', bounding_max_lat = '41.384916' where iso_key = 'FR';
update geoareas set bounding_min_long = '22.131859', bounding_min_lat = '52.353549', bounding_max_long = '40.12828', bounding_max_lat = '44.387575' where iso_key = 'UA';
update geoareas set bounding_min_long = '5.857503', bounding_min_lat = '55.058758', bounding_max_long = '15.016634', bounding_max_lat = '47.27882' where iso_key = 'DE';
update geoareas set bounding_min_long = '20.241845', bounding_min_lat = '48.263463', bounding_max_long = '29.705918', bounding_max_lat = '43.670797' where iso_key = 'RO';
update geoareas set bounding_min_long = '-18.160566', bounding_min_lat = '43.764564', bounding_max_long = '4.322041', bounding_max_lat = '27.646401' where iso_key = 'ES';
update geoareas set bounding_min_long = '1.414829', bounding_min_lat = '42.64272', bounding_max_long = '1.740184', bounding_max_lat = '42.434464' where iso_key = 'AD';
update geoareas set bounding_min_long = '-9.098871', bounding_min_lat = '80.477853', bounding_max_long = '33.629297', bounding_max_lat = '58.020955' where iso_key = 'NO';
update geoareas set bounding_min_long = '-31.282943', bounding_min_lat = '42.137377', bounding_max_long = '-6.212509', bounding_max_lat = '32.648292' where iso_key = 'PT';
update geoareas set bounding_min_long = '9.479453', bounding_min_lat = '47.270758', bounding_max_long = '9.610504', bounding_max_lat = '47.057386' where iso_key = 'LI';
update geoareas set bounding_min_long = '-72.818082', bounding_min_lat = '83.5996', bounding_max_long = '-11.42555', bounding_max_lat = '59.815471' where iso_key = 'GL';
update geoareas set bounding_min_long = '20.448602', bounding_min_lat = '42.358138', bounding_max_long = '23.005656', bounding_max_lat = '40.84991' where iso_key = 'MK';
update geoareas set bounding_min_long = '5.970003', bounding_min_lat = '47.775637', bounding_max_long = '10.454587', bounding_max_lat = '45.830045' where iso_key = 'CH';
update geoareas set bounding_min_long = '19.646481', bounding_min_lat = '41.743783', bounding_max_long = '28.231797', bounding_max_lat = '34.934457' where iso_key = 'GR';
update geoareas set bounding_min_long = '22.344042', bounding_min_lat = '44.237791', bounding_max_long = '28.585315', bounding_max_lat = '41.243529' where iso_key = 'BG';
update geoareas set bounding_min_long = '6.627689', bounding_min_lat = '47.082139', bounding_max_long = '18.485828', bounding_max_lat = '36.687838' where iso_key = 'IT';
update geoareas set bounding_min_long = '12.42749', bounding_min_lat = '41.906202', bounding_max_long = '12.439169', bounding_max_lat = '41.897572' where iso_key = 'VA';
update geoareas set bounding_min_long = '12.396897', bounding_min_lat = '43.98977', bounding_max_long = '12.514668', bounding_max_lat = '43.894091' where iso_key = 'SM';
update geoareas set bounding_min_long = '18.839088', bounding_min_lat = '46.169172', bounding_max_long = '22.97682', bounding_max_lat = '42.242125' where iso_key = 'RS';
update geoareas set bounding_min_long = '39.978315', bounding_min_lat = '43.569796', bounding_max_long = '46.672583', bounding_max_lat = '41.070207' where iso_key = 'GE';
update geoareas set bounding_min_long = '15.736642', bounding_min_lat = '45.276565', bounding_max_long = '19.583746', bounding_max_lat = '42.559728' where iso_key = 'BA';
update geoareas set bounding_min_long = '19.280715', bounding_min_lat = '42.647966', bounding_max_long = '21.031099', bounding_max_lat = '39.653497' where iso_key = 'AL';
update geoareas set bounding_min_long = '20.029506', bounding_min_lat = '43.261081', bounding_max_long = '21.752915', bounding_max_lat = '41.853802' where iso_key = 'XK';
update geoareas set bounding_min_long = '21.854511', bounding_min_lat = '59.638996', bounding_max_long = '28.151078', bounding_max_lat = '57.525481' where iso_key = 'EE';
update geoareas set bounding_min_long = '-24.475658', bounding_min_lat = '66.526069', bounding_max_long = '-13.556118', bounding_max_lat = '63.406699' where iso_key = 'IS';
update geoareas set bounding_min_long = '11.147155', bounding_min_lat = '69.036872', bounding_max_long = '24.155508', bounding_max_lat = '55.346389' where iso_key = 'SE';
update geoareas set bounding_min_long = '32.301005', bounding_min_lat = '35.182685', bounding_max_long = '34.050149', bounding_max_lat = '34.56957' where iso_key = 'CY';
update geoareas set bounding_min_long = '13.378181', bounding_min_lat = '46.86329', bounding_max_long = '16.516181', bounding_max_lat = '45.42839' where iso_key = 'SI';
update geoareas set bounding_min_long = '18.436374', bounding_min_lat = '43.542355', bounding_max_long = '20.347627', bounding_max_lat = '41.869073' where iso_key = 'ME';
update geoareas set bounding_min_long = '21.014976', bounding_min_lat = '58.063433', bounding_max_long = '28.202031', bounding_max_lat = '55.667507' where iso_key = 'LV';
update geoareas set bounding_min_long = '23.175103', bounding_min_lat = '56.145824', bounding_max_long = '32.710283', bounding_max_lat = '51.265062' where iso_key = 'BY';
update geoareas set bounding_min_long = '-5.355799', bounding_min_lat = '36.1633070000001', bounding_max_long = '-5.33451', bounding_max_lat = '36.112175' where iso_key = 'GI';
update geoareas set bounding_min_long = '16.093054', bounding_min_lat = '48.553471', bounding_max_long = '22.87662', bounding_max_lat = '45.753022' where iso_key = 'HU';
update geoareas set bounding_min_long = '-10.390239', bounding_min_lat = '55.36582', bounding_max_long = '-6.027379', bounding_max_lat = '51.473706' where iso_key = 'IE';
update geoareas set bounding_min_long = '5.724953', bounding_min_lat = '50.167171', bounding_max_long = '6.493795', bounding_max_lat = '49.445458' where iso_key = 'LU';
update geoareas set bounding_min_long = '-179.999989', bounding_min_lat = '81.854177', bounding_max_long = '180', bounding_max_lat = '41.199269' where iso_key = 'RU';
update geoareas set bounding_min_long = '44.768255', bounding_min_lat = '41.890958', bounding_max_long = '50.365949', bounding_max_lat = '38.398742' where iso_key = 'AZ';
update geoareas set bounding_min_long = '12.089733', bounding_min_lat = '51.037815', bounding_max_long = '18.832215', bounding_max_lat = '48.576208' where iso_key = 'CZ';
update geoareas set bounding_min_long = '8.121499', bounding_min_lat = '57.73689', bounding_max_long = '15.137092', bounding_max_lat = '54.628862' where iso_key = 'DK';
update geoareas set bounding_min_long = '-4.785335', bounding_min_lat = '54.40717', bounding_max_long = '-4.337998', bounding_max_lat = '54.058716' where iso_key = 'IM';
update geoareas set bounding_min_long = '-2.646138', bounding_min_lat = '49.506591', bounding_max_long = '-2.512322', bounding_max_lat = '49.428715' where iso_key = 'GG';
update geoareas set bounding_min_long = '16.862671', bounding_min_lat = '49.597696', bounding_max_long = '22.538656', bounding_max_lat = '47.763416' where iso_key = 'SK';
update geoareas set bounding_min_long = '26.618975', bounding_min_lat = '48.477713', bounding_max_long = '30.131009', bounding_max_lat = '45.450456' where iso_key = 'MD';
update geoareas set bounding_min_long = '-7.422616', bounding_min_lat = '62.355677', bounding_max_long = '-6.406063', bounding_max_lat = '61.414288' where iso_key = 'FO';
update geoareas set bounding_min_long = '51.659307', bounding_min_lat = '-46.326836', bounding_max_long = '70.555415', bounding_max_lat = '-49.709834' where iso_key = 'TF';
update geoareas set bounding_min_long = '-180', bounding_min_lat = '-60.520905', bounding_max_long = '180', bounding_max_lat = '-89.998899' where iso_key = 'AQ';
update geoareas set bounding_min_long = '-159.842486', bounding_min_lat = '-21.186458', bounding_max_long = '-159.736885', bounding_max_lat = '-21.249504' where iso_key = 'CK';
update geoareas set bounding_min_long = '-169.948329', bounding_min_lat = '-18.966025', bounding_max_long = '-169.793429', bounding_max_lat = '-19.137901' where iso_key = 'NU';
update geoareas set bounding_min_long = '-180', bounding_min_lat = '-12.476913', bounding_max_long = '180', bounding_max_lat = '-21.705807' where iso_key = 'FJ';
update geoareas set bounding_min_long = '-151.5124', bounding_min_lat = '-8.781531', bounding_max_long = '-136.293897', bounding_max_lat = '-20.875883' where iso_key = 'PF';
update geoareas set bounding_min_long = '-178.194389', bounding_min_lat = '-13.221673', bounding_max_long = '-176.128055', bounding_max_lat = '-14.324913' where iso_key = 'WF';
update geoareas set bounding_min_long = '-128.350196', bounding_min_lat = '-24.323218', bounding_max_long = '-128.290097', bounding_max_lat = '-24.412566' where iso_key = 'PN';
update geoareas set bounding_min_long = '167.906165', bounding_min_lat = '-29.013939', bounding_max_long = '167.990398', bounding_max_lat = '-29.096312' where iso_key = 'NF';
update geoareas set bounding_min_long = '-172.496979', bounding_min_lat = '-8.553888', bounding_max_long = '-171.214722', bounding_max_lat = '-9.37889' where iso_key = 'TK';
update geoareas set bounding_min_long = '105.628998', bounding_min_lat = '-10.38408', bounding_max_long = '105.736603', bounding_max_lat = '-10.51097' where iso_key = 'CX';
update geoareas set bounding_min_long = '159.928254', bounding_min_lat = '-19.114647', bounding_max_long = '168.139122', bounding_max_lat = '-22.661097' where iso_key = 'NC';
update geoareas set bounding_min_long = '140.862338', bounding_min_lat = '-1.35325', bounding_max_long = '155.957644', bounding_max_lat = '-11.630556' where iso_key = 'PG';
update geoareas set bounding_min_long = '-174.540839', bounding_min_lat = '3.923533', bounding_max_long = '174.778924', bounding_max_lat = '-11.45682' where iso_key = 'KI';
update geoareas set bounding_min_long = '176.066376', bounding_min_lat = '-5.65778', bounding_max_long = '179.231094', bounding_max_lat = '-8.55415' where iso_key = 'TV';
update geoareas set bounding_min_long = '-176.847675', bounding_min_lat = '-8.546506', bounding_max_long = '178.536214', bounding_max_lat = '-52.570279' where iso_key = 'NZ';
update geoareas set bounding_min_long = '-172.778492', bounding_min_lat = '-13.465276', bounding_max_long = '-171.449581', bounding_max_lat = '-14.047256' where iso_key = 'WS';
update geoareas set bounding_min_long = '166.907053', bounding_min_lat = '-0.489376', bounding_max_long = '166.958419', bounding_max_lat = '-0.550819' where iso_key = 'NR';
update geoareas set bounding_min_long = '155.677557', bounding_min_lat = '-6.608847', bounding_max_long = '166.92917', bounding_max_lat = '-11.832197' where iso_key = 'SB';
update geoareas set bounding_min_long = '96.819443', bounding_min_lat = '-12.12833', bounding_max_long = '96.91470300000012', bounding_max_lat = '-12.2' where iso_key = 'CC';
update geoareas set bounding_min_long = '166.844731', bounding_min_lat = '11.168647', bounding_max_long = '171.756783', bounding_max_lat = '5.799801' where iso_key = 'MH';
update geoareas set bounding_min_long = '112.908178', bounding_min_lat = '-10.051738', bounding_max_long = '158.958908', bounding_max_lat = '-54.749268' where iso_key = 'AU';
update geoareas set bounding_min_long = '150.6118705220982', bounding_min_lat = '-35.11962193000792', bounding_max_long = '150.7221073427097', bounding_max_lat = '-35.19004429597557' where iso_key = 'AU-JB';
update geoareas set bounding_min_long = '148.7700125416807', bounding_min_lat = '-35.14640659899841', bounding_max_long = '149.4038358080466', bounding_max_lat = '-35.91410926500274' where iso_key = 'AU-ACT';
update geoareas set bounding_min_long = '129.001960029777', bounding_min_lat = '-25.99901367192805', bounding_max_long = '141.025727578291', bounding_max_lat = '-38.07140671057834' where iso_key = 'AU-SA';
update geoareas set bounding_min_long = '140.9663354861816', bounding_min_lat = '-34.005190735152', bounding_max_long = '149.9424954965635', bounding_max_lat = '-39.14554005233448' where iso_key = 'AU-VIC';
update geoareas set bounding_min_long = '141.0021069719212', bounding_min_lat = '-28.14440193736187', bounding_max_long = '153.6169609687646', bounding_max_lat = '-37.50057905208423' where iso_key = 'AU-NSW';
update geoareas set bounding_min_long = '112.908153263503', bounding_min_lat = '-13.74418166147464', bounding_max_long = '129.001960029777', bounding_max_lat = '-35.09780308151935' where iso_key = 'AU-WA';
update geoareas set bounding_min_long = '138.0017406465288', bounding_min_lat = '-10.05169861046129', bounding_max_long = '153.5386723915595', bounding_max_lat = '-29.13934582297228' where iso_key = 'AU-QLD';
update geoareas set bounding_min_long = '129.001960029777', bounding_min_lat = '-10.96879337200827', bounding_max_long = '138.0024217988984', bounding_max_lat = '-25.99901367192805' where iso_key = 'AU-NT';
update geoareas set bounding_min_long = '143.8386891113783', bounding_min_lat = '-39.5802471001673', bounding_max_long = '148.474128741331', bounding_max_lat = '-43.61945868003225' where iso_key = 'AU-TAS';
update geoareas set bounding_min_long = '-170.820524', bounding_min_lat = '-14.257476', bounding_max_long = '-170.56811', bounding_max_lat = '-14.359795' where iso_key = 'AS';
update geoareas set bounding_min_long = '166.526094', bounding_min_lat = '-13.709499', bounding_max_long = '169.896328', bounding_max_lat = '-20.241813' where iso_key = 'VU';
update geoareas set bounding_min_long = '42.656446', bounding_min_lat = '11.499815', bounding_max_long = '48.938543', bounding_max_lat = '7.997083' where iso_key = 'SOL';
update geoareas set bounding_min_long = '21.825262', bounding_min_lat = '22.202418', bounding_max_long = '38.609458', bounding_max_lat = '8.665621' where iso_key = 'SD';
update geoareas set bounding_min_long = '7.495595', bounding_min_lat = '37.340383', bounding_max_long = '11.535968', bounding_max_lat = '30.229421' where iso_key = 'TN';
update geoareas set bounding_min_long = '-17.09877', bounding_min_lat = '27.656452', bounding_max_long = '-8.6821', bounding_max_lat = '20.806146' where iso_key = 'EH';
update geoareas set bounding_min_long = '11.130153', bounding_min_lat = '3.687295', bounding_max_long = '18.622151', bounding_max_lat = '-5.004294' where iso_key = 'CG';
update geoareas set bounding_min_long = '24.703226', bounding_min_lat = '31.654967', bounding_max_long = '36.871373', bounding_max_lat = '21.994885' where iso_key = 'EG';
update geoareas set bounding_min_long = '-16.711791', bounding_min_lat = '12.67995', bounding_max_long = '-13.673553', bounding_max_lat = '10.940159' where iso_key = 'GW';
update geoareas set bounding_min_long = '-13.292697', bounding_min_lat = '9.996522', bounding_max_long = '-10.283217', bounding_max_lat = '6.90653' where iso_key = 'SL';
update geoareas set bounding_min_long = '21.978897', bounding_min_lat = '-8.193608', bounding_max_long = '33.661543', bounding_max_lat = '-18.041534' where iso_key = 'ZM';
update geoareas set bounding_min_long = '25.22397', bounding_min_lat = '-15.643076', bounding_max_long = '33.006751', bounding_max_lat = '-22.402043' where iso_key = 'ZW';
update geoareas set bounding_min_long = '8.434245', bounding_min_lat = '3.758324', bounding_max_long = '11.33536', bounding_max_lat = '0.960097' where iso_key = 'GQ';
update geoareas set bounding_min_long = '-15.05122', bounding_min_lat = '12.673904', bounding_max_long = '-7.681205', bounding_max_lat = '7.215891' where iso_key = 'GN';
update geoareas set bounding_min_long = '29.323409', bounding_min_lat = '-0.994926', bounding_max_long = '40.463556', bounding_max_lat = '-11.716235' where iso_key = 'TZ';
update geoareas set bounding_min_long = '13.448203', bounding_min_lat = '23.445236', bounding_max_long = '23.983425', bounding_max_lat = '7.475306' where iso_key = 'TD';
update geoareas set bounding_min_long = '-16.824807', bounding_min_lat = '13.812129', bounding_max_long = '-13.826722', bounding_max_lat = '13.064138' where iso_key = 'GM';
update geoareas set bounding_min_long = '55.232836', bounding_min_lat = '-20.865134', bounding_max_long = '55.839104', bounding_max_lat = '-21.369083' where iso_key = 'RE';
update geoareas set bounding_min_long = '-0.090196', bounding_min_lat = '11.115601', bounding_max_long = '1.777907', bounding_max_lat = '6.089422' where iso_key = 'TG';
update geoareas set bounding_min_long = '2.686017', bounding_min_lat = '13.872849', bounding_max_long = '14.627149', bounding_max_lat = '4.277414' where iso_key = 'NG';
update geoareas set bounding_min_long = '19.977365', bounding_min_lat = '-17.787596', bounding_max_long = '29.364802', bounding_max_lat = '-26.854174' where iso_key = 'BW';
update geoareas set bounding_min_long = '8.703169', bounding_min_lat = '2.302185', bounding_max_long = '14.480595', bounding_max_lat = '-3.916298' where iso_key = 'GA';
update geoareas set bounding_min_long = '41.764666', bounding_min_lat = '12.708604', bounding_max_long = '43.409785', bounding_max_lat = '10.941038' where iso_key = 'DJ';
update geoareas set bounding_min_long = '43.226644', bounding_min_lat = '-11.368453', bounding_max_long = '44.526719', bounding_max_lat = '-12.368289' where iso_key = 'KM';
update geoareas set bounding_min_long = '6.468164', bounding_min_lat = '1.699121', bounding_max_long = '7.45229', bounding_max_lat = '0.047388' where iso_key = 'ST';
update geoareas set bounding_min_long = '11.721693', bounding_min_lat = '-16.967698', bounding_max_long = '25.2588', bounding_max_lat = '-28.93875' where iso_key = 'NA';
update geoareas set bounding_min_long = '-8.603578', bounding_min_lat = '10.724074', bounding_max_long = '-2.505862', bounding_max_lat = '4.351311' where iso_key = 'CI';
update geoareas set bounding_min_long = '12.213704', bounding_min_lat = '5.312131', bounding_max_long = '31.273988', bounding_max_lat = '-13.453804' where iso_key = 'CD';
update geoareas set bounding_min_long = '40.964404', bounding_min_lat = '11.983713', bounding_max_long = '51.390176', bounding_max_lat = '-1.695348' where iso_key = 'SO';
update geoareas set bounding_min_long = '57.31767', bounding_min_lat = '-19.989942', bounding_max_long = '57.791956', bounding_max_lat = '-20.513218' where iso_key = 'MU';
update geoareas set bounding_min_long = '29.014126', bounding_min_lat = '-2.312985', bounding_max_long = '30.811432', bounding_max_lat = '-4.455852' where iso_key = 'BI';
update geoareas set bounding_min_long = '32.67044', bounding_min_lat = '-9.394982', bounding_max_long = '35.892724', bounding_max_lat = '-17.131099' where iso_key = 'MW';
update geoareas set bounding_min_long = '0.16382', bounding_min_lat = '23.517867', bounding_max_long = '15.963191', bounding_max_lat = '11.696289' where iso_key = 'NE';
update geoareas set bounding_min_long = '-11.507535', bounding_min_lat = '8.53767', bounding_max_long = '-7.39993', bounding_max_lat = '4.351311' where iso_key = 'LR';
update geoareas set bounding_min_long = '9.310264', bounding_min_lat = '33.181954', bounding_max_long = '25.150486', bounding_max_lat = '19.49664' where iso_key = 'LY';
update geoareas set bounding_min_long = '72.349724', bounding_min_lat = '-7.220386', bounding_max_long = '72.498552', bounding_max_lat = '-7.43536' where iso_key = 'IO';
update geoareas set bounding_min_long = '28.85765', bounding_min_lat = '-1.063036', bounding_max_long = '30.876596', bounding_max_lat = '-2.808562' where iso_key = 'RW';
update geoareas set bounding_min_long = '29.561948', bounding_min_lat = '4.220208', bounding_max_long = '34.978206', bounding_max_lat = '-1.469936' where iso_key = 'UG';
update geoareas set bounding_min_long = '-14.414954', bounding_min_lat = '-7.882568', bounding_max_long = '-5.659701', bounding_max_lat = '-16.004036' where iso_key = 'SH';
update geoareas set bounding_min_long = '30.787506', bounding_min_lat = '-25.742976', bounding_max_long = '32.112903', bounding_max_lat = '-27.309961' where iso_key = 'SZ';
update geoareas set bounding_min_long = '19.510555', bounding_min_lat = '60.350273', bounding_max_long = '45.223111', bounding_max_lat = '-12.984995' where iso_key = 'YT';
update geoareas set bounding_min_long = '33.899978', bounding_min_lat = '5.492301', bounding_max_long = '41.884038', bounding_max_lat = '-4.692375' where iso_key = 'KE';
update geoareas set bounding_min_long = '30.221753', bounding_min_lat = '-10.464322', bounding_max_long = '40.844567', bounding_max_lat = '-26.861616' where iso_key = 'MZ';
update geoareas set bounding_min_long = '-17.535617', bounding_min_lat = '16.678905', bounding_max_long = '-11.382426', bounding_max_lat = '12.328033' where iso_key = 'SN';
update geoareas set bounding_min_long = '-5.523559', bounding_min_lat = '15.077866', bounding_max_long = '2.389188', bounding_max_lat = '9.424722' where iso_key = 'BF';
update geoareas set bounding_min_long = '-17.003065', bounding_min_lat = '35.9299', bounding_max_long = '-1.065537', bounding_max_lat = '21.420708' where iso_key = 'MA';
update geoareas set bounding_min_long = '14.43114', bounding_min_lat = '10.996228', bounding_max_long = '27.40332', bounding_max_lat = '2.270068' where iso_key = 'CF';
update geoareas set bounding_min_long = '16.447555', bounding_min_lat = '-22.146296', bounding_max_long = '37.887745', bounding_max_lat = '-46.96287' where iso_key = 'ZA';
update geoareas set bounding_min_long = '27.051713', bounding_min_lat = '-28.581718', bounding_max_long = '29.390692', bounding_max_lat = '-30.642315' where iso_key = 'LS';
update geoareas set bounding_min_long = '24.147395', bounding_min_lat = '12.223079', bounding_max_long = '35.268318', bounding_max_lat = '3.490717' where iso_key = 'SS';
update geoareas set bounding_min_long = '36.426748', bounding_min_lat = '18.005077', bounding_max_long = '43.116728', bounding_max_lat = '12.376583' where iso_key = 'ER';
update geoareas set bounding_min_long = '32.998896', bounding_min_lat = '14.852298', bounding_max_long = '47.978188', bounding_max_lat = '3.456094' where iso_key = 'ET';
update geoareas set bounding_min_long = '-3.243905', bounding_min_lat = '11.16689', bounding_max_long = '1.187246', bounding_max_lat = '4.762449' where iso_key = 'GH';
update geoareas set bounding_min_long = '8.532844', bounding_min_lat = '13.078504', bounding_max_long = '16.183436', bounding_max_lat = '1.676229' where iso_key = 'CM';
update geoareas set bounding_min_long = '-8.683366', bounding_min_lat = '37.092362', bounding_max_long = '11.96788', bounding_max_lat = '18.986645' where iso_key = 'DZ';
update geoareas set bounding_min_long = '-12.280614', bounding_min_lat = '24.995581', bounding_max_long = '4.234657', bounding_max_lat = '10.143257' where iso_key = 'ML';
update geoareas set bounding_min_long = '0.763344', bounding_min_lat = '12.383844', bounding_max_long = '3.834474', bounding_max_lat = '6.216778' where iso_key = 'BJ';
update geoareas set bounding_min_long = '11.743036', bounding_min_lat = '-4.428928', bounding_max_long = '24.046677', bounding_max_lat = '-18.019778' where iso_key = 'AO';
update geoareas set bounding_min_long = '55.383421', bounding_min_lat = '-4.55874', bounding_max_long = '55.542998', bounding_max_lat = '-4.785496' where iso_key = 'SC';
update geoareas set bounding_min_long = '43.257133', bounding_min_lat = '-12.079624', bounding_max_long = '50.482738', bounding_max_lat = '-25.570532' where iso_key = 'MG';
update geoareas set bounding_min_long = '-175.36234', bounding_min_lat = '-18.565326', bounding_max_long = '-173.921859', bounding_max_lat = '-21.450628' where iso_key = 'TO';
update geoareas set bounding_min_long = '-17.063992', bounding_min_lat = '27.285932', bounding_max_long = '-4.82262', bounding_max_lat = '14.745354' where iso_key = 'MR';
update geoareas set bounding_min_long = '-25.341548', bounding_min_lat = '17.19368', bounding_max_long = '-22.681892', bounding_max_lat = '14.818217' where iso_key = 'CV';

insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (1, 184, 'FR', 'France', 256546);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (2, 188, 'ES', 'Spain', 247147);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (3, 199, 'IT', 'Italy', 236868);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (4, 186, 'DE', 'Germany', 216733);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (5, 178, 'GB', 'United Kingdom', 201475);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (6, 9, 'US', 'United States of America', 200196);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (7, 183, 'NL', 'Netherlands', 165778);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (8, 197, 'GR', 'Greece', 158119);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (9, 180, 'AT', 'Austria', 149371);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (10, 174, 'BE', 'Belgium', 148077);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (11, 196, 'CH', 'Switzerland', 140577);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (12, 132, 'TR', 'Turkey', 128745);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (13, 219, 'CZ', 'Czech Republic', 120544);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (14, 191, 'PT', 'Portugal', 119744);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (15, 82, 'CA', 'Canada', 118059);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (16, 72, 'MX', 'Mexico', 117908);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (17, 117, 'TH', 'Thailand', 115444);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (18, 320, 'GB-ENG', 'England', 96626);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (19, 215, 'IE', 'Ireland', 93184);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (20, 194, 'VA', 'Vatican', 89923);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (21, 214, 'HU', 'Hungary', 89318);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (22, 182, 'HR', 'Croatia', 88683);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (23, 220, 'DK', 'Denmark', 88115);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (24, 207, 'SE', 'Sweden', 82360);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (25, 264, 'EG', 'Egypt', 79372);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (26, 176, 'PL', 'Poland', 78443);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (27, 120, 'AE', 'United Arab Emirates', 75773);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (28, 110, 'CN', 'China', 75578);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (29, 151, 'SG', 'Singapore', 73361);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (30, 247, 'AU', 'Australia', 70273);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (31, 114, 'ID', 'Indonesia', 64713);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (32, 149, 'HK', 'Hong Kong S.A.R.', 64032);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (33, 108, 'JP', 'Japan', 63571);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (34, 139, 'MY', 'Malaysia', 63368);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (35, 303, 'MA', 'Morocco', 62586);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (36, 190, 'NO', 'Norway', 61899);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (37, 216, 'LU', 'Luxembourg', 61298);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (38, 172, 'MC', 'Monaco', 60665);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (39, 179, 'FI', 'Finland', 53158);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (40, 128, 'VN', 'Vietnam', 51596);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (41, 217, 'RU', 'Russia', 51172);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (42, 208, 'CY', 'Cyprus', 50903);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (43, 154, 'IN', 'India', 50132);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (44, 97, 'DO', 'Dominican Republic', 49874);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (45, 71, 'BS', 'The Bahamas', 48089);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (46, 261, 'TN', 'Tunisia', 45567);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (47, 322, 'GB-SCT', 'Scotland', 45205);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (48, 209, 'SI', 'Slovenia', 45159);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (49, 169, 'BR', 'Brazil', 44288);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (50, 223, 'SK', 'Slovakia', 44270);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (51, 305, 'ZA', 'South Africa', 43729);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (52, 159, 'AR', 'Argentina', 42078);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (53, 198, 'BG', 'Bulgaria', 41189);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (54, 241, 'NZ', 'New Zealand', 39518);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (55, 112, 'KH', 'Cambodia', 39129);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (56, 109, 'KR', 'South Korea', 38978);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (57, 206, 'IS', 'Iceland', 38334);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (58, 2, 'JM', 'Jamaica', 38296);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (59, 181, 'MT', 'Malta', 37097);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (60, 101, 'CU', 'Cuba', 37060);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (61, 205, 'EE', 'Estonia', 33862);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (62, 134, 'IL', 'Israel', 33518);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (63, 62, 'PR', 'Puerto Rico', 33277);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (64, 74, 'CR', 'Costa Rica', 33056);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (65, 323, 'GB-WLS', 'Wales', 32742);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (66, 157, 'PE', 'Peru', 32520);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (67, 129, 'PH', 'Philippines', 30684);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (68, 165, 'CL', 'Chile', 28766);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (69, 187, 'RO', 'Romania', 28251);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (70, 210, 'ME', 'Montenegro', 27018);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (71, 211, 'LV', 'Latvia', 26811);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (72, 202, 'BA', 'Bosnia and Herzegovina', 25726);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (73, 122, 'TW', 'Taiwan', 25362);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (74, 136, 'LK', 'Sri Lanka', 25186);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (75, 162, 'CO', 'Colombia', 25032);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (76, 185, 'UA', 'Ukraine', 24582);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (77, 148, 'QA', 'Qatar', 24475);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (78, 299, 'KE', 'Kenya', 24249);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (79, 213, 'GI', 'Gibraltar', 24168);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (80, 152, 'JO', 'Jordan', 23821);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (81, 105, 'PA', 'Panama', 23580);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (82, 192, 'LI', 'Liechtenstein', 22947);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (83, 189, 'AD', 'Andorra', 22785);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (84, 64, 'KY', 'Cayman Islands', 22360);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (85, 200, 'RS', 'Serbia', 22261);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (86, 171, 'LT', 'Lithuania', 21597);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (87, 3, 'BB', 'Barbados', 21010);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (88, 102, 'VI', 'United States Virgin Islands', 20883);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (89, 6, 'BZ', 'Belize', 20103);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (90, 153, 'MO', 'Macau S.A.R', 19891);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (91, 271, 'TZ', 'Tanzania', 19771);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (92, 161, 'UY', 'Uruguay', 19604);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (93, 146, 'MV', 'Maldives', 19560);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (94, 76, 'MF', 'Saint Martin', 19195);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (95, 63, 'AW', 'Aruba', 18653);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (96, 127, 'OM', 'Oman', 18466);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (97, 8, 'LC', 'Saint Lucia', 17809);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (98, 287, 'MU', 'Mauritius', 17742);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (99, 147, 'LA', 'Laos', 17357);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (100, 175, 'SM', 'San Marino', 16392);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (101, 67, 'VG', 'British Virgin Islands', 16287);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (102, 167, 'EC', 'Ecuador', 15601);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (103, 98, 'HN', 'Honduras', 15521);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (104, 137, 'SA', 'Saudi Arabia', 15497);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (105, 103, 'GT', 'Guatemala', 15061);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (106, 230, 'FJ', 'Fiji', 14088);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (107, 65, 'SX', 'Sint Maarten', 13957);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (108, 142, 'NP', 'Nepal', 13828);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (109, 80, 'AG', 'Antigua and Barbuda', 13689);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (110, 4, 'CW', 'Curaçao', 13472);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (111, 140, 'MM', 'Myanmar', 13426);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (112, 160, 'BO', 'Bolivia', 13376);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (113, 158, 'PY', 'Paraguay', 12895);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (114, 212, 'BY', 'Belarus', 12670);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (115, 143, 'BH', 'Bahrain', 12301);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (116, 163, 'VE', 'Venezuela', 12277);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (117, 70, 'MQ', 'Martinique', 12266);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (118, 203, 'AL', 'Albania', 12106);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (119, 195, 'MK', 'Macedonia', 12012);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (120, 141, 'PS', 'Palestine', 11880);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (121, 81, 'BM', 'Bermuda', 11667);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (122, 268, 'ZW', 'Zimbabwe', 11630);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (123, 79, 'HT', 'Haiti', 11528);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (124, 99, 'TC', 'Turks and Caicos Islands', 11305);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (125, 138, 'LB', 'Lebanon', 11235);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (126, 75, 'KN', 'Saint Kitts and Nevis', 10627);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (127, 173, 'JE', 'Jersey', 10333);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (128, 118, 'KW', 'Kuwait', 10144);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (129, 201, 'GE', 'Georgia', 10050);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (130, 5, 'NI', 'Nicaragua', 10027);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (131, 277, 'BW', 'Botswana', 9645);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (132, 267, 'ZM', 'Zambia', 9614);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (133, 282, 'NA', 'Namibia', 9570);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (134, 106, 'GP', 'Guadeloupe', 9410);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (135, 319, 'CV', 'Cape Verde', 9393);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (136, 315, 'SC', 'Seychelles', 8832);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (137, 301, 'SN', 'Senegal', 8287);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (138, 113, 'KZ', 'Kazakhstan', 8197);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (139, 1, 'AN', 'Netherlands Antilles', 8100);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (140, 66, 'TT', 'Trinidad and Tobago', 8083);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (141, 131, 'IR', 'Iran', 7657);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (142, 221, 'IM', 'Isle of Man', 7635);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (143, 96, 'GD', 'Grenada', 7440);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (144, 231, 'PF', 'French Polynesia', 7379);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (145, 204, 'XK', 'Kosovo', 7200);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (146, 311, 'DZ', 'Algeria', 7080);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (147, 73, 'DM', 'Dominica', 7064);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (148, 308, 'ET', 'Ethiopia', 7052);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (149, 78, 'SV', 'El Salvador', 6943);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (150, 222, 'GG', 'Guernsey', 6882);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (151, 218, 'AZ', 'Azerbaijan', 6858);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (152, 309, 'GH', 'Ghana', 6740);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (153, 107, 'SY', 'Syria', 6714);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (154, 297, 'SZ', 'Swaziland', 6644);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (155, 295, 'UG', 'Uganda', 6568);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (156, 68, 'GU', 'Guam', 6495);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (157, 276, 'NG', 'Nigeria', 6460);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (158, 300, 'MZ', 'Mozambique', 6426);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (159, 155, 'PK', 'Pakistan', 6328);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (160, 237, 'NC', 'New Caledonia', 5922);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (161, 115, 'IQ', 'Iraq', 5839);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (162, 177, 'AM', 'Armenia', 5716);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (163, 224, 'MD', 'Moldova', 5552);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (164, 100, 'VC', 'Saint Vincent and the Grenadines', 5536);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (165, 135, 'BN', 'Brunei', 5396);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (166, 258, 'VU', 'Vanuatu', 5291);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (167, 116, 'MN', 'Mongolia', 5223);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (168, 133, 'AF', 'Afghanistan', 5208);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (169, 121, 'UZ', 'Uzbekistan', 5168);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (170, 274, 'RE', 'Reunion', 5147);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (171, 316, 'MG', 'Madagascar', 5020);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (172, 111, 'BD', 'Bangladesh', 4998);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (173, 170, 'AX', 'Aland', 4778);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (174, 145, 'KG', 'Kyrgyzstan', 4569);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (175, 273, 'GM', 'Gambia', 4358);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (176, 284, 'LS', 'Lesotho', 4166);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (177, 104, 'BL', 'Saint Barthelemy', 4143);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (178, 61, 'AI', 'Anguilla', 4071);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (179, 294, 'RW', 'Rwanda', 3950);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (180, 310, 'CM', 'Cameroon', 3891);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (181, 283, 'CI', 'Cote d''Ivoire', 3815);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (182, 314, 'AO', 'Angola', 3793);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (183, 193, 'GL', 'Greenland', 3699);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (184, 289, 'MW', 'Malawi', 3667);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (185, 227, 'AQ', 'Antarctica', 3644);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (186, 292, 'LY', 'Libya', 3292);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (187, 228, 'CK', 'Cook Islands', 3175);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (188, 125, 'BT', 'Bhutan', 3105);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (189, 238, 'PG', 'Papua New Guinea', 3052);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (190, 285, 'CD', 'Democratic Republic of the Congo', 3036);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (191, 130, 'KP', 'North Korea', 2901);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (192, 279, 'DJ', 'Djibouti', 2783);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (193, 225, 'FO', 'Faroe Islands', 2783);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (194, 123, 'YE', 'Yemen', 2763);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (195, 312, 'ML', 'Mali', 2724);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (196, 164, 'FK', 'Falkland Islands', 2717);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (197, 302, 'BF', 'Burkina Faso', 2694);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (198, 260, 'SD', 'Sudan', 2648);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (199, 313, 'BJ', 'Benin', 2632);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (200, 275, 'TG', 'Togo', 2492);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (201, 166, 'SR', 'Suriname', 2487);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (202, 156, 'GF', 'French Guiana', 2455);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (203, 144, 'TM', 'Turkmenistan', 2303);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (204, 124, 'TJ', 'Tajikistan', 2278);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (205, 242, 'WS', 'Samoa', 2225);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (206, 263, 'CG', 'Congo', 2203);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (207, 168, 'GY', 'Guyana', 2177);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (208, 278, 'GA', 'Gabon', 2119);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (209, 318, 'MR', 'Mauritania', 1974);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (210, 290, 'NE', 'Niger', 1929);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (211, 266, 'SL', 'Sierra Leone', 1913);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (212, 270, 'GN', 'Guinea', 1811);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (213, 317, 'TO', 'Tonga', 1788);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (214, 288, 'BI', 'Burundi', 1740);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (215, 119, 'PW', 'Palau', 1734);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (216, 304, 'CF', 'Central African Republic', 1731);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (217, 257, 'AS', 'American Samoa', 1702);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (218, 272, 'TD', 'Chad', 1640);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (219, 291, 'LR', 'Liberia', 1592);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (220, 77, 'MS', 'Montserrat', 1583);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (221, 150, 'FM', 'Micronesia', 1569);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (222, 286, 'SO', 'Somalia', 1472);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (223, 293, 'IO', 'British Indian Ocean Territory', 1461);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (224, 262, 'EH', 'Western Sahara', 1447);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (225, 306, 'SS', 'South Sudan', 1408);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (226, 126, 'TL', 'Timor', 1366);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (227, 244, 'SB', 'Solomon Islands', 1331);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (228, 269, 'GQ', 'Equatorial Guinea', 1287);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (229, 307, 'ER', 'Eritrea', 1258);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (230, 7, 'MP', 'Northern Mariana Islands', 1230);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (231, 298, 'YT', 'Mayotte', 1210);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (232, 265, 'GW', 'Guinea Bissau', 1160);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (233, 281, 'ST', 'Sao Tome and Principe', 1079);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (234, 280, 'KM', 'Comoros', 971);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (235, 259, 'SOL', 'Somaliland', 957);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (236, 246, 'MH', 'Marshall Islands', 926);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (237, 296, 'SH', 'Saint Helena', 855);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (238, 236, 'CX', 'Christmas Island', 802);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (239, 226, 'TF', 'French Southern and Antarctic Lands', 793);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (240, 234, 'NF', 'Norfolk Island', 772);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (241, 69, 'PM', 'Saint Pierre and Miquelon', 758);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (242, 239, 'KI', 'Kiribati', 749);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (243, 233, 'PN', 'Pitcairn Islands', 576);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (244, 232, 'WF', 'Wallis and Futuna', 561);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (245, 245, 'CC', 'Cocos (Keeling) Islands', 543);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (246, 240, 'TV', 'Tuvalu', 528);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (247, 229, 'NU', 'Niue', 503);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (248, 243, 'NR', 'Nauru', 492);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (249, 235, 'TK', 'Tokelau', 346);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (250, 324, 'SJ', 'Svalbard', 22);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (251, 327, 'GS', 'South Georgia and the Islands', 17);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (252, 326, 'BV', 'Bouvet Island', 8);
insert into popularity (rank, geo_area_id, geo_area_iso_key, geo_area_name, total) values (253, 325, 'HM', 'Heard Island and McDonald Islands',  6);


insert into adaptive_images(id, one_x_url, one_point_five_x_url, two_x_url, three_x_url, four_x_url) values (1, 'https://www.arrivinginhighheels.com/wp-content/uploads/countries/1.0x/N2.jpg', 'https://www.arrivinginhighheels.com/wp-content/uploads/countries/1.0x/N2.jpg', 'https://www.arrivinginhighheels.com/wp-content/uploads/countries/1.0x/N2.jpg', 'https://www.arrivinginhighheels.com/wp-content/uploads/countries/1.0x/N2.jpg', 'https://www.arrivinginhighheels.com/wp-content/uploads/countries/1.0x/N2.jpg');

-- insert details for US, for testing
insert into geoarea_details (geo_area_id, population, size, thumbnail_url_id, last_modification_time) values (9, 327720000, '9525067', 1, CURRENT_TIMESTAMP);

-- inserting edge case for the area details: vatican's size is smaller than 1 sq km
INSERT INTO geoarea_details (geo_area_id, population, size, thumbnail_url_id, last_modification_time) values (194, 1000, '0.44', 1, CURRENT_TIMESTAMP);

--must see's for US
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'New York', 9);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Miami', 9);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Los Angeles', 9);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Las Vegas', 9);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Walt Disney World', 9);

-- insert details and must see's for Brazil, for testing
insert into geoarea_details (geo_area_id, population, size, thumbnail_url_id, last_modification_time) values (169, 209498000, 8515767, 1, CURRENT_TIMESTAMP);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Rio de Janeiro', 169);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Sao Paulo', 169);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Salvador', 169);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Amazon', 169);
insert into geoarea_must_see (id, description, geo_area_id) values (nextval('geoarea_must_see_id_seq'), 'Carnival', 169);

-- insert top places for Brazil, for testing
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 9, 8777);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 159, 8098);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 184, 7342);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 199, 6278);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 188, 5823);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 178, 5822);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 165, 5161);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 186, 5132);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 191, 4996);
INSERT INTO top_places (id, lived_geo_area_id, been_geo_area_id, number_of_users_been) VALUES (nextval('top_places_id_seq'), 169, 158, 4806);

INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'fr', 'French');
INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'es', 'Spanish');
INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'it', 'Italian');
INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'pt', 'Portuguese');
INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'zh-Hant', 'Traditional Chinese');
INSERT INTO supported_languages (id, code, name) VALUES (nextval('supported_languages_id_seq'), 'zh-Hans', 'Simplified Chinese');

-- regions translations
INSERT INTO region_translations (id, name, region_id, supported_language_id) VALUES (nextval('region_translations_id_seq'), 'América do Norte', 1, 4);

-- countries translations
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'États-Unis', 9, 1);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Estados Unidos de América', 9, 2);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Estados Unidos da América', 9, 4);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), '美利堅合眾國', 9, 5);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), '美利坚合众国', 9, 6);

INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Argentina', 159, 4);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'França', 184, 4);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Itália', 199, 4);
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Espanha', 188, 4);

-- state translations
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Carolina do Sul', 56, 4);

-- cities translations
INSERT INTO geoarea_translations (id, name, geo_area_id, supported_language_id) VALUES (nextval('geoarea_translations_id_seq'), 'Nueva York', 325, 2);

-- must see's translations
INSERT INTO geoarea_must_see_translations (id, description, geo_area_must_see_id, supported_language_id) VALUES (nextval('geoarea_must_see_translations_id_seq'), 'Nueva York', 1, 2);
INSERT INTO geoarea_must_see_translations (id, description, geo_area_must_see_id, supported_language_id) VALUES (nextval('geoarea_must_see_translations_id_seq'), 'Miami', 2, 2);
INSERT INTO geoarea_must_see_translations (id, description, geo_area_must_see_id, supported_language_id) VALUES (nextval('geoarea_must_see_translations_id_seq'), 'Los Angeles', 3, 2);
INSERT INTO geoarea_must_see_translations (id, description, geo_area_must_see_id, supported_language_id) VALUES (nextval('geoarea_must_see_translations_id_seq'), 'Las Vegas', 4, 2);
INSERT INTO geoarea_must_see_translations (id, description, geo_area_must_see_id, supported_language_id) VALUES (nextval('geoarea_must_see_translations_id_seq'), 'El mundo de Walt Disney', 5, 2);

-- insert some airports, for testing
INSERT INTO airports (id, iata_code, last_modification_time, lat, long, name) values (1, 'JFK', '2017-01-01 00:00:00.0', '40.64', '-73.779', 'New York');
INSERT INTO airports (id, iata_code, last_modification_time, lat, long, name) values (2, 'FLN', '2017-01-01 00:00:00.0', '-27.672', '-48.548', 'Florianopolis');
INSERT INTO airports (id, iata_code, last_modification_time, lat, long, name) values (3, 'GIG', '2017-01-01 00:00:00.0', '-22.809', '-43.244', 'Rio de Janeiro');
INSERT INTO airports (id, iata_code, last_modification_time, lat, long, name) values (4, 'CGH', '2017-01-01 00:00:00.0', '-23.627', '-46.655', 'Sao Paulo');
INSERT INTO airports (id, iata_code, last_modification_time, lat, long, name) values (5, 'VCP', '2017-01-01 00:00:00.0', '-23.008', '-47.134', 'Campinas');

INSERT INTO cities (id, name, lat, long, last_modification_time) values (1, 'FLN', 'Florianopolis', '-27.672', '-48.548', '2017-01-01 00:00:00.0');
INSERT INTO airport_city_migration_mapping (airport_id, city_id) values (2, 1);

-- relate some of the airports to a geoarea
INSERT INTO airport_geo_areas (id, last_modification_time, airport_id, geo_area_id) VALUES (nextval('airport_geo_areas_id_seq'), '2017-01-01 00:00:00.0', 1, 9);
INSERT INTO airport_geo_areas (id, last_modification_time, airport_id, geo_area_id) VALUES (nextval('airport_geo_areas_id_seq'), '2017-01-01 00:00:00.0', 2, 169);
INSERT INTO airport_geo_areas (id, last_modification_time, airport_id, geo_area_id) VALUES (nextval('airport_geo_areas_id_seq'), '2017-01-01 00:00:00.0', 3, 169);
INSERT INTO airport_geo_areas (id, last_modification_time, airport_id, geo_area_id) VALUES (nextval('airport_geo_areas_id_seq'), '2017-01-01 00:00:00.0', 4, 169);
INSERT INTO airport_geo_areas (id, last_modification_time, airport_id, geo_area_id) VALUES (nextval('airport_geo_areas_id_seq'), '2017-01-01 00:00:00.0', 5, 169);

-- insert translation for the New York airport to test for this feature
INSERT INTO airport_translations (id, name, airport_id, supported_language_id) VALUES (nextval('airport_translations_id_seq'), 'Nueva York', 1, 2);