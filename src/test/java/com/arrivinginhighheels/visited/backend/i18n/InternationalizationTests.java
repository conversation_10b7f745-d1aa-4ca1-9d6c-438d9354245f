package com.arrivinginhighheels.visited.backend.i18n;

import com.arrivinginhighheels.visited.backend.dto.AreaDTO;
import com.arrivinginhighheels.visited.backend.dto.AreaDetailsDto;
import com.arrivinginhighheels.visited.backend.dto.RegionDTO;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.Locale;
import java.util.stream.Stream;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AREAS_URL;
import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.LANGUAGES_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static java.lang.Boolean.TRUE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class InternationalizationTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testSimpleLocaleChange() {
        //test default
        ResponseEntity<String> languageResp =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(LANGUAGES_URL + "/current", String.class);

        assertThat(languageResp.getStatusCode(), is(HttpStatus.OK));
        assertThat(languageResp.getBody(), is("en"));

        //test simple change to french
        languageResp =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage(Locale.FRANCE.getLanguage())
                          .get(LANGUAGES_URL + "/current", String.class);

        assertThat(languageResp.getStatusCode(), is(HttpStatus.OK));
        assertThat(languageResp.getBody(), is("fr"));
    }

    @Test
    public void testGettingAnAreaNameInPortuguese() {
        //when: request all the areas
        ResponseEntity<AreaDTO> respArea =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .setLanguage("pt")
                        .get(AREAS_URL + "/US", AreaDTO.class);

        //then:
        assertThat(respArea.getStatusCode(), is(HttpStatus.OK));

        //get US to test the return
        AreaDTO usArea = respArea.getBody();
        assertThat(usArea.getName(), is("Estados Unidos da América"));
    }

    @Test
    public void testGettingAnAreaNameInTraditionalChinese() {
        //when: request all the areas
        ResponseEntity<AreaDTO> respArea =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .setLanguage("zh-Hant")
                        .get(AREAS_URL + "/US", AreaDTO.class);

        //then:
        assertThat(respArea.getStatusCode(), is(HttpStatus.OK));

        //get US to test the return
        AreaDTO usArea = respArea.getBody();
        assertThat(usArea.getName(), is("美利堅合眾國"));
    }

    @Test
    public void testGettingAnAreaNameInSimplifiedChinese() {
        //when: request all the areas
        ResponseEntity<AreaDTO> respArea =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .setLanguage("zh-Hans")
                        .get(AREAS_URL + "/US", AreaDTO.class);

        //then:
        assertThat(respArea.getStatusCode(), is(HttpStatus.OK));

        //get US to test the return
        AreaDTO usArea = respArea.getBody();
        assertThat(usArea.getName(), is("美利坚合众国"));
    }

    @Test
    public void testGettingAreasAndRegionsNamesInPortuguese() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage("pt")
                          .get(AREAS_URL, RegionDTO[].class);

        //then:
        assertThat(respRegions.getStatusCode(), is(HttpStatus.OK));

        RegionDTO[] regions = respRegions.getBody();

        //get the North America region to test
        RegionDTO northAmericaRegion = regions[0];
        assertThat(northAmericaRegion.getName(), is("América do Norte"));

        List<AreaDTO> northAmericanAreas = northAmericaRegion.getAreas();

        //get US to test the return
        AreaDTO usArea = northAmericanAreas.get(8);
        assertThat(usArea.getName(), is("Estados Unidos da América"));
    }

    @Test
    public void testGettingAnAreasDetailsInSpanish() {
        //when: I get an area's details in spanish
        ResponseEntity<AreaDetailsDto> respAreaInfo =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage("es")
                          .get(AREAS_URL + "/US/details", AreaDetailsDto.class);

        //then:
        assertThat(respAreaInfo.getStatusCode(), is(HttpStatus.OK));

        AreaDetailsDto usDetails = respAreaInfo.getBody();
        assertThat(usDetails.name(), is("Estados Unidos de América"));

        List<String> mustSee = usDetails.mustSee();
        assertThat(mustSee.get(0), is("Nueva York"));
        assertThat(mustSee.get(1), is("Miami"));
        assertThat(mustSee.get(2), is("Los Angeles"));
        assertThat(mustSee.get(3), is("Las Vegas"));
        assertThat(mustSee.get(4), is("El mundo de Walt Disney"));
    }

    @Test
    public void testGettingASubdivisionForUSInPortuguese() {
        //when: request all the subdivisions of the US area
        ResponseEntity<AreaDTO[]> respSubdivisions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage("pt")
                          .get(AREAS_URL + "/US/subdivisions", AreaDTO[].class);

        //then:
        assertThat(respSubdivisions.getStatusCode(), is(HttpStatus.OK));

        boolean hasSubdivisionSouthCarolinaWithNameTranslatedToPortuguese =
                Stream.of(respSubdivisions.getBody())
                      .anyMatch(a -> a.getName().equalsIgnoreCase("Carolina do Sul"));
        assertThat(hasSubdivisionSouthCarolinaWithNameTranslatedToPortuguese, is(TRUE));
    }

    @Test
    public void testGettingACityNameInSpanish() {
        //when: I request all the cities of the New York area
        ResponseEntity<AreaDTO[]> respCities =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage("es")
                          .get(AREAS_URL + "/US-NY/cities", AreaDTO[].class);

        //then:
        assertThat(respCities.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] cities = respCities.getBody();
        AreaDTO nyCity = Stream.of(cities)
                               .filter(c -> c.getIso().equals("KJFK"))
                               .findFirst()
                               .get();
        assertThat(nyCity.getId(), is(notNullValue()));
        assertThat(nyCity.getName(), is("Nueva York"));
        assertThat(nyCity.getType(), is("CITY"));
    }

    @Test
    public void testGettingTopPlacesForBrazilAreaInPortuguese() {
        //when: request top places for Brazil
        ResponseEntity<AreaDTO[]> respTopPlaces =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .setLanguage("pt")
                          .get(AREAS_URL + "/BR/topPlaces", AreaDTO[].class);

        //then:
        assertThat(respTopPlaces.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] topPlaces = respTopPlaces.getBody();
        assertThat(topPlaces, is(notNullValue()));
        assertThat(topPlaces.length, is(10)); //query limit

        //test the top places order
        assertThat(topPlaces[0].getName(), is("Estados Unidos da América"));
        assertThat(topPlaces[1].getName(), is("Argentina"));
        assertThat(topPlaces[2].getName(), is("França"));
        assertThat(topPlaces[3].getName(), is("Itália"));
        assertThat(topPlaces[4].getName(), is("Espanha"));
    }

    @Test
    public void testFallBackToEnglish() {
        //when: I request all the cities of the New York area
        ResponseEntity<AreaDTO[]> respCities =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .setLanguage("epo") //esperanto... not available in the test db!
                        .get(AREAS_URL + "/US-NY/cities", AreaDTO[].class);

        //then:
        assertThat(respCities.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] cities = respCities.getBody();
        AreaDTO nyCity = Stream.of(cities)
                .filter(c -> c.getId().equals(325L)) //new york
                .findFirst()
                .get();
        assertThat(nyCity.getName(), is("New York")); //should fallback to english
    }
}
