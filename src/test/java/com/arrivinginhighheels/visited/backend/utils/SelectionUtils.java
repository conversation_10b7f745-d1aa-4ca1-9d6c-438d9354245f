package com.arrivinginhighheels.visited.backend.utils;

import com.arrivinginhighheels.visited.backend.dto.RankDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.dto.UsersDTO;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.ANDROID_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.ADMIN_USER;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Utility class for common actions for the geoarea selection behaviour
 */
public class SelectionUtils {

    public static void clearUserSelections(SecureAPIRequestBuilder securedAPI, String userEmail) {
        ResponseEntity<SelectionsDTO> resp =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                        .put(RESET_USER_SELECTIONS_URL.replace("{username}", userEmail),null, SelectionsDTO.class);
        assertThat(resp.getStatusCode(), is(HttpStatus.ACCEPTED));
    }

    public static void clearUserLogs(SecureAPIRequestBuilder securedAPI, String userEmail) {
        ResponseEntity<SelectionsDTO> resp =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                        .put(RESET_USER_SELECTION_LOGS_URL.replace("{username}", userEmail), null, SelectionsDTO.class);
        assertThat(resp.getStatusCode(), is(HttpStatus.ACCEPTED));
    }

    public static SelectionsDTO selectAreaForTheUser(SecureAPIRequestBuilder securedAPI, String userEmail,
                                               String areaIsoKey, SelectionType type) {
        SelectionDTO selection = new SelectionDTO(areaIsoKey, type);
        ResponseEntity<SelectionsDTO> resp =
                securedAPI.withCredentials(userEmail, IOS_PLATFORM)
                        .post(SELECT_URL, selection, SelectionsDTO.class);

        return resp.getBody();
    }

    public static SelectionsDTO selectAreaForTheLoggedInUser(SecureAPIRequestBuilder securedAPI, String areaIsoKey,
                                                       SelectionType type) {
        SelectionDTO selection = new SelectionDTO(areaIsoKey, type);
        ResponseEntity<SelectionsDTO> resp =
                securedAPI.withLoggedUser()
                        .post(SELECT_URL, selection, SelectionsDTO.class);

        return resp.getBody();
    }

    public static void createNewUser(TestRestTemplate restTemplate, String newuserEmail) {
        //when: create a new user
        var tokenResponse =
                restTemplate.postForObject(SIGNUP_URL,
                        new AuthRequest(newuserEmail, ANDROID_PLATFORM), TokenResponseWithStats.class);

        //then: its collections of selections should be empty
        assertThat(tokenResponse.token(), is(notNullValue()));
        assertThat(tokenResponse.selections(), is(notNullValue()));
        assertThat(tokenResponse.selections().getLivedAreas(), is(notNullValue()));
        assertThat(tokenResponse.selections().getLivedAreas().size(), is(0));
        assertThat(tokenResponse.selections().getBeenAreas(), is(notNullValue()));
        assertThat(tokenResponse.selections().getBeenAreas().size(), is(0));
        assertThat(tokenResponse.selections().getWantAreas(), is(notNullValue()));
        assertThat(tokenResponse.selections().getWantAreas().size(), is(0));
    }

    public static Long clearAllUsersSelections(SecureAPIRequestBuilder securedAPI) {
        //when: get all the users as admin
        ResponseEntity<UsersDTO> response =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                          .get(USERS_URL, UsersDTO.class);

        //then:
        assertThat(response.getStatusCode(), is(HttpStatus.OK));
        assertThat(response.getBody(), is(notNullValue()));
        assertThat(response.getBody().getUsers(), is(notNullValue()));
        assertThat(response.getBody().getUsers().isEmpty(), is(false));

        //finally: call clear selections in every user
        List<UserDTO> users = response.getBody().getUsers();
        users.forEach(user -> {
            clearUserSelections(securedAPI, user.getEmail());
        });
        users.forEach(user -> {
            securedAPI.withLoggedUser()
                    .get(RANK_URL, RankDTO.class);
        });

        return Long.valueOf(users.size());
    }
}
