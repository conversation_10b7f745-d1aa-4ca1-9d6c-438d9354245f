package com.arrivinginhighheels.visited.backend.domain;

import com.arrivinginhighheels.visited.backend.model.OSType;
import com.arrivinginhighheels.visited.backend.model.Platform;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 18/01/17.
 */
public interface TestConstants {

    interface Platforms {
        Platform IOS_PLATFORM = new Platform(OSType.iOS, "2.1.1");
        Platform ANDROID_PLATFORM = new Platform(OSType.Android, "5.0");
        Platform WEB_PLATFORM = new Platform(OSType.WEB, "2.0");
    }

    interface Users {
        UserDTO ADMIN_USER = new UserDTO("<EMAIL>", new Date());
        UserDTO REGULAR_USER = new UserDTO("<EMAIL>", new Date());
        UserDTO NEW_USER = new UserDTO("<EMAIL>", new Date());
        UserDTO CANADIAN_USER = new UserDTO("<EMAIL>", new Date());
        UserDTO BRITISH_USER = new UserDTO("<EMAIL>", new Date());
    }

    interface GeoAreasIsoKeys {

        interface Countries {
            String BRAZIL_ISO_KEY = "BR";
            String USA_ISO_KEY = "US";
            String CANADA_ISO_KEY = "CA";
            String MADAGASCAR_ISO_KEY = "MG";
            String IRELAND_ISO_KEY = "IE";
            String ITALY_ISO_KEY = "IT";
            String SPAIN_ISO_KEY = "ES";
            String GERMANY_ISO_KEY = "DE";
            String PORTUGAL_ISO_KEY = "PT";
            String UNITED_KINGDOM_ISO_KEY = "GB";
        }

        interface States {
            String USA_NY_ISO_KEY = "US-NY";
            String USA_TX_ISO_KEY = "US-TX";
            String USA_KS_ISO_KEY = "US-KS";

            String CANADA_AB_ISO_KEY = "CA-AB";
            String CANADA_QC_ISO_KEY = "CA-QC";
            String CANADA_ON_ISO_KEY = "CA-ON";

            String UK_ENGLAND_ISO_KEY = "GB-ENG";
        }

        interface Cities {
            String USA_NY_NEW_YORK_ISO_KEY = "KJFK";
        }
    }
}
