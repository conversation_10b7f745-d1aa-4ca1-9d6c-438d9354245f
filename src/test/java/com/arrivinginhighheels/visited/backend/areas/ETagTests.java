package com.arrivinginhighheels.visited.backend.areas;

import com.arrivinginhighheels.visited.backend.dto.AreaDTO;
import com.arrivinginhighheels.visited.backend.dto.RegionDTO;
import com.arrivinginhighheels.visited.backend.model.AreaDetails;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.apache.http.HttpHeaders;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AREAS_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ETagTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testETagPresenceInTheResponseHeaders() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL, RegionDTO[].class);

        //then:
        assertThat(respRegions.getHeaders().get(HttpHeaders.ETAG), is(notNullValue()));
    }

    @Test
    public void testUsageOfETagInTheAreasApiMethod() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL, RegionDTO[].class);

        //then:
        assertThat(respRegions.getHeaders().get(HttpHeaders.ETAG), is(notNullValue()));
        List<String> etags = respRegions.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .ifNoneMatch(etags.get(0))
                        .get(AREAS_URL, RegionDTO[].class);
        //then: should return a 304 status with an empty response body
        assertThat(respRegions.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respRegions.getBody(), is(nullValue()));
    }

    @Test
    public void testETagMatchCausingA304ResponseWithEmptyBodyInTheAreasMethod() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL, RegionDTO[].class);

        //then:
        List<String> etags = respRegions.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        //when: I send the request again, but this time informing the etag in the request headers...
        respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .ifNoneMatch(etags.get(0))
                        .get(AREAS_URL, RegionDTO[].class);

        //then: should return a 304 status with an empty response body
        assertThat(respRegions.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respRegions.getBody(), is(nullValue()));
    }

    @Test
    public void testETagMatchCausingA304ResponseWithEmptyBodyInTheAreasDetailsMethod() {
        //when: request all the areas
        ResponseEntity<AreaDetails> respAreaDetails =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL + "/BR", AreaDetails.class);

        //then:
        List<String> etags = respAreaDetails.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        //when: I send the request again, but this time informing the etag in the request headers...
        respAreaDetails =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .ifNoneMatch(etags.get(0))
                          .get(AREAS_URL + "/BR", AreaDetails.class);

        //then: should return a 304 status with an empty response body
        assertThat(respAreaDetails.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respAreaDetails.getBody(), is(nullValue()));
    }

    @Test
    public void testETagMatchCausingA304ResponseWithEmptyBodyInTheSubdivisionsMethod() {
        //when: request all the areas
        ResponseEntity<AreaDTO[]> respSubdivisions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US/subdivisions", AreaDTO[].class);

        //then:
        List<String> etags = respSubdivisions.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        //when: I send the request again, but this time informing the etag in the request headers...
        respSubdivisions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .ifNoneMatch(etags.get(0))
                        .get(AREAS_URL + "/US/subdivisions", AreaDTO[].class);

        //then: should return a 304 status with an empty response body
        assertThat(respSubdivisions.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respSubdivisions.getBody(), is(nullValue()));
    }

    @Test
    public void testETagMatchCausingA304ResponseWithEmptyBodyInTheTopPlacesMethod() {
        //when: request all the areas
        ResponseEntity<AreaDTO[]> respTopPlaces =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US/topPlaces", AreaDTO[].class);

        //then:
        List<String> etags = respTopPlaces.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        //when: I send the request again, but this time informing the etag in the request headers...
        respTopPlaces =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .ifNoneMatch(etags.get(0))
                        .get(AREAS_URL + "/US/topPlaces", AreaDTO[].class);

        //then: should return a 304 status with an empty response body
        assertThat(respTopPlaces.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respTopPlaces.getBody(), is(nullValue()));
    }

    @Test
    public void testETagMatchCausingA304ResponseWithEmptyBodyInTheCitiesMethod() {
        //when: request all the areas
        ResponseEntity<AreaDTO[]> respCities =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US-NY/cities", AreaDTO[].class);

        //then:
        List<String> etags = respCities.getHeaders().get(HttpHeaders.ETAG);
        assertThat(etags, is(notNullValue()));
        assertThat(etags.size(), is(1));

        //when: I send the request again, but this time informing the etag in the request headers...
        respCities =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .ifNoneMatch(etags.get(0))
                        .get(AREAS_URL + "/US-NY/cities", AreaDTO[].class);

        //then: should return a 304 status with an empty response body
        assertThat(respCities.getStatusCode(), is(HttpStatus.NOT_MODIFIED));
        assertThat(respCities.getBody(), is(nullValue()));
    }

    @Test
    public void testIfChangingTheLanguageChangesTheEtag() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respAreas =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL, RegionDTO[].class);
        List<String> etags = respAreas.getHeaders().get(HttpHeaders.ETAG);

        //and: I send the request again, but this time informing the etag in the request headers, but changing the language also...
        respAreas =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .ifNoneMatch(etags.get(0))
                          .setLanguage("fr")
                          .get(AREAS_URL, RegionDTO[].class);

        //then: should return a 200 status with an non-empty response body
        assertThat(respAreas.getStatusCode(), is(HttpStatus.OK));
        assertThat(respAreas.getBody(), is(notNullValue()));
    }

}
