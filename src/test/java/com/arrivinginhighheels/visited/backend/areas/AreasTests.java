package com.arrivinginhighheels.visited.backend.areas;

import com.arrivinginhighheels.visited.backend.dto.*;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.stream.Stream;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.AREAS_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AreasTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testGetAllAreas() {
        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL, RegionDTO[].class);

        //then:
        assertThat(respRegions.getStatusCode(), is(HttpStatus.OK));

        RegionDTO[] regions = respRegions.getBody();
        assertThat(regions, is(notNullValue()));
        assertThat(regions.length, is(7));

        //get the North America region to test
        RegionDTO northAmericaRegion = regions[0];
        assertThat(northAmericaRegion.getId(), is(1L));
        assertThat(northAmericaRegion.getName(), is("North America"));

        List<AreaDTO> northAmericanAreas = northAmericaRegion.getAreas();
        assertThat(northAmericanAreas, is(notNullValue()));
        assertThat(northAmericanAreas.size(), is(42));

        //get US to test the return
        AreaDTO usArea = northAmericanAreas.get(8);

        assertUSAreaInformation(usArea);
    }

    @Test
    public void testGetUsAreaOnly() {
        //when: request all the areas
        ResponseEntity<AreaDTO> respArea =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US", AreaDTO.class);

        //then:
        assertThat(respArea.getStatusCode(), is(HttpStatus.OK));

        //get US to test the return
        AreaDTO usArea = respArea.getBody();

        //assert all US info
        assertUSAreaInformation(usArea);
    }

    @Test
    public void testGetAnAreasInfo() {
        //when: I get an area's details
        ResponseEntity<AreaDetailsDto> respAreaInfo =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US/details", AreaDetailsDto.class);

        //then:
        assertThat(respAreaInfo.getStatusCode(), is(HttpStatus.OK));

        AreaDetailsDto usDetails = respAreaInfo.getBody();
        assertThat(usDetails.id(), is(9L));
        assertThat(usDetails.isoCode(), is("US"));
        assertThat(usDetails.type(), is("COUNTRY"));
        assertThat(usDetails.name(), is("United States of America"));
        assertThat(usDetails.popularity(), is(6));
        assertThat(usDetails.size(), is(9525067d));
        assertThat(usDetails.population(), is(327720000L));

        List<String> mustSee = usDetails.mustSee();
        assertThat(mustSee, is(notNullValue()));
        assertThat(mustSee.isEmpty(), is(false));

        assertThat(mustSee.get(0), is("New York"));
        assertThat(mustSee.get(1), is("Miami"));
        assertThat(mustSee.get(2), is("Los Angeles"));
        assertThat(mustSee.get(3), is("Las Vegas"));
        assertThat(mustSee.get(4), is("Walt Disney World"));
    }

    @Test
    public void testGetVaticansAreasInfo() {
        //when: I get an area's details
        ResponseEntity<AreaDetailsDto> respAreaInfo =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/VA/details", AreaDetailsDto.class);

        //then:
        assertThat(respAreaInfo.getStatusCode(), is(HttpStatus.OK));

        AreaDetailsDto vaticanDetails = respAreaInfo.getBody();
        assertThat(vaticanDetails.id(), is(194L));
        assertThat(vaticanDetails.isoCode(), is("VA"));
        assertThat(vaticanDetails.type(), is("COUNTRY"));
        assertThat(vaticanDetails.name(), is("Vatican"));
        assertThat(vaticanDetails.size(), is(0.44d));
        assertThat(vaticanDetails.population(), is(1000L));
    }

    @Test
    public void testGetAllSubdivisionsForUS() {
        //when: request all the subdivisions of the US area
        ResponseEntity<AreaDTO[]> respSubdivisions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/US/subdivisions", AreaDTO[].class);

        //then:
        assertThat(respSubdivisions.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] subdivisions = respSubdivisions.getBody();
        assertThat(subdivisions, is(notNullValue()));
        assertThat(subdivisions.length, is(51));

        //get US-CO to test the return
        AreaDTO coloradoArea = subdivisions[0];
        assertThat(coloradoArea.getId(), is(10L));
        assertThat(coloradoArea.getIso(), is("US-CO"));
        assertThat(coloradoArea.getName(), is("Colorado"));
        assertThat(coloradoArea.getType(), is("STATE"));
//        assertThat(coloradoArea.getFlag(), is(notNullValue())); //TODO assert a real url for a flag

        //test US Label
        List<AreaLabelDTO> coloradoLabels = coloradoArea.getLabels();
        assertThat(coloradoLabels, is(notNullValue()));
        assertThat(coloradoLabels.isEmpty(), is(Boolean.FALSE));

        AreaLabelDTO usLabel = coloradoLabels.get(0);
        assertThat(usLabel.getId(), is(notNullValue()));
        assertThat(usLabel.getResolution(), is(2));
        assertThat(usLabel.getCoordinate(), is(new Double[] { -105.5466527982642, 38.99804627131091 }));

        //test US bounds
        assertThat(coloradoArea.getBounds(), is(new Double[] { -109.058934, 41.003906, -102.042974, 36.994786 }));

        //test existence of US ETags
        ETagsDTO coloradoAreaETags = coloradoArea.getETags();
        assertThat(coloradoAreaETags, is(notNullValue()));
        assertThat(coloradoAreaETags.getSubdivisions(), is(nullValue()));
        assertThat(coloradoAreaETags.getInfo(), is(nullValue()));
    }

    @Test
    public void getAllCitiesForTheColoradoArea() {
        //when: I request all the cities of the Colorado area
        ResponseEntity<AreaDTO[]> respCities =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL + "/US-CO/cities", AreaDTO[].class);

        //then:
        assertThat(respCities.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] cities = respCities.getBody();
        assertThat(cities, is(notNullValue()));
        assertThat(cities.length, is(1));

        //get US-CO to test the return
        AreaDTO durangoCity = cities[0];
        assertThat(durangoCity.getId(), is(notNullValue()));
        assertThat(durangoCity.getIso(), is("KDRO"));
        assertThat(durangoCity.getName(), is("Durango"));
        assertThat(durangoCity.getType(), is("CITY"));
//        assertThat(durangoCity.getFlag(), is(notNullValue())); //TODO assert a real url for a flag

        ETagsDTO durangoAreaETags = durangoCity.getETags();
        assertThat(durangoAreaETags, is(notNullValue()));
        assertThat(durangoAreaETags.getSubdivisions(), is(nullValue()));
        assertThat(durangoAreaETags.getInfo(), is(nullValue()));
    }

    @Test
    public void testIfCitiesAreNotTreatedAsNormalSubdivisions() {
        //test this by asserting Brazil's data in the getAllAreas, since it has cities but doesn't have any other types of subdivisions
        //in the etags it should have: infos, cities, but no subdivisions

        //when: request all the areas
        ResponseEntity<RegionDTO[]> respRegions =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL, RegionDTO[].class);

        //and get Brazil:
        RegionDTO southAmerica = Stream.of(respRegions.getBody())
                .filter(reg -> reg.getName().equalsIgnoreCase("South America"))
                .findFirst()
                .get();

        AreaDTO brazil =
                southAmerica.getAreas().stream()
                        .filter(areaDTO -> areaDTO.getName().equalsIgnoreCase("Brazil"))
                        .findFirst()
                        .get();

        //then:
        assertThat(brazil, is(notNullValue()));
        assertThat(brazil.getETags(), is(notNullValue()));
        assertThat(brazil.getETags().getSubdivisions(), is(nullValue()));
        assertThat(brazil.getETags().getInfo(), is(notNullValue()));
    }

    @Test
    public void testTopPlacesForBrazilArea() {
        //when: request top places for Brazil
        ResponseEntity<AreaDTO[]> respTopPlaces =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(AREAS_URL + "/BR/topPlaces", AreaDTO[].class);

        //then:
        assertThat(respTopPlaces.getStatusCode(), is(HttpStatus.OK));

        AreaDTO[] topPlaces = respTopPlaces.getBody();
        assertThat(topPlaces, is(notNullValue()));
        assertThat(topPlaces.length, is(10)); //query limit

        //test the top places order
        AreaDTO usArea = topPlaces[0];

        // test full format for this area
        assertUSAreaInformation(usArea);

        //test only basic data from the other areas, just to test order.
        AreaDTO argentinaArea = topPlaces[1];
        assertThat(argentinaArea.getName(), is("Argentina"));

        AreaDTO franceArea = topPlaces[2];
        assertThat(franceArea.getName(), is("France"));

        AreaDTO italyArea = topPlaces[3];
        assertThat(italyArea.getName(), is("Italy"));

        AreaDTO spainArea = topPlaces[4];
        assertThat(spainArea.getName(), is("Spain"));
    }

    @Test
    public void testAddingUserNotesToAnArea() {
        //when: I request an area's notes for the Regular user:
        ResponseEntity<AreaUserNotesDTO> respUserNotes =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(AREAS_URL + "/BR/notes", AreaUserNotesDTO.class);

        //then, should return 404, since no notes were added yet:
        assertThat(respUserNotes.getStatusCode(), is(HttpStatus.NOT_FOUND));

        //when: I add some notes
        AreaUserNotesDTO areaUserNotesDTO = new AreaUserNotesDTO().notes("Testing adding notes.");
        ResponseEntity<AreaUserNotesDTO> respPostUserNotes =
                securedAPI.withLoggedUser()
                          .post(AREAS_URL + "/BR/notes", areaUserNotesDTO, AreaUserNotesDTO.class);

        //then:
        assertThat(respPostUserNotes.getStatusCode(), is(HttpStatus.OK));
        AreaUserNotesDTO userNotes = respPostUserNotes.getBody();
        assertThat(userNotes.getNotes(), is("Testing adding notes."));

        //when: I get the notes again
        respUserNotes =
                securedAPI.withLoggedUser()
                          .get(AREAS_URL + "/BR/notes", AreaUserNotesDTO.class);

        //then: should return OK with the notes added before.
        assertThat(respUserNotes.getStatusCode(), is(HttpStatus.OK));

        userNotes = respUserNotes.getBody();
        assertThat(userNotes.getNotes(), is("Testing adding notes."));
    }

    private void assertUSAreaInformation(AreaDTO usArea) {
        assertThat(usArea.getId(), is(9L));
        assertThat(usArea.getIso(), is("US"));
        assertThat(usArea.getName(), is("United States of America"));
        assertThat(usArea.getType(), is("COUNTRY"));
//        assertThat(usArea.getFlag(), is(notNullValue()));

        //test US Label
        List<AreaLabelDTO> usLabels = usArea.getLabels();
        assertThat(usLabels, is(notNullValue()));
        assertThat(usLabels.isEmpty(), is(Boolean.FALSE));

        AreaLabelDTO usLabel = usLabels.get(0);
        assertThat(usLabel.getId(), is(notNullValue()));
        assertThat(usLabel.getResolution(), is(0));
        assertThat(usLabel.getCoordinate(), is(new Double[] {-100.038, 39.94755}));

        //test US bounds
        assertThat(usArea.getBounds(), is(new Double[] {-178.194518, 71.407683, 179.779962, 18.963907}));

        //test existence of US ETags
        ETagsDTO usETags = usArea.getETags();
        assertThat(usETags, is(notNullValue()));
        assertThat(usETags.getSubdivisions(), is(notNullValue()));
        assertThat(usETags.getInfo(), is(notNullValue()));
    }
}
