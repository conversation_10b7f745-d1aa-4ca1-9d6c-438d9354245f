package com.arrivinginhighheels.visited.backend.selection;

import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNIN_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.*;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the state/subdivision selection behaviour of the API
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SelectStateTests {

    public static final String NEWUSER_EMAIL = "<EMAIL>";

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testSelectionOfAStateWithLived() {
        testSelectionOfThisStateAsLived(USA_TX_ISO_KEY, USA_ISO_KEY);
    }

    @Test
    public void testSelectionOfAUnitedKingdomStateWithLived() {
        testSelectionOfThisStateAsLived(UK_ENGLAND_ISO_KEY, UNITED_KINGDOM_ISO_KEY);
    }

    private void testSelectionOfThisStateAsLived(String theStatesIsoKey, String theCountrysIsoKey) {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), theStatesIsoKey, SelectionType.LIVED);

        //then:
        assertThat(userSelections.getLivedAreas(), is(notNullValue()));
        assertThat(userSelections.getLivedAreas().size(), is(2)); //The country and the state should be there
        assertThat(userSelections.getLivedAreas(), hasItem(theCountrysIsoKey));
        assertThat(userSelections.getLivedAreas(), hasItem(theStatesIsoKey));
    }

    @Test
    public void testThatOnlyOneCountryAndSubdivisionCanBeSelectedAsLived() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //if a subdivision is selected as live, its country must be selected as live as well
        // and only one subdivision of the country can be selected

        //when:
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, SelectionType.LIVED);
        selectAreaForTheLoggedInUser(securedAPI, CANADA_QC_ISO_KEY, SelectionType.LIVED);
        SelectionsDTO selections = selectAreaForTheLoggedInUser(securedAPI, USA_TX_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(selections.getLivedAreas().size(), is(2)); //only one country and a state as lived should ne there
        assertThat(selections.getLivedAreas(), hasItem(USA_ISO_KEY));
        assertThat(selections.getLivedAreas(), hasItem(USA_TX_ISO_KEY));

        //and: checks that the old lived countries should not show  in any list
        assertThat(selections.getLivedAreas(), not(hasItem(BRAZIL_ISO_KEY)));
        assertThat(selections.getLivedAreas(), not(hasItem(CANADA_ISO_KEY)));
        assertThat(selections.getLivedAreas(), not(hasItem(CANADA_QC_ISO_KEY)));
        assertThat(selections.getBeenAreas(), not(hasItem(BRAZIL_ISO_KEY)));
        assertThat(selections.getBeenAreas(), not(hasItem(CANADA_ISO_KEY)));
        assertThat(selections.getBeenAreas(), not(hasItem(CANADA_QC_ISO_KEY)));
        assertThat(selections.getWantAreas(), not(hasItem(BRAZIL_ISO_KEY)));
        assertThat(selections.getWantAreas(), not(hasItem(CANADA_ISO_KEY)));
        assertThat(selections.getWantAreas(), not(hasItem(CANADA_QC_ISO_KEY)));
    }

    @Test
    public void testSelectionOfAStateWithBeen() {
        testSelecionOfTheStateAsBeen(CANADA_QC_ISO_KEY, CANADA_ISO_KEY);
    }

    @Test
    public void testSelectionOfAUnitedKingdomStateWithBeen() {
        testSelecionOfTheStateAsBeen(UK_ENGLAND_ISO_KEY, UNITED_KINGDOM_ISO_KEY);
    }

    private void testSelecionOfTheStateAsBeen(String theStatesIsoKey, String theCountrysIsoKey) {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), theStatesIsoKey, SelectionType.BEEN);

        //then:
        assertThat(userSelections.getBeenAreas(), is(notNullValue()));
        assertThat(userSelections.getBeenAreas().size(), is(2)); //only the country and the state should appear
        assertThat(userSelections.getBeenAreas(), hasItem(theStatesIsoKey));
        assertThat(userSelections.getBeenAreas(), hasItem(theCountrysIsoKey));
    }

    @Test
    public void testSelectionOfAStateWithWant() {
        testSelectionOfTheStateAsWant(CANADA_AB_ISO_KEY, CANADA_ISO_KEY);
    }

    @Test
    public void testSelectionOfAUnitedKingdomStateWithWant() {
        testSelectionOfTheStateAsWant(UK_ENGLAND_ISO_KEY, UNITED_KINGDOM_ISO_KEY);
    }

    private void testSelectionOfTheStateAsWant(String theStatesIsoKey, String theCountrysIsoKey) {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), theStatesIsoKey, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), is(notNullValue()));
        assertThat(userSelections.getWantAreas().size(), is(2)); //only the country and the state should appear
        assertThat(userSelections.getWantAreas(), hasItem(theStatesIsoKey));
        assertThat(userSelections.getWantAreas(), hasItem(theCountrysIsoKey));
    }

    @Test
    public void testVisualizationOfTravelStatsInSignUpAndSigninResponse() {
        //given: new user created
        createNewUser(this.restTemplate, NEWUSER_EMAIL);

        //when: select
        selectAreaForTheUser(securedAPI, NEWUSER_EMAIL, CANADA_QC_ISO_KEY, SelectionType.LIVED);
        selectAreaForTheLoggedInUser(securedAPI,  ITALY_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI,  USA_KS_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI,  USA_TX_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI,  USA_NY_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI,  CANADA_ON_ISO_KEY, SelectionType.WANT);

        ///and signin with the new user again
        var tokenResponse = this.restTemplate.postForObject(SIGNIN_URL,
                new AuthRequest(NEWUSER_EMAIL, IOS_PLATFORM), TokenResponseWithStats.class);

        //then: the selections collections should reflect the new values
        assertThat(tokenResponse.token(), is(notNullValue()));
        assertThat(tokenResponse.selections(), is(notNullValue()));
        assertThat(tokenResponse.selections().getLivedAreas(), hasItem(CANADA_ISO_KEY)); //higher selection type for CA
        assertThat(tokenResponse.selections().getLivedAreas(), hasItem(CANADA_QC_ISO_KEY));
        assertThat(tokenResponse.selections().getLivedAreas().size(), is(2));

        assertThat(tokenResponse.selections().getBeenAreas(), hasItem(ITALY_ISO_KEY));
        assertThat(tokenResponse.selections().getBeenAreas(), hasItem(USA_ISO_KEY)); //higher selection type for USA
        assertThat(tokenResponse.selections().getBeenAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(tokenResponse.selections().getBeenAreas().size(), is(3));

        assertThat(tokenResponse.selections().getWantAreas(), hasItem(USA_TX_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas(), hasItem(USA_NY_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas(), hasItem(CANADA_ON_ISO_KEY));
        assertThat(tokenResponse.selections().getWantAreas().size(), is(3)); //no CA or US should be here
    }

    @Test
    public void testDeSelectionOfAState() {
        testDeSelectionOfThisState(USA_NY_ISO_KEY, USA_ISO_KEY);
    }

    @Test
    public void testDeSelectionOfAUnitedKingdomState() {
        testDeSelectionOfThisState(UK_ENGLAND_ISO_KEY, UNITED_KINGDOM_ISO_KEY);

    }

    private void testDeSelectionOfThisState(String theStatesIsoKey, String theCountrysIsoKey) {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), theStatesIsoKey, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), hasItem(theCountrysIsoKey));
        assertThat(userSelections.getWantAreas(), hasItem(theStatesIsoKey));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI, theStatesIsoKey, SelectionType.CLEAR);

        //then:
        assertThat(userSelections.getLivedAreas(), not(hasItem(theStatesIsoKey)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(theCountrysIsoKey)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(theStatesIsoKey)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(theCountrysIsoKey)));
        assertThat(userSelections.getWantAreas(), not(hasItem(theStatesIsoKey)));
        assertThat(userSelections.getWantAreas(), not(hasItem(theCountrysIsoKey)));
    }

    @Test
    public void testChangeSelectionsTypeOfAState() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_KS_ISO_KEY, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getWantAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY)));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, SelectionType.BEEN);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_ISO_KEY));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY)));

        //when:
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, SelectionType.CLEAR);

        //then:
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY)));
    }

    @Test
    public void testChangeSelectionsTypeOfAStateInACountryWithAnotherStateSelected() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_TX_ISO_KEY, SelectionType.WANT);

        //then : the country and the state should be marked as WANT
        assertThat(userSelections.getWantAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getWantAreas(), hasItem(USA_TX_ISO_KEY));

        //when: add another state selection to the same country
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, SelectionType.BEEN);

        //then: the country should be "upgraded" to BEEN
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY))); //shouldn't be here anymore
        assertThat(userSelections.getWantAreas(), hasItem(USA_TX_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_ISO_KEY)); //should be upgraded to BEEN...
        assertThat(userSelections.getBeenAreas(), hasItem(USA_KS_ISO_KEY)); //...along with the new state

        //when: we do the same changes with the LIVED type of selection
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_NY_ISO_KEY, SelectionType.LIVED);

        //then: the country should be "upgraded" again, this time to LIVED, and disappear from other listings
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY))); //shouldn't be here anymore
        assertThat(userSelections.getWantAreas(), hasItem(USA_TX_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY))); //shouldn't be here anymore as well
        assertThat(userSelections.getBeenAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), hasItem(USA_ISO_KEY)); //should be here now
        assertThat(userSelections.getLivedAreas(), hasItem(USA_NY_ISO_KEY)); //along with the new state

        //when: we clear the lived state ...
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_NY_ISO_KEY, SelectionType.CLEAR);

        //then: the country should be downgraded to the state with the "higher" type existent (BEEN)
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY))); //shouldn't be here
        assertThat(userSelections.getWantAreas(), hasItem(USA_TX_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_ISO_KEY)); //should be here, downgraded to BEEN
        assertThat(userSelections.getBeenAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY))); //should not exist anymore
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_NY_ISO_KEY))); //should not exist anymore

        //when: we clear the been state as well...
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_KS_ISO_KEY, SelectionType.CLEAR);

        //then: the country should be downgraded to the state with the "higher" type existent (WANT)
        assertThat(userSelections.getWantAreas(), hasItem(USA_ISO_KEY)); //should be here
        assertThat(userSelections.getWantAreas(), hasItem(USA_TX_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY))); //shouldn't be here anymore
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_KS_ISO_KEY)));//shouldn't exist anymore
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY))); //should not be here
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_NY_ISO_KEY))); //should not be here

        //when: finally, we clear the wanted state...
        userSelections = selectAreaForTheLoggedInUser(securedAPI, USA_TX_ISO_KEY, SelectionType.CLEAR);


        //then: the country and all states should be cleared (non existent anywhere)
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getWantAreas(), not(hasItem(USA_TX_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getBeenAreas(), not(hasItem(USA_KS_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_ISO_KEY)));
        assertThat(userSelections.getLivedAreas(), not(hasItem(USA_NY_ISO_KEY)));
    }

}
