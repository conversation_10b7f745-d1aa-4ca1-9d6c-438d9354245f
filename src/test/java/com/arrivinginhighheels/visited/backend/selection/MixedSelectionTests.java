package com.arrivinginhighheels.visited.backend.selection;

import com.arrivinginhighheels.visited.backend.config.Constants;
import com.arrivinginhighheels.visited.backend.dto.MultipleSelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.CANADA_QC_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.USA_KS_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.*;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests changes of selections going from country to state and vice-versa
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class MixedSelectionTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testAChangeOfLivedFromACountryToAState() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when: select a country as lived
        SelectionsDTO selectionsDTO = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), ITALY_ISO_KEY,
                SelectionType.LIVED);

        //then: the selections collections should reflect the new values
        assertThat(selectionsDTO.getLivedAreas(), hasItem(ITALY_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas().size(), is(1));

        //when: changing to a state
        selectionsDTO = selectAreaForTheLoggedInUser(securedAPI, CANADA_QC_ISO_KEY, SelectionType.LIVED);

        //then: Italy should not show in the lived collection
        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(ITALY_ISO_KEY))); //should disappear
        assertThat(selectionsDTO.getLivedAreas(), hasItem(CANADA_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas(), hasItem(CANADA_QC_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas().size(), is(2));
    }

    @Test
    public void testAChangeOfLivedFromAStateToACountry() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when: select a state as lived
        SelectionsDTO selectionsDTO = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), CANADA_QC_ISO_KEY,
                SelectionType.LIVED);

        //then: the selections collections should reflect the new values
        assertThat(selectionsDTO.getLivedAreas(), hasItem(CANADA_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas(), hasItem(CANADA_QC_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas().size(), is(2));

        //when: changing to a country with no subdivision
        selectionsDTO = selectAreaForTheLoggedInUser(securedAPI, ITALY_ISO_KEY, SelectionType.LIVED);

        //then: Canada should no be in the lived collection anymore. Only Italy should appear
        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(CANADA_ISO_KEY)));
        assertThat(selectionsDTO.getLivedAreas(), not(hasItem(CANADA_QC_ISO_KEY)));
        assertThat(selectionsDTO.getLivedAreas(), hasItem(ITALY_ISO_KEY));
        assertThat(selectionsDTO.getLivedAreas().size(), is(1));
    }

    @Test
    public void testSelectionOfStateAsBeenOfAPreexistingCountryMarkedAsLived() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when: select a country that has subdivisions as lived
        SelectionsDTO selectionsDTO = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_KS_ISO_KEY, SelectionType.BEEN);

        //then: the selections collections should reflect the new values
        assertThat(selectionsDTO.getBeenAreas(), hasItem(USA_ISO_KEY));
        assertThat(selectionsDTO.getBeenAreas(), hasItem(USA_KS_ISO_KEY));

        //when: Selecting a subdivision of USA as been
        selectionsDTO = selectAreaForTheLoggedInUser(securedAPI, USA_ISO_KEY,
                SelectionType.LIVED);

        //then: USA should be lived, and KS should be been
        assertThat(selectionsDTO.getLivedAreas(), hasItem(USA_ISO_KEY));
        assertThat(selectionsDTO.getBeenAreas(), hasItem(USA_KS_ISO_KEY));
    }

    @Test
    public void testMultipleSelections() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:

        MultipleSelectionDTO multipleSelectionDTO = new MultipleSelectionDTO(
                Stream.of(
                        new SelectionDTO(USA_KS_ISO_KEY, SelectionType.BEEN),
                        new SelectionDTO(BRAZIL_ISO_KEY, SelectionType.LIVED),
                        new SelectionDTO(CANADA_QC_ISO_KEY, SelectionType.WANT)
                ).collect(Collectors.toList())
        );

        ResponseEntity<SelectionsDTO> responseEntity =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .post(Constants.Routes.SELECT_MULTIPLE_URL, multipleSelectionDTO, SelectionsDTO.class);

        SelectionsDTO userSelections = responseEntity.getBody();

        //then:
        assertThat(userSelections.getWantAreas().size(), is(2));
        assertThat(userSelections.getWantAreas(), hasItem(CANADA_QC_ISO_KEY));
        assertThat(userSelections.getWantAreas(), hasItem(CANADA_ISO_KEY));

        assertThat(userSelections.getLivedAreas().size(), is(1));
        assertThat(userSelections.getLivedAreas(), hasItem(BRAZIL_ISO_KEY));

        assertThat(userSelections.getBeenAreas().size(), is(2));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_KS_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_ISO_KEY));
    }
}
