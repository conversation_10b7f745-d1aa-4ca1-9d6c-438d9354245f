package com.arrivinginhighheels.visited.backend.selection;

import com.arrivinginhighheels.visited.backend.dto.SelectionLogDTO;
import com.arrivinginhighheels.visited.backend.dto.SelectionLogsDTO;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.GET_USER_SELECTION_LOGS_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.CANADA_AB_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.USA_KS_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.ADMIN_USER;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.*;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests that assert the selection history (logging) behaviour.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SelectionLogsTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testThatUsersLogRetrievalIsPossibleToAnAdminUserOnly() {
        ResponseEntity<SelectionLogsDTO> response =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(GET_USER_SELECTION_LOGS_URL.replace("{username}", REGULAR_USER.getEmail()), SelectionLogsDTO.class);

        assertThat(response.getStatusCode(), is(HttpStatus.FORBIDDEN));
    }

    @Test
    public void testSimpleLogCreationForSelectionOfCountry() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());
        clearUserLogs(securedAPI, REGULAR_USER.getEmail());

        //when: user makes a simple selection of a country
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), ITALY_ISO_KEY, SelectionType.BEEN);

        //and: admin gets the logs for the user
        ResponseEntity<SelectionLogsDTO> response = getLogsForTheRegularUser();

        //then: verifies the logs for the new selection

        SelectionLogsDTO logs = response.getBody();
        assertThat(logs, is(notNullValue()));
        assertThat(logs.getUserName(), is(REGULAR_USER.getEmail()));
        assertThat(logs.getHistory(), is(notNullValue()));
        assertThat(logs.getHistory().size(), is(1)); // only one entry in the history should exist

        SelectionLogDTO log = logs.getHistory().get(0);
        assertThat(log.getGeoAreaIsoKey(), is(ITALY_ISO_KEY));
        assertThat(log.getType(), is(SelectionType.BEEN));
        assertThat(log.getTimestamp(), is(notNullValue()));
    }

    @Test
    public void testSimpleLogCreationForSelectionOfState() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());
        clearUserLogs(securedAPI, REGULAR_USER.getEmail());

        //when: user makes a simple selection of a country
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_KS_ISO_KEY, SelectionType.BEEN);

        //and: admin gets the logs for the user
        ResponseEntity<SelectionLogsDTO> response = getLogsForTheRegularUser();

        //then: verifies the logs for the new selection

        SelectionLogsDTO logs = response.getBody();
        assertThat(logs, is(notNullValue()));
        assertThat(logs.getHistory(), is(notNullValue()));
        assertThat(logs.getHistory().size(), is(2)); // two entries in the history should exist (one for the country, other for the state)

        boolean isAllLogsBeen =
                logs.getHistory()
                        .stream()
                        .allMatch(log -> log.getType() == SelectionType.BEEN);

        boolean isAllTimestamped =
                logs.getHistory()
                        .stream()
                        .allMatch(log -> log.getTimestamp() != null);

        boolean areUSAandKSISOKeysPresentOnly =
                logs.getHistory()
                        .stream()
                        .noneMatch(log -> !log.getGeoAreaIsoKey().equals(USA_ISO_KEY) &&
                                          !log.getGeoAreaIsoKey().equals(USA_KS_ISO_KEY));

        boolean isUSAPresent = logs.getHistory().stream()
                                                .anyMatch(log -> log.getGeoAreaIsoKey().equals(USA_ISO_KEY));
        boolean isKSPresent = logs.getHistory().stream()
                                               .anyMatch(log -> log.getGeoAreaIsoKey().equals(USA_KS_ISO_KEY));

        assertThat(isAllLogsBeen, is(true));
        assertThat(isAllTimestamped, is(true));
        assertThat(areUSAandKSISOKeysPresentOnly && isUSAPresent && isKSPresent, is(true));
    }

    @Test
    public void testLoggingOfClearingASelectionOfCountry() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());
        clearUserLogs(securedAPI, REGULAR_USER.getEmail());

        //when: user makes a simple selection of a country
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), MADAGASCAR_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI, MADAGASCAR_ISO_KEY, SelectionType.CLEAR);

        //and: admin gets the logs for the user
        ResponseEntity<SelectionLogsDTO> response = getLogsForTheRegularUser();

        //then: verifies the logs for the new selection
        SelectionLogsDTO logs = response.getBody();
        assertThat(logs, is(notNullValue()));
        assertThat(logs.getHistory(), is(notNullValue()));
        assertThat(logs.getHistory().size(), is(2)); // two entries in the history should exist (one for the WANT, other for the CLEAR)

        boolean areAllLogsForMadagascar =
                logs.getHistory()
                        .stream()
                        .allMatch(log -> log.getGeoAreaIsoKey().equals(MADAGASCAR_ISO_KEY));


        assertThat(areAllLogsForMadagascar, is(true));

        //and: verifies if the first log is for a WANT selection, and the second (made later) is for a CLEAR
        //     testing the ordering of the records as well (older to newer selection timestamps)
        SelectionLogDTO wantLog = logs.getHistory().get(0);
        SelectionLogDTO clearLog = logs.getHistory().get(1);

        assertThat(wantLog.getType(), is(SelectionType.WANT));
        assertThat(clearLog.getType(), is(SelectionType.CLEAR));
        assertThat(clearLog.getTimestamp().compareTo(wantLog.getTimestamp()) > 0, is(true)); //order of the timestamps
    }

    @Test
    public void testLoggingOfClearingASelectionOfState() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());
        clearUserLogs(securedAPI, REGULAR_USER.getEmail());

        //when: user makes a simple selection of a country
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), CANADA_AB_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI, CANADA_AB_ISO_KEY, SelectionType.CLEAR);

        //and: admin gets the logs for the user
        ResponseEntity<SelectionLogsDTO> response = getLogsForTheRegularUser();

        //then: verifies the logs for the new selection
        SelectionLogsDTO logs = response.getBody();
        assertThat(logs, is(notNullValue()));
        assertThat(logs.getHistory(), is(notNullValue()));
        assertThat(logs.getHistory().size(), is(4)); // two entries in the history should exist (one for the WANT, other for the CLEAR)

        //and: verifies if the first logs is for a WANT selection, and the last ones (made later) are for a CLEAR
        //     testing the ordering of the records as well (older to newer selection timestamps)
        SelectionLogDTO wantStateLog = logs.getHistory().get(0);
        SelectionLogDTO wantCountryLog = logs.getHistory().get(1);
        SelectionLogDTO clearStateLog = logs.getHistory().get(2);
        SelectionLogDTO clearCountryLog = logs.getHistory().get(3);

        assertThat(wantStateLog.getType(), is(SelectionType.WANT));
        assertThat(wantCountryLog.getType(), is(SelectionType.WANT));
        assertThat(clearStateLog.getType(), is(SelectionType.CLEAR));
        assertThat(clearCountryLog.getType(), is(SelectionType.CLEAR));
        assertThat(clearStateLog.getTimestamp().compareTo(wantStateLog.getTimestamp()) > 0, is(true)); //order of the timestamps
        assertThat(clearCountryLog.getTimestamp().compareTo(wantCountryLog.getTimestamp()) > 0, is(true)); //order of the timestamps
    }


    @Test
    public void testClearingOrderOfACountryResetedBySelectionOtherAsLivedInItsPlace() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());
        clearUserLogs(securedAPI, REGULAR_USER.getEmail());

        //when: user makes a simple selection of a country
        selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), BRAZIL_ISO_KEY, SelectionType.LIVED);
        selectAreaForTheLoggedInUser(securedAPI, MADAGASCAR_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI, ITALY_ISO_KEY, SelectionType.LIVED);

        //and: admin gets the logs for the user
        ResponseEntity<SelectionLogsDTO> response = getLogsForTheRegularUser();

        //then: verifies the logs for the new selection
        SelectionLogsDTO logs = response.getBody();
        assertThat(logs, is(notNullValue()));
        assertThat(logs.getHistory(), is(notNullValue()));
        assertThat(logs.getHistory().size(), is(4));

        //and: verifies the data and the ordering of the records
        SelectionLogDTO brazilAsLivedLog = logs.getHistory().get(0);
        SelectionLogDTO madagascarAsBeenLog = logs.getHistory().get(1);
        SelectionLogDTO brazilAsClearLog = logs.getHistory().get(2);
        SelectionLogDTO italyAsLivedLog = logs.getHistory().get(3);

        //data
        assertThat(brazilAsLivedLog.getGeoAreaIsoKey(), is(BRAZIL_ISO_KEY));
        assertThat(brazilAsLivedLog.getType(), is(SelectionType.LIVED));
        assertThat(madagascarAsBeenLog.getGeoAreaIsoKey(), is(MADAGASCAR_ISO_KEY));
        assertThat(madagascarAsBeenLog.getType(), is(SelectionType.BEEN));
        assertThat(brazilAsClearLog.getGeoAreaIsoKey(), is(BRAZIL_ISO_KEY));
        assertThat(brazilAsClearLog.getType(), is(SelectionType.CLEAR));
        assertThat(italyAsLivedLog.getGeoAreaIsoKey(), is(ITALY_ISO_KEY));
        assertThat(italyAsLivedLog.getType(), is(SelectionType.LIVED));

        //order of the timestamps
        assertThat(brazilAsLivedLog.getTimestamp().compareTo(madagascarAsBeenLog.getTimestamp()) <= 0, is(true));
        assertThat(madagascarAsBeenLog.getTimestamp().compareTo(brazilAsClearLog.getTimestamp()) <= 0, is(true));
        assertThat(brazilAsClearLog.getTimestamp().compareTo(italyAsLivedLog.getTimestamp()) <= 0, is(true));
    }

    private ResponseEntity<SelectionLogsDTO> getLogsForTheRegularUser() {
        ResponseEntity<SelectionLogsDTO> response = securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                .get(GET_USER_SELECTION_LOGS_URL.replace("{username}", REGULAR_USER.getEmail()), SelectionLogsDTO.class);

        assertThat(response.getStatusCode(), is(HttpStatus.OK));
        return response;
    }
}
