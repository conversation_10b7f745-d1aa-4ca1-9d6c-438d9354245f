package com.arrivinginhighheels.visited.backend.selection;

import com.arrivinginhighheels.visited.backend.dto.SelectionsDTO;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Cities.USA_NY_NEW_YORK_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.USA_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.USA_NY_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.clearUserSelections;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.selectAreaForTheUser;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SelectCitiesTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testSelectingACityAsLived() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_NY_NEW_YORK_ISO_KEY, SelectionType.LIVED);

        //then:
        assertThat(userSelections.getLivedAreas(), is(notNullValue()));
        assertThat(userSelections.getLivedAreas().size(), is(3)); //The country and the state also should be there
        assertThat(userSelections.getLivedAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), hasItem(USA_NY_ISO_KEY));
        assertThat(userSelections.getLivedAreas(), hasItem(USA_NY_NEW_YORK_ISO_KEY));
    }

    @Test
    public void testSelectingACityAsBeen() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_NY_NEW_YORK_ISO_KEY, SelectionType.BEEN);

        //then:
        assertThat(userSelections.getBeenAreas(), is(notNullValue()));
        assertThat(userSelections.getBeenAreas().size(), is(3)); //The country and the state should also be there
        assertThat(userSelections.getBeenAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_NY_ISO_KEY));
        assertThat(userSelections.getBeenAreas(), hasItem(USA_NY_NEW_YORK_ISO_KEY));
    }

    @Test
    public void testSelectingACityAsWant() {
        //given:
        clearUserSelections(securedAPI, REGULAR_USER.getEmail());

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_NY_NEW_YORK_ISO_KEY, SelectionType.WANT);

        //then:
        assertThat(userSelections.getWantAreas(), is(notNullValue()));
        assertThat(userSelections.getWantAreas().size(), is(3)); //The country and the state should also be there
        assertThat(userSelections.getWantAreas(), hasItem(USA_ISO_KEY));
        assertThat(userSelections.getWantAreas(), hasItem(USA_NY_ISO_KEY));
        assertThat(userSelections.getWantAreas(), hasItem(USA_NY_NEW_YORK_ISO_KEY));
    }

    @Test
    public void testClearingSelectionOnAWantedCity() {
        testSelectingACityAsWant();

        //when:
        SelectionsDTO userSelections = selectAreaForTheUser(securedAPI, REGULAR_USER.getEmail(), USA_NY_NEW_YORK_ISO_KEY, SelectionType.CLEAR);

        //then:
        assertThat(userSelections.getWantAreas(), is(notNullValue()));
        assertThat(userSelections.getWantAreas().size(), is(0)); //The country and the state should also not be there
    }

}
