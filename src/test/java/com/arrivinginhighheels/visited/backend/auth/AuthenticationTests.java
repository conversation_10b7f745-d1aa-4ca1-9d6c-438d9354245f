package com.arrivinginhighheels.visited.backend.auth;

import com.arrivinginhighheels.visited.backend.dto.UserSessionsDTO;
import com.arrivinginhighheels.visited.backend.model.AuthorityName;
import com.arrivinginhighheels.visited.backend.model.SelectionType;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.ITALY_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.Countries.MADAGASCAR_ISO_KEY;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.GeoAreasIsoKeys.States.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.ADMIN_USER;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.selectAreaForTheLoggedInUser;
import static com.arrivinginhighheels.visited.backend.utils.SelectionUtils.selectAreaForTheUser;
import static org.hamcrest.CoreMatchers.*;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the Authentication with JWT
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AuthenticationTests {

    public static final String USER_THAT_WILL_DELETE_ITSELF_EMAIL = "<EMAIL>";

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    /**
     * Test that a not logged in (no JWT in the headers) request to "/" is rejected with a 401 HTTP Status.
     */
    @Test
    public void testIfSecurityIsBlockingUnauthorizedRequests() {
        //when:
        ResponseEntity respEntity = this.restTemplate.getForEntity("/", String.class);

        //then:
        assertThat(respEntity.getStatusCode(), is(HttpStatus.UNAUTHORIZED));
    }

    /**
     * Perform a successful login.
     */
    @Test
    public void loginSuccessfulTest() {
        //when:
        var tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(REGULAR_USER.getEmail(), IOS_PLATFORM),
                        TokenResponseWithStats.class);

        //then:
        assertThat(tokenResponse.token(), is(notNullValue()));

        var user = tokenResponse.user();
        assertUserInfo(user);
    }

    /**
     * Tests the admin login.
     */
    @Test
    public void adminLoginSuccessfulTest() {
        //when:
        var tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(ADMIN_USER.getEmail(), IOS_PLATFORM),
                        TokenResponseWithStats.class);

        //then:
        assertThat(tokenResponse.token(), is(notNullValue()));

        UserDTO user = tokenResponse.user();
        assertThat(user, is(notNullValue()));
        assertThat(user.getEmail(), is(ADMIN_USER.getEmail()));
        assertThat(user.getAuthorities(), is(notNullValue()));
        assertThat(user.getAuthorities().size(), is(2)); //two roles: user and admin
        assertThat(user.getAuthorities(), hasItem(AuthorityName.ROLE_USER));
        assertThat(user.getAuthorities(), hasItem(AuthorityName.ROLE_ADMIN));
    }

    /**
     * Test if, with a invalid login info, the API rejects the login attempt.
     */
    @Test
    public void failedLoginTest() {
        //when:
        var ent =
                this.restTemplate.postForEntity(SIGNIN_URL,
                        new AuthRequest("<EMAIL>", IOS_PLATFORM),
                        TokenResponseWithStats.class);

        //then:
        assertThat(ent.getStatusCode(), is(HttpStatus.UNAUTHORIZED));
    }

    /**
     * Test if, without informing the platform info, the API rejects the login request.
     */
    @Test
    public void failedLoginByBadRequestWithoutPlatformTest() {
        //when:
        var ent =
                this.restTemplate.postForEntity(SIGNIN_URL,
                        new AuthRequest("<EMAIL>", null),
                        TokenResponseWithStats.class);

        //then:
        assertThat(ent.getStatusCode(), is(HttpStatus.BAD_REQUEST));
    }

    /**
     * Test if, without informing the email, the API rejects the login request.
     */
    @Test
    public void failedLoginByBadRequestWithoutEmailTest() {
        //when:
        var ent =
                this.restTemplate.postForEntity(SIGNIN_URL,
                        new AuthRequest(null, IOS_PLATFORM),
                        TokenResponseWithStats.class);

        //then:
        assertThat(ent.getStatusCode(), is(HttpStatus.BAD_REQUEST));
    }

    /**
     * Test if, with a logged in user, the secured methods become available.
     */
    @Test
    public void testAuthorizedRequest() {
        //when
        ResponseEntity<UserDTO> resp =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                          .get(USERS_ME_URL, UserDTO.class);

        UserDTO user = resp.getBody();

        //then:
        assertUserInfo(user);
    }

    @Test
    public void testDeletionOfAUserAsRequestedByTheUserHimself() {
        //given: a newly created user
        var userDTO = new AuthRequest(USER_THAT_WILL_DELETE_ITSELF_EMAIL, IOS_PLATFORM);
        ResponseEntity<TokenResponseWithStats> tokenResp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);
        assertThat(tokenResp.getStatusCode(), is(HttpStatus.CREATED));

        Date firstCreationDate = tokenResp.getBody().user().getCreationDate();

        //and a few areas seleted for him
        selectAreaForTheUser(securedAPI, USER_THAT_WILL_DELETE_ITSELF_EMAIL, UK_ENGLAND_ISO_KEY, SelectionType.LIVED);
        selectAreaForTheLoggedInUser(securedAPI, CANADA_QC_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI, USA_NY_ISO_KEY, SelectionType.BEEN);
        selectAreaForTheLoggedInUser(securedAPI, CANADA_AB_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI, MADAGASCAR_ISO_KEY, SelectionType.WANT);
        selectAreaForTheLoggedInUser(securedAPI, ITALY_ISO_KEY, SelectionType.WANT);

        //when: the user request its own deletion:
        ResponseEntity<Boolean> deleteMeResponse =
                securedAPI.withLoggedUser()
                          .delete(USERS_ME_URL, Boolean.class);
        assertThat(deleteMeResponse.getStatusCode(), is(HttpStatus.ACCEPTED));
        assertThat(deleteMeResponse.getBody(), is(Boolean.TRUE));

        //then: the user cannot access any available method or login
        ResponseEntity<UserDTO> userResponse =
                securedAPI.withLoggedUser()
                          .get(USERS_ME_URL, UserDTO.class);
        assertThat(userResponse.getStatusCode(), is(HttpStatus.UNAUTHORIZED)); //the user got signed out

        ResponseEntity<TokenResponseWithStats> loginResponse =
                this.restTemplate.postForEntity(SIGNIN_URL,
                        new AuthRequest(USER_THAT_WILL_DELETE_ITSELF_EMAIL, IOS_PLATFORM),
                        TokenResponseWithStats.class);
        assertThat(loginResponse.getStatusCode(), is(HttpStatus.UNAUTHORIZED)); //the user cannot login with the deleted e-mail

        // and check that all the information of the user is gone
        ResponseEntity<UserSessionsDTO> userSessionsResp =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM)
                          .get(GET_USER_SESSIONS_URL.replace("{username}", USER_THAT_WILL_DELETE_ITSELF_EMAIL),
                                  UserSessionsDTO.class);
        assertThat(userSessionsResp.getStatusCode(), is(HttpStatus.NOT_FOUND));

        //when: I request the re-creation of the deleted user
        userDTO = new AuthRequest(USER_THAT_WILL_DELETE_ITSELF_EMAIL, IOS_PLATFORM);
        tokenResp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);
        assertThat(tokenResp.getStatusCode(), is(HttpStatus.CREATED));

        //then: all the information is as it was a newly created user
        UserDTO user = tokenResp.getBody().user();
        assertThat(user.getCreationDate(), is(notNullValue()));
        assertThat(user.getCreationDate(), is(not(firstCreationDate)));
        assertThat(user.getEmail(), is(USER_THAT_WILL_DELETE_ITSELF_EMAIL));
        assertThat(tokenResp.getBody().selections().getLivedAreas().isEmpty(), is(true));
        assertThat(tokenResp.getBody().selections().getWantAreas().isEmpty(), is(true));
        assertThat(tokenResp.getBody().selections().getBeenAreas().isEmpty(), is(true));
    }

    /**
     * Asserts a regular user's (username = 'user') information.
     *
     * @param user
     */
    private void assertUserInfo(UserDTO user) {
        assertThat(user, is(notNullValue()));
        assertThat(user.getEmail(), is(REGULAR_USER.getEmail()));
        assertThat(user.getAuthorities(), is(notNullValue()));
        assertThat(user.getAuthorities().size(), is(1)); //one role only
        assertThat(user.getAuthorities().get(0), is(AuthorityName.ROLE_USER)); //user auth - not admin
    }

}
