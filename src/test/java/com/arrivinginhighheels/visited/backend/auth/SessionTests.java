package com.arrivinginhighheels.visited.backend.auth;

import com.arrivinginhighheels.visited.backend.dto.UserSessionsDTO;
import com.arrivinginhighheels.visited.backend.model.Session;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.*;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.ADMIN_USER;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.REGULAR_USER;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the session recording behaviour of the API
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SessionTests {

    public static final String NEW_USER_EMAIL = "<EMAIL>";

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    @Test
    public void testThatRegularUserCantSeeSessionData() {
        //when: User tries to see session informations for any user
        ResponseEntity<UserSessionsDTO> response =
                securedAPI.withCredentials(REGULAR_USER.getEmail(), IOS_PLATFORM)
                        .get(GET_USER_SESSIONS_URL.replace("{username}", REGULAR_USER.getEmail()), UserSessionsDTO.class);

        //then: show return a FORBIDDEN status
        assertThat(response.getStatusCode(), is(HttpStatus.FORBIDDEN));
    }

    @Test
    public void testSessionCreationOnSignin() {
        //given: the number of sessions for the regular user had before he signs in
        ResponseEntity<UserSessionsDTO> response =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), IOS_PLATFORM) //signed in with Admin user
                          .get(GET_USER_SESSIONS_URL.replace("{username}", REGULAR_USER.getEmail()), UserSessionsDTO.class);

        final int numberOfSessionsPriorToTheSignin = response.getBody().getSessions().size();

        //when: the regular user signs in the API and gets a new token
        var tokenResponse =
                this.restTemplate.postForObject(SIGNIN_URL,
                        new AuthRequest(REGULAR_USER.getEmail(), ANDROID_PLATFORM),
                        TokenResponseWithStats.class);

        //then:
        assertThat(tokenResponse.token(), is(notNullValue()));

        //given: the token for the regular user
        final String token = tokenResponse.token();

        //when: admin checks the new sessions data for the regular user
        response = securedAPI.withLoggedUser() //already signed in with Admin user
                             .get(GET_USER_SESSIONS_URL.replace("{username}", REGULAR_USER.getEmail()), UserSessionsDTO.class);

        List<Session> sessions = response.getBody().getSessions();
        Session lastSession = sessions.get(sessions.size() - 1); //last session in the collection

        //then:
        assertThat(sessions.size(), is(numberOfSessionsPriorToTheSignin + 1));
        assertThat(lastSession.getToken(), is(token));
        assertThat(lastSession.getPlatform(), is(ANDROID_PLATFORM));
        assertThat(lastSession.getStartDateTime(), is(notNullValue()));
    }

    @Test
    public void testSessionCreationOnSignup() {
        //given: a new user
        var userDTO = new AuthRequest(NEW_USER_EMAIL, IOS_PLATFORM);

        //when: the new user sign up...
        var resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);
        var tokenResponse = resp.getBody();

        //then:
        assertThat(resp.getStatusCode(), is(HttpStatus.CREATED));
        assertThat(tokenResponse.token(), is(notNullValue()));

        //when: Admin user gets the session for the recently created user
        ResponseEntity<UserSessionsDTO> response =
                securedAPI.withCredentials(ADMIN_USER.getEmail(), WEB_PLATFORM) //signed in with Admin user
                        .get(GET_USER_SESSIONS_URL.replace("{username}", NEW_USER_EMAIL), UserSessionsDTO.class);

        List<Session> sessions = response.getBody().getSessions();

        //then:
        assertThat(sessions, is(notNullValue()));
        assertThat(sessions.size(), is(1)); //only one session should exists

        Session session = sessions.get(0);
        assertThat(session.getToken(), is(tokenResponse.token()));
        assertThat(session.getPlatform(), is(IOS_PLATFORM));
        assertThat(session.getStartDateTime(), is(notNullValue()));
    }
}
