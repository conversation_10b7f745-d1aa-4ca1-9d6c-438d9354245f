package com.arrivinginhighheels.visited.backend.auth;

import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.model.AuthorityName;
import com.arrivinginhighheels.visited.backend.security.dto.TokenResponseWithStats;
import com.arrivinginhighheels.visited.backend.security.dto.UserDTO;
import com.arrivinginhighheels.visited.backend.utils.SecureAPIRequestBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.SIGNUP_URL;
import static com.arrivinginhighheels.visited.backend.config.Constants.Routes.USERS_ME_URL;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Platforms.IOS_PLATFORM;
import static com.arrivinginhighheels.visited.backend.domain.TestConstants.Users.NEW_USER;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * Tests the creation of users
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SignUpTests {

    @Autowired
    private TestRestTemplate restTemplate;

    private SecureAPIRequestBuilder securedAPI;

    @Before
    public void setup() {
        this.securedAPI = new SecureAPIRequestBuilder(restTemplate);
    }

    /**
     * Tests the creation of a new user, and if after the signup a token is returned with the users information.
     */
    @Test
    public void createANewUser() {
        //given:
        var userDTO = new AuthRequest(NEW_USER.getEmail(), IOS_PLATFORM);

        //when:
        var resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);

        //then:
        assertThat(resp.getStatusCode(), is(HttpStatus.CREATED));

        var tokenResponse = resp.getBody();

        assertThat(tokenResponse.token(), is(notNullValue()));

        var user = tokenResponse.user();
        assertThat(user, is(notNullValue()));
        assertThat(user.getCreationDate(), is(notNullValue()));
        assertThat(user.getEmail(), is(NEW_USER.getEmail()));
        assertThat(user.getAuthorities(), is(notNullValue()));
        assertThat(user.getAuthorities().size(), is(1)); //one role only
        assertThat(user.getAuthorities().get(0), is(AuthorityName.ROLE_USER)); //user auth - not admin

        //when: I use the recently created user to access a secured resource
        var respLogin = securedAPI.withCredentials(NEW_USER.getEmail(), IOS_PLATFORM)
                                                      .get(USERS_ME_URL, UserDTO.class);

        //then:
        assertThat(respLogin.getStatusCode(), is(HttpStatus.OK));
    }

    @Test
    public void failToCreateANewUserDueToExistingEmail() {
        //given:
        var userDTO = new AuthRequest("<EMAIL>", IOS_PLATFORM);

        //when:
        ResponseEntity<String> responseEntity =
                restTemplate.postForEntity(SIGNUP_URL, userDTO, String.class);

        //then:
        assertThat(responseEntity.getStatusCode(), is(HttpStatus.CONFLICT));
    }

    @Test
    public void failToCreateANewUserDueToInvalidEmail() {
        //given:
        var userDTO = new AuthRequest("a", IOS_PLATFORM);

        //when:
        var responseEntity =
                restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);

        //then:
        assertThat(responseEntity.getStatusCode(), is(HttpStatus.BAD_REQUEST));
    }

//    @Test
//    public void failToCreateANewUserDueToNullEmail() {
//        //given:
//        NewUserDTO userDTO = new NewUserDTO(null, IOS_PLATFORM);
//
//        //when:
//        ResponseEntity<TokenResponse> responseEntity =
//                restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponse.class);
//
//        //then:
//        assertThat(responseEntity.getStatusCode(), is(HttpStatus.BAD_REQUEST));
//    }

    @Test
    public void failToCreateANewUserWithoutInformingThePlatform() {
        //given:
        var userDTO = new AuthRequest("<EMAIL>", null);

        //when:
        var responseEntity =
                restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);

        //then:
        assertThat(responseEntity.getStatusCode(), is(HttpStatus.BAD_REQUEST));
    }

    @Test
    public void caseSensitivityIsIgnoredWhenCreatingANewUser() {
        //given:
        var userDTO = new AuthRequest("<EMAIL>", IOS_PLATFORM);

        //when:
        var resp = restTemplate.postForEntity(SIGNUP_URL, userDTO, TokenResponseWithStats.class);

        //then:
        var tokenResponse = resp.getBody();
        UserDTO user = tokenResponse.user();
        assertThat(user.getEmail(), is("<EMAIL>"));
    }
}
