# This is a sample build configuration for Maven.
# Check our guides at https://confluence.atlassian.com/x/VYk8Lw for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: maven:3.9.3-amazoncorretto-17

pipelines:
#  default:
#    - step:
#        name: Build and test
#        script:
#          - echo "Initializing the build-test"
#          - mvn clean install
  branches:
    master:
      - step:
          name: Build, test and release
          script: # Modify the commands below to build your repository.
            - echo "Initializing the build-release"
            - mvn clean -e
            - cp ext/settings.xml ~/.m2/settings.xml
            - mvn install deploy -e
